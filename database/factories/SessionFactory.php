<?php

namespace Database\Factories;

use App\Models\Session;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Session>
 */
class SessionFactory extends Factory
{
    protected $model = Session::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startTime = $this->faker->dateTimeBetween('-7 days', '+7 days');
        $endTime = $this->faker->dateTimeBetween($startTime, '+14 days');

        return [
            'session_id' => $this->faker->numerify('###################'), // 19 digit number
            'campaign_id' => $this->faker->numerify('###################'), // Will be overridden when used with specific campaign
            'name' => $this->faker->words(3, true) . ' Session',
            'status' => $this->faker->randomElement(['draft', 'active', 'paused', 'completed']),
            'delivery_type' => $this->faker->randomElement(['standard', 'accelerated', 'scheduled']),
            'budget' => $this->faker->randomFloat(2, 50, 5000),
            'start_time' => $startTime,
            'end_time' => $endTime,
        ];
    }

    /**
     * Indicate that the session is active.
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the session is running (active and within time range).
     */
    public function running(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'active',
            'start_time' => $this->faker->dateTimeBetween('-2 days', 'now'),
            'end_time' => $this->faker->dateTimeBetween('now', '+7 days'),
        ]);
    }

    /**
     * Set specific budget.
     */
    public function withBudget(float $budget): static
    {
        return $this->state(fn(array $attributes) => [
            'budget' => $budget,
        ]);
    }

    /**
     * Set specific delivery type.
     */
    public function withDeliveryType(string $deliveryType): static
    {
        return $this->state(fn(array $attributes) => [
            'delivery_type' => $deliveryType,
        ]);
    }

    /**
     * Set specific time range.
     */
    public function withTimeRange(\DateTime $startTime, \DateTime $endTime): static
    {
        return $this->state(fn(array $attributes) => [
            'start_time' => $startTime,
            'end_time' => $endTime,
        ]);
    }
}
