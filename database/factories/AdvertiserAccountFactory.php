<?php

namespace Database\Factories;

use App\Models\AdvertiserAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AdvertiserAccount>
 */
class AdvertiserAccountFactory extends Factory
{
    protected $model = AdvertiserAccount::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'advertiser_id' => $this->faker->numerify('###################'),
            'advertiser_name' => $this->faker->company() . ' Ads',
            'status' => $this->faker->randomElement(['active', 'inactive', 'suspended', 'pending']),
            'currency' => $this->faker->randomElement(['USD', 'VND', 'THB', 'MYR', 'SGD']),
            'timezone' => $this->faker->randomElement(['Asia/Ho_Chi_Minh', 'Asia/Bangkok', 'Asia/Singapore']),
            'company_name' => $this->faker->company(),
            'industry' => $this->faker->randomElement(['E-commerce', 'Fashion', 'Electronics', 'Food & Beverage']),
            'permissions' => ['ADVERTISER_READ', 'ADVERTISER_WRITE'],
            'balance' => $this->faker->randomFloat(2, 0, 10000),
            'authorized_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Indicate that the account is active.
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Set specific currency.
     */
    public function withCurrency(string $currency): static
    {
        return $this->state(fn(array $attributes) => [
            'currency' => $currency,
        ]);
    }

    /**
     * Set specific balance.
     */
    public function withBalance(float $balance): static
    {
        return $this->state(fn(array $attributes) => [
            'balance' => $balance,
        ]);
    }
}
