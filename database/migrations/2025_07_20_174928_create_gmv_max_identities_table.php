<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gmv_max_identities', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('identity_id')->unique()->comment('Identity ID từ TikTok API');
            $table->string('name')->comment('Tên identity');
            $table->enum('type', ['creator', 'brand', 'agency', 'other'])->comment('Loại identity');
            $table->enum('status', ['active', 'inactive', 'pending', 'suspended'])->default('pending')->comment('Trạng thái identity');
            $table->string('advertiser_id')->comment('Advertiser ID liên kết');

            // Timestamps
            $table->timestamps();

            // Indexes cho performance
            $table->index(['status', 'type']);
            $table->index('advertiser_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gmv_max_identities');
    }
};
