<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Use raw SQL to modify enum column
        DB::statement("ALTER TABLE tiktok_shops MODIFY COLUMN exclusive_authorization_status ENUM('none', 'pending', 'granted', 'expired', 'revoked', 'suspended') DEFAULT 'none' COMMENT 'Trạng thái ủy quyền độc quyền'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to original enum values using raw SQL
        DB::statement("ALTER TABLE tiktok_shops MODIFY COLUMN exclusive_authorization_status ENUM('none', 'pending', 'granted', 'expired', 'revoked') DEFAULT 'none' COMMENT 'Trạng thái <PERSON>y quyền độc quyền'");
    }
};
