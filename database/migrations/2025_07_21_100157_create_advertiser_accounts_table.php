<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('advertiser_accounts', function (Blueprint $table) {
            $table->id();

            // Các trường theo TikTok API specification
            $table->string('advertiser_id')->unique()->comment('TikTok Advertiser ID từ API');
            $table->string('advertiser_name')->comment('Tên tài khoản quảng cáo');
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('active')->comment('Trạng thái tài khoản');
            $table->string('currency', 10)->nullable()->comment('Tiền tệ mặc định');
            $table->string('timezone', 50)->nullable()->comment('<PERSON><PERSON><PERSON> giờ');
            $table->string('company_name')->nullable()->comment('Tên công ty');
            $table->string('industry')->nullable()->comment('Ngành nghề');
            $table->json('permissions')->nullable()->comment('Quyền hạn của tài khoản');
            $table->decimal('balance', 15, 2)->default(0)->comment('Số dư tài khoản');
            $table->timestamp('authorized_at')->nullable()->comment('Thời gian được ủy quyền');
            $table->timestamp('last_sync_at')->nullable()->comment('Lần sync cuối cùng');

            // Timestamps và soft deletes
            $table->timestamps();
            $table->softDeletes();

            // Indexes cho performance
            $table->index(['status', 'currency']);
            $table->index('authorized_at');
            $table->index('last_sync_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advertiser_accounts');
    }
};
