<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gmv_max_videos', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('video_id')->unique()->comment('Video ID từ TikTok API');
            $table->string('title')->comment('Tiêu đề video');
            $table->text('url')->nullable()->comment('URL video');
            $table->string('thumbnail')->nullable()->comment('Đường dẫn thumbnail');
            $table->integer('duration')->nullable()->comment('Thời lượng video (giây)');
            $table->enum('status', ['active', 'inactive', 'processing', 'failed'])->default('processing')->comment('Trạng thái video');
            $table->boolean('is_custom_anchor')->default(false)->comment('Là custom anchor video');

            // Foreign key to campaigns
            $table->foreignId('campaign_id')->constrained('gmv_max_campaigns')->onDelete('cascade')->comment('ID chiến dịch');

            // Timestamps
            $table->timestamps();

            // Indexes cho performance
            $table->index(['status', 'is_custom_anchor']);
            $table->index(['campaign_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gmv_max_videos');
    }
};
