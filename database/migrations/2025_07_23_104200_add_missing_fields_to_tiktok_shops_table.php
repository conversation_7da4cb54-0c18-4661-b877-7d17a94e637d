<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tiktok_shops', function (Blueprint $table) {
            // Business Center fields
            $table->string('store_authorized_bc_id')->nullable()->comment('ID của Business Center được ủy quyền truy cập shop');
            $table->boolean('is_owner_bc')->default(false)->comment('BC có sở hữu shop không');
            $table->string('bc_id')->nullable()->comment('ID của Business Center');
            $table->string('bc_name')->nullable()->comment('Tên Business Center');
            $table->text('bc_profile_image')->nullable()->comment('URL ảnh profile của BC');
            $table->enum('user_role', ['ADMIN', 'STANDARD'])->nullable()->comment('Vai trò user trong BC');

            // Shop details
            $table->string('store_code')->nullable()->comment('Mã code của TikTok Shop');
            $table->text('thumbnail_url')->nullable()->comment('URL thumbnail của shop');
            $table->enum('store_role', ['AD_PROMOTION', 'MANAGER', 'UNSET'])->nullable()->comment('Quyền của BC user đối với shop');

            // Advertiser info
            $table->string('advertiser_name')->nullable()->comment('Tên advertiser account');

            // Update existing fields to match TikTok API
            $table->json('targeting_region_codes')->nullable()->comment('Danh sách mã vùng shop có thể target');

            // Indexes for new fields
            $table->index('store_authorized_bc_id');
            $table->index('bc_id');
            $table->index(['is_owner_bc', 'store_role']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tiktok_shops', function (Blueprint $table) {
            $table->dropColumn([
                'store_authorized_bc_id',
                'is_owner_bc',
                'bc_id',
                'bc_name',
                'bc_profile_image',
                'user_role',
                'store_code',
                'thumbnail_url',
                'store_role',
                'advertiser_name',
                'targeting_region_codes'
            ]);
        });
    }
};
