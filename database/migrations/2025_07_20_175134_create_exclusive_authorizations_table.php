<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exclusive_authorizations', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('authorization_id')->unique()->comment('Authorization ID từ TikTok API');
            $table->string('advertiser_id')->comment('Advertiser ID');
            $table->enum('status', ['pending', 'granted', 'expired', 'revoked'])->default('pending')->comment('Trạng thái ủy quyền');
            $table->datetime('granted_at')->nullable()->comment('Ngày cấp quyền');
            $table->datetime('expires_at')->nullable()->comment('<PERSON><PERSON>y hết hạn');

            // Foreign key to shops
            $table->foreignId('shop_id')->constrained('tiktok_shops')->onDelete('cascade')->comment('ID cửa hàng');

            // Timestamps
            $table->timestamps();

            // Indexes cho performance
            $table->index(['status', 'expires_at']);
            $table->index(['shop_id', 'status']);
            $table->index('advertiser_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exclusive_authorizations');
    }
};
