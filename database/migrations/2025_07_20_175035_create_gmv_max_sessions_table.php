<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gmv_max_sessions', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('session_id')->unique()->comment('Session ID từ TikTok API');
            $table->string('name')->comment('Tên session');
            $table->enum('status', ['draft', 'active', 'paused', 'completed', 'cancelled'])->default('draft')->comment('Trạng thái session');
            $table->enum('delivery_type', ['standard', 'accelerated', 'balanced'])->default('standard')->comment('Loại triển khai');
            $table->decimal('budget', 15, 2)->nullable()->comment('Ngân sách session');
            $table->datetime('start_time')->comment('Thời gian bắt đầu');
            $table->datetime('end_time')->nullable()->comment('Thời gian kết thúc');

            // Foreign key to campaigns
            $table->foreignId('campaign_id')->constrained('gmv_max_campaigns')->onDelete('cascade')->comment('ID chiến dịch');

            // Timestamps
            $table->timestamps();

            // Indexes cho performance
            $table->index(['status', 'start_time']);
            $table->index(['campaign_id', 'status']);
            $table->index('delivery_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gmv_max_sessions');
    }
};
