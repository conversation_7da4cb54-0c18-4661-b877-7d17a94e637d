<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the constraint exists before trying to drop it
        $indexes = DB::select("SHOW INDEX FROM ai_analyses WHERE Key_name = 'unique_current_analysis'");
        
        if (!empty($indexes)) {
            Schema::table('ai_analyses', function (Blueprint $table) {
                $table->dropUnique('unique_current_analysis');
            });
        }

        // Clean up any duplicate records
        DB::statement("
            DELETE a1 FROM ai_analyses a1
            INNER JOIN ai_analyses a2 
            WHERE a1.id > a2.id 
            AND a1.campaign_id = a2.campaign_id 
            AND a1.analysis_type = a2.analysis_type 
            AND a1.is_current = a2.is_current
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore the unique constraint if needed
        Schema::table('ai_analyses', function (Blueprint $table) {
            $table->unique(['campaign_id', 'analysis_type', 'is_current'], 'unique_current_analysis');
        });
    }
};
