<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shop_products', function (Blueprint $table) {
            $table->id();

            // Core TikTok API fields
            $table->string('item_group_id')->unique()->comment('SPU ID của sản phẩm từ TikTok API');
            $table->string('store_id')->comment('TikTok Shop ID');
            $table->string('title')->comment('Tiêu đề sản phẩm');
            $table->text('product_image_url')->nullable()->comment('URL hình ảnh sản phẩm');

            // Price fields
            $table->decimal('min_price', 15, 2)->comment('Giá tối thiểu');
            $table->decimal('max_price', 15, 2)->comment('<PERSON><PERSON><PERSON> tối đa');
            $table->string('currency', 10)->comment('Mã tiền tệ');

            // Product details
            $table->string('category')->nullable()->comment('Danh mục sản phẩm');
            $table->integer('historical_sales')->default(0)->comment('Số lượng bán lịch sử');

            // Status fields
            $table->enum('status', ['AVAILABLE', 'NOT_AVAILABLE'])->default('AVAILABLE')->comment('Trạng thái sản phẩm');
            $table->enum('gmv_max_ads_status', ['OCCUPIED', 'UNOCCUPIED'])->nullable()->comment('Trạng thái trong GMV Max Campaign');
            $table->boolean('is_running_custom_shop_ads')->default(false)->comment('Đang chạy Shopping Ads');

            // Deprecated field (kept for compatibility)
            $table->string('catalog_id')->nullable()->comment('ID catalog (deprecated)');

            // Foreign key to shops
            $table->foreignId('shop_id')->constrained('tiktok_shops')->onDelete('cascade')->comment('ID cửa hàng local');

            // Timestamps
            $table->timestamps();

            // Indexes cho performance
            $table->index(['status', 'gmv_max_ads_status']);
            $table->index(['shop_id', 'status']);
            $table->index(['store_id', 'status']);
            $table->index('category');
            $table->index('historical_sales');
            $table->index('is_running_custom_shop_ads');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shop_products');
    }
};
