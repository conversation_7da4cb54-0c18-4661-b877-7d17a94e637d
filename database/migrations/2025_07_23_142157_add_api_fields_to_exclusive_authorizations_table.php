<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exclusive_authorizations', function (Blueprint $table) {
            // Add new fields from TikTok API v1.3 specification
            $table->string('store_id')->nullable()->after('authorization_id')->comment('TikTok Store ID');
            $table->string('store_authorized_bc_id')->nullable()->after('store_id')->comment('Business Center ID authorized for store');
            $table->string('advertiser_name')->nullable()->after('advertiser_id')->comment('Advertiser account name');
            $table->string('identity_id')->nullable()->after('advertiser_name')->comment('Official TikTok account identity ID');

            // Add indexes for new fields
            $table->index('store_id');
            $table->index('store_authorized_bc_id');
            $table->index(['store_id', 'advertiser_id']);
        });

        // Update status enum to match TikTok API specification
        DB::statement("ALTER TABLE exclusive_authorizations MODIFY COLUMN status ENUM('pending', 'granted', 'expired', 'revoked', 'EFFECTIVE', 'INEFFECTIVE', 'UNAUTHORIZED') DEFAULT 'pending' COMMENT 'Authorization status'");

        // Add new advertiser_status column with TikTok API enum values
        Schema::table('exclusive_authorizations', function (Blueprint $table) {
            $table->enum('advertiser_status', [
                'STATUS_ENABLE',
                'STATUS_CONFIRM_FAIL',
                'STATUS_PENDING_CONFIRM',
                'STATUS_LIMIT',
                'STATUS_CONTRACT_PENDING',
                'STATUS_DISABLE',
                'STATUS_PENDING_CONFIRM_MODIFY',
                'STATUS_PENDING_VERIFIED',
                'STATUS_SELF_SERVICE_UNAUDITED',
                'STATUS_WAIT_FOR_BPM_AUDIT',
                'STATUS_CONFIRM_FAIL_END',
                'STATUS_CONFIRM_MODIFY_FAIL'
            ])->nullable()->after('status')->comment('Advertiser account status from TikTok API');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exclusive_authorizations', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['store_id']);
            $table->dropIndex(['store_authorized_bc_id']);
            $table->dropIndex(['store_id', 'advertiser_id']);

            // Drop new columns
            $table->dropColumn([
                'store_id',
                'store_authorized_bc_id',
                'advertiser_name',
                'identity_id',
                'advertiser_status'
            ]);
        });

        // Revert status enum to original values
        DB::statement("ALTER TABLE exclusive_authorizations MODIFY COLUMN status ENUM('pending', 'granted', 'expired', 'revoked') DEFAULT 'pending' COMMENT 'Trạng thái ủy quyền'");
    }
};
