<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gmv_max_campaigns', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('campaign_id')->unique()->comment('Campaign ID từ TikTok API');
            $table->string('name')->comment('Tên chiến dịch');
            $table->enum('status', ['draft', 'active', 'paused', 'completed', 'cancelled'])->default('draft')->comment('Trạng thái chiến dịch');
            $table->decimal('target_roi', 8, 2)->nullable()->comment('Target ROI (%)');
            $table->decimal('budget', 15, 2)->nullable()->comment('Ngân sách tổng');
            $table->decimal('daily_budget', 15, 2)->nullable()->comment('Ngân sách hàng ngày');
            $table->datetime('start_date')->comment('Ngày bắt đầu');
            $table->datetime('end_date')->nullable()->comment('Ngày kết thúc');
            $table->string('advertiser_id')->comment('Advertiser ID');

            // Foreign key to shops
            $table->foreignId('shop_id')->constrained('tiktok_shops')->onDelete('cascade')->comment('ID cửa hàng');

            // Timestamps và soft deletes
            $table->timestamps();
            $table->softDeletes();

            // Indexes cho performance
            $table->index(['status', 'start_date']);
            $table->index('advertiser_id');
            $table->index(['shop_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gmv_max_campaigns');
    }
};
