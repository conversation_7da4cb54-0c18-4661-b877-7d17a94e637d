<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_reports', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('report_id')->unique()->comment('Report ID từ TikTok API');
            $table->date('report_date')->comment('Ngày báo cáo');
            $table->decimal('total_cost', 15, 2)->default(0)->comment('Chi phí tổng');
            $table->integer('orders_count')->default(0)->comment('Số đơn hàng');
            $table->decimal('gross_revenue', 15, 2)->default(0)->comment('Doanh thu tổng');
            $table->decimal('cost_per_order', 15, 2)->default(0)->comment('<PERSON> phí trên mỗi đơn hàng');
            $table->decimal('roi', 8, 2)->default(0)->comment('Return on Investment (%)');
            $table->bigInteger('impressions')->default(0)->comment('Số lượt hiển thị');
            $table->bigInteger('clicks')->default(0)->comment('Số lượt click');
            $table->decimal('ctr', 8, 2)->default(0)->comment('Click-through rate (%)');
            $table->decimal('conversion_rate', 8, 2)->default(0)->comment('Tỷ lệ chuyển đổi (%)');

            // Foreign key to campaigns
            $table->string('campaign_id')->comment('TikTok Campaign ID');
            $table->foreign('campaign_id')->references('campaign_id')->on('gmv_max_campaigns')->onDelete('cascade');

            // Timestamps
            $table->timestamps();

            // Indexes cho performance và analytics
            $table->index(['report_date', 'campaign_id']);
            $table->index(['campaign_id', 'report_date']);
            $table->index('roi');
            $table->unique(['campaign_id', 'report_date'], 'unique_campaign_report_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_reports');
    }
};
