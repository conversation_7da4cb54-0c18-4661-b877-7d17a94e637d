<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tiktok_shops', function (Blueprint $table) {
            $table->id();

            // Các trường theo JSON specification
            $table->string('shop_id')->unique()->comment('TikTok Shop ID từ API');
            $table->string('name')->comment('Tên cửa hàng');
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('pending')->comment('Trạng thái cửa hàng');
            $table->boolean('is_eligible_gmv_max')->default(false)->comment('Đủ điều kiện GMV Max');
            $table->string('region', 10)->nullable()->comment('<PERSON>hu vực (VN, TH, MY, SG, ID, PH)');
            $table->string('currency', 10)->nullable()->comment('Tiền tệ (VND, USD, THB)');
            $table->string('advertiser_id')->nullable()->comment('Advertiser ID từ TikTok');
            $table->enum('exclusive_authorization_status', ['none', 'pending', 'granted', 'expired', 'revoked'])->default('none')->comment('Trạng thái ủy quyền độc quyền');

            // Timestamps và soft deletes
            $table->timestamps();
            $table->softDeletes();

            // Indexes cho performance
            $table->index(['status', 'is_eligible_gmv_max']);
            $table->index('region');
            $table->index('advertiser_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tiktok_shops');
    }
};
