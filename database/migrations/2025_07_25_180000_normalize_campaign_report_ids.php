<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing report IDs to use new normalized format
        $this->normalizeExistingReportIds();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Cannot reverse this migration as we lose the original format information
        // This is a one-way migration for data normalization
    }

    /**
     * Normalize existing report IDs to new format
     */
    private function normalizeExistingReportIds(): void
    {
        $reports = DB::table('campaign_reports')->get();
        
        foreach ($reports as $report) {
            $oldReportId = $report->report_id;
            
            // Extract components from old format
            if (preg_match('/^gmv_max_(\w+)_(\d+)_(.+)$/', $oldReportId, $matches)) {
                $campaignType = $matches[1];
                $campaignId = $matches[2];
                $dateString = $matches[3];
                
                try {
                    // Parse date and normalize to YYYYMMDD
                    $normalizedDate = Carbon::parse($dateString)->format('Ymd');
                    
                    // Generate new report ID
                    $newReportId = "gmv_max_{$campaignType}_{$campaignId}_{$normalizedDate}";
                    
                    // Update if different
                    if ($oldReportId !== $newReportId) {
                        DB::table('campaign_reports')
                            ->where('id', $report->id)
                            ->update(['report_id' => $newReportId]);
                            
                        echo "Updated: {$oldReportId} → {$newReportId}\n";
                    }
                    
                } catch (\Exception $e) {
                    echo "Failed to parse date for report ID: {$oldReportId} - {$e->getMessage()}\n";
                }
            } else {
                echo "Skipped invalid format: {$oldReportId}\n";
            }
        }
    }
};
