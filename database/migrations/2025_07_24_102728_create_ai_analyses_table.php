<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_analyses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained('gmv_max_campaigns')->onDelete('cascade');
            $table->enum('analysis_type', ['recommendations', 'roi_prediction', 'budget_optimization']);
            $table->json('ai_result')->comment('Complete AI response data');
            $table->decimal('confidence_score', 5, 2)->nullable()->comment('AI confidence score 0-100');
            $table->boolean('is_current')->default(true)->comment('Mark latest analysis for each type');
            $table->timestamp('analyzed_at')->useCurrent()->comment('When analysis was performed');
            $table->timestamps();

            // Indexes for performance
            $table->index(['campaign_id', 'analysis_type', 'is_current']);
            $table->index(['campaign_id', 'is_current']);
            $table->index(['analysis_type', 'is_current']);
            $table->index('analyzed_at');

            // Ensure only one current analysis per campaign per type
            $table->unique(['campaign_id', 'analysis_type', 'is_current'], 'unique_current_analysis');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_analyses');
    }
};
