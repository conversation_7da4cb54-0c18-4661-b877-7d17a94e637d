<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tiktok_settings', function (Blueprint $table) {
            $table->id();

            // Configuration key (unique identifier)
            $table->string('key')->unique()->comment('Configuration key identifier');

            // Configuration value (encrypted for sensitive data)
            $table->text('value')->nullable()->comment('Configuration value (encrypted for sensitive data)');

            // Metadata
            $table->string('type')->default('string')->comment('Data type: string, boolean, integer, json');
            $table->boolean('is_encrypted')->default(false)->comment('Whether the value is encrypted');
            $table->boolean('is_public')->default(true)->comment('Whether the setting can be displayed in UI');
            $table->text('description')->nullable()->comment('Human-readable description');
            $table->string('group')->default('general')->comment('Settings group for organization');

            // Validation
            $table->json('validation_rules')->nullable()->comment('Validation rules for the setting');
            $table->text('default_value')->nullable()->comment('Default value if not set');

            // Audit trail
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this setting');
            $table->timestamp('last_tested_at')->nullable()->comment('Last time this setting was tested');
            $table->json('test_result')->nullable()->comment('Result of last test');

            $table->timestamps();

            // Indexes
            $table->index(['group', 'key']);
            $table->index('is_public');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tiktok_settings');
    }
};
