<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tiktok_shops', function (Blueprint $table) {
            // Thêm foreign key tới advertiser_accounts table
            $table->foreignId('advertiser_account_id')->nullable()->after('advertiser_id')
                  ->constrained('advertiser_accounts')->onDelete('set null')
                  ->comment('<PERSON>ên kết tới tài khoản quảng cáo');

            // Index cho performance
            $table->index('advertiser_account_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tiktok_shops', function (Blueprint $table) {
            $table->dropForeign(['advertiser_account_id']);
            $table->dropIndex(['advertiser_account_id']);
            $table->dropColumn('advertiser_account_id');
        });
    }
};
