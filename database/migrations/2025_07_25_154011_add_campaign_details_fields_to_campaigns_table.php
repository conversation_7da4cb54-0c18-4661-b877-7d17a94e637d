<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gmv_max_campaigns', function (Blueprint $table) {
            // Store and authorization info
            $table->string('store_id')->nullable();
            $table->string('store_authorized_bc_id')->nullable();

            // Campaign type and configuration
            $table->string('shopping_ads_type')->nullable(); // PRODUCT, LIVE
            $table->string('product_specific_type')->nullable(); // ALL, CUSTOMIZED_PRODUCTS, UNSET
            $table->string('optimization_goal')->nullable(); // VALUE
            $table->boolean('roi_protection_enabled')->default(false);

            // Bidding and budget
            $table->string('deep_bid_type')->nullable(); // VO_MIN_ROAS
            $table->decimal('roas_bid', 8, 2)->nullable();
            $table->decimal('budget_details', 12, 2)->nullable(); // Rename to avoid conflict

            // Scheduling
            $table->string('schedule_type')->nullable(); // SCHEDULE_FROM_NOW, SCHEDULE_START_END
            $table->timestamp('schedule_start_time')->nullable();
            $table->timestamp('schedule_end_time')->nullable();

            // Targeting (JSON fields)
            $table->json('placements')->nullable(); // ["PLACEMENT_TIKTOK", "PLACEMENT_PANGLE"]
            $table->json('location_ids')->nullable();
            $table->json('age_groups')->nullable();

            // Video and content settings
            $table->string('product_video_specific_type')->nullable(); // AUTO_SELECTION, CUSTOM_SELECTION, UNSET
            $table->boolean('affiliate_posts_enabled')->default(false);

            // Product and content associations (JSON fields)
            $table->json('item_group_ids')->nullable(); // Product SPU IDs
            $table->json('identity_list')->nullable(); // TikTok accounts
            $table->json('item_list')->nullable(); // Authorized TikTok posts
            $table->json('custom_anchor_video_list')->nullable(); // Customized posts
            $table->string('campaign_custom_anchor_video_id')->nullable();

            // Sync tracking
            $table->timestamp('details_synced_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gmv_max_campaigns', function (Blueprint $table) {
            $table->dropColumn([
                'store_id',
                'store_authorized_bc_id',
                'shopping_ads_type',
                'product_specific_type',
                'optimization_goal',
                'roi_protection_enabled',
                'deep_bid_type',
                'roas_bid',
                'budget_details',
                'schedule_type',
                'schedule_start_time',
                'schedule_end_time',
                'placements',
                'location_ids',
                'age_groups',
                'product_video_specific_type',
                'affiliate_posts_enabled',
                'item_group_ids',
                'identity_list',
                'item_list',
                'custom_anchor_video_list',
                'campaign_custom_anchor_video_id',
                'details_synced_at'
            ]);
        });
    }
};
