<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tiktok_shops', function (Blueprint $table) {
            // Local image paths
            $table->string('thumbnail_local_path')->nullable()->comment('Local path cho shop thumbnail image');
            $table->string('bc_profile_image_local_path')->nullable()->comment('Local path cho BC profile image');

            // Metadata cho image management
            $table->timestamp('images_last_synced_at')->nullable()->comment('Lần cuối sync images');
            $table->json('image_sync_metadata')->nullable()->comment('Metadata về image sync process');

            // Indexes
            $table->index('images_last_synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tiktok_shops', function (Blueprint $table) {
            $table->dropColumn([
                'thumbnail_local_path',
                'bc_profile_image_local_path',
                'images_last_synced_at',
                'image_sync_metadata'
            ]);
        });
    }
};
