<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AdvertiserAccount;
use App\Models\Shop;
use Carbon\Carbon;

class AdvertiserAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo advertiser accounts mẫu
        $advertiserAccounts = [
            [
                'advertiser_id' => 'adv_techstore_vn_001',
                'advertiser_name' => 'TechStore Vietnam Ads Account',
                'status' => 'active',
                'currency' => 'VND',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'company_name' => 'TechStore Vietnam Co., Ltd',
                'industry' => 'Electronics & Technology',
                'permissions' => ['campaign_management', 'reporting', 'billing'],
                'balance' => ********.00, // 50M VND
                'authorized_at' => Carbon::now()->subDays(30),
                'last_sync_at' => Carbon::now()->subHours(2),
            ],
            [
                'advertiser_id' => 'adv_fashion_hub_002',
                'advertiser_name' => 'Fashion Hub VN Ads Account',
                'status' => 'active',
                'currency' => 'VND',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'company_name' => 'Fashion Hub Vietnam Ltd',
                'industry' => 'Fashion & Apparel',
                'permissions' => ['campaign_management', 'reporting'],
                'balance' => ********.00, // 30M VND
                'authorized_at' => Carbon::now()->subDays(25),
                'last_sync_at' => Carbon::now()->subHours(1),
            ],
            [
                'advertiser_id' => 'adv_beauty_corner_003',
                'advertiser_name' => 'Beauty Corner Ads Account',
                'status' => 'active',
                'currency' => 'VND',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'company_name' => 'Beauty Corner Vietnam',
                'industry' => 'Beauty & Cosmetics',
                'permissions' => ['campaign_management', 'reporting'],
                'balance' => ********.00, // 20M VND
                'authorized_at' => Carbon::now()->subDays(20),
                'last_sync_at' => Carbon::now()->subHours(3),
            ],
            [
                'advertiser_id' => 'adv_sports_fitness_004',
                'advertiser_name' => 'Sports & Fitness Ads Account',
                'status' => 'active',
                'currency' => 'VND',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'company_name' => 'Sports & Fitness Vietnam',
                'industry' => 'Sports & Recreation',
                'permissions' => ['campaign_management', 'reporting'],
                'balance' => ********.00, // 40M VND
                'authorized_at' => Carbon::now()->subDays(15),
                'last_sync_at' => Carbon::now()->subHours(4),
            ],
            [
                'advertiser_id' => 'adv_thai_electronics_005',
                'advertiser_name' => 'Thai Electronics Ads Account',
                'status' => 'active',
                'currency' => 'THB',
                'timezone' => 'Asia/Bangkok',
                'company_name' => 'Thai Electronics Co., Ltd',
                'industry' => 'Electronics & Technology',
                'permissions' => ['campaign_management', 'reporting', 'billing'],
                'balance' => 500000.00, // 500K THB
                'authorized_at' => Carbon::now()->subDays(10),
                'last_sync_at' => Carbon::now()->subHours(1),
            ],
            [
                'advertiser_id' => 'adv_malaysia_fashion_006',
                'advertiser_name' => 'Malaysia Fashion Ads Account',
                'status' => 'active',
                'currency' => 'USD',
                'timezone' => 'Asia/Kuala_Lumpur',
                'company_name' => 'Malaysia Fashion Sdn Bhd',
                'industry' => 'Fashion & Apparel',
                'permissions' => ['campaign_management', 'reporting'],
                'balance' => 10000.00, // 10K USD
                'authorized_at' => Carbon::now()->subDays(12),
                'last_sync_at' => Carbon::now()->subHours(2),
            ],
        ];

        foreach ($advertiserAccounts as $accountData) {
            AdvertiserAccount::create($accountData);
        }

        // Link existing shops với advertiser accounts
        $this->linkShopsToAdvertiserAccounts();

        $this->command->info('Created ' . count($advertiserAccounts) . ' advertiser accounts and linked to shops');
    }

    /**
     * Link existing shops với advertiser accounts dựa trên advertiser_id
     */
    protected function linkShopsToAdvertiserAccounts(): void
    {
        $shops = Shop::all();
        $advertiserAccounts = AdvertiserAccount::all();

        foreach ($shops as $shop) {
            // Tìm advertiser account phù hợp dựa trên region hoặc tạo mapping logic
            $advertiserAccount = $this->findMatchingAdvertiserAccount($shop, $advertiserAccounts);

            if ($advertiserAccount) {
                $shop->update([
                    'advertiser_account_id' => $advertiserAccount->id,
                    'advertiser_id' => $advertiserAccount->advertiser_id
                ]);
            }
        }
    }

    /**
     * Tìm advertiser account phù hợp cho shop
     */
    protected function findMatchingAdvertiserAccount(Shop $shop, $advertiserAccounts): ?AdvertiserAccount
    {
        // Mapping logic dựa trên tên shop hoặc region
        $mappings = [
            'TechStore Vietnam' => 'adv_techstore_vn_001',
            'Fashion Hub VN' => 'adv_fashion_hub_002',
            'Beauty Corner' => 'adv_beauty_corner_003',
            'Sports & Fitness' => 'adv_sports_fitness_004',
            'Thai Electronics' => 'adv_thai_electronics_005',
            'Malaysia Fashion' => 'adv_malaysia_fashion_006',
        ];

        $advertiserId = $mappings[$shop->name] ?? null;

        if ($advertiserId) {
            return $advertiserAccounts->firstWhere('advertiser_id', $advertiserId);
        }

        // Fallback: match by region
        $regionMappings = [
            'VN' => ['adv_techstore_vn_001', 'adv_fashion_hub_002', 'adv_beauty_corner_003', 'adv_sports_fitness_004'],
            'TH' => ['adv_thai_electronics_005'],
            'MY' => ['adv_malaysia_fashion_006'],
        ];

        $possibleAdvertiserIds = $regionMappings[$shop->region] ?? [];

        if (!empty($possibleAdvertiserIds)) {
            return $advertiserAccounts->firstWhere('advertiser_id', $possibleAdvertiserIds[0]);
        }

        return null;
    }
}
