<?php

namespace Database\Seeders;

use App\Models\TikTokSettings;
use Illuminate\Database\Seeder;
use JsonException;

class TikTokSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * @throws JsonException
     */
    public function run(): void
    {
        $settings = [
            // API Configuration
            [
                'key' => 'api.base_url',
                'value' => 'https://business-api.tiktok.com',
                'type' => 'string',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'TikTok Business API Base URL',
                'group' => 'api',
                'default_value' => 'https://business-api.tiktok.com',
            ],
            [
                'key' => 'api.access_token',
                'value' => env('TIKTOK_ACCESS_TOKEN'),
                'type' => 'string',
                'is_encrypted' => true,
                'is_public' => false,
                'description' => 'TikTok Business API Access Token',
                'group' => 'api',
                'validation_rules' => ['required', 'string', 'min:10'],
            ],
            [
                'key' => 'api.app_id',
                'value' => env('TIKTOK_APP_ID'),
                'type' => 'string',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'TikTok App ID',
                'group' => 'api',
                'validation_rules' => ['required', 'string'],
            ],
            [
                'key' => 'api.secret',
                'value' => env('TIKTOK_SECRET'),
                'type' => 'string',
                'is_encrypted' => true,
                'is_public' => false,
                'description' => 'TikTok App Secret',
                'group' => 'api',
                'validation_rules' => ['required', 'string', 'min:10'],
            ],


            // Sync Configuration
            [
                'key' => 'sync.enabled',
                'value' => true,
                'type' => 'boolean',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Enable automatic synchronization',
                'group' => 'sync',
                'default_value' => true,
            ],
            [
                'key' => 'sync.auto_sync_interval',
                'value' => 3600,
                'type' => 'integer',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Auto sync interval in seconds (3600 = 1 hour)',
                'group' => 'sync',
                'default_value' => 3600,
                'validation_rules' => ['integer', 'min:300', 'max:86400'],
            ],
            [
                'key' => 'sync.batch_size',
                'value' => 100,
                'type' => 'integer',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Batch size for sync operations',
                'group' => 'sync',
                'default_value' => 100,
                'validation_rules' => ['integer', 'min:10', 'max:1000'],
            ],
            [
                'key' => 'sync.retry_attempts',
                'value' => 3,
                'type' => 'integer',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Number of retry attempts for failed sync operations',
                'group' => 'sync',
                'default_value' => 3,
                'validation_rules' => ['integer', 'min:1', 'max:10'],
            ],

            // Cache Configuration
            [
                'key' => 'cache.default_ttl',
                'value' => 3600,
                'type' => 'integer',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Default cache TTL in seconds',
                'group' => 'cache',
                'default_value' => 3600,
                'validation_rules' => ['integer', 'min:60', 'max:86400'],
            ],
            [
                'key' => 'cache.api_response_ttl',
                'value' => 1800,
                'type' => 'integer',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'API response cache TTL in seconds',
                'group' => 'cache',
                'default_value' => 1800,
                'validation_rules' => ['integer', 'min:60', 'max:7200'],
            ],

            // Logging Configuration
            [
                'key' => 'logging.enabled',
                'value' => true,
                'type' => 'boolean',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Enable TikTok API logging',
                'group' => 'logging',
                'default_value' => true,
            ],
            [
                'key' => 'logging.level',
                'value' => 'info',
                'type' => 'string',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Logging level (debug, info, warning, error)',
                'group' => 'logging',
                'default_value' => 'info',
                'validation_rules' => ['in:debug,info,warning,error'],
            ],
            [
                'key' => 'logging.log_requests',
                'value' => true,
                'type' => 'boolean',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Log all API requests',
                'group' => 'logging',
                'default_value' => true,
            ],
            [
                'key' => 'logging.log_responses',
                'value' => false,
                'type' => 'boolean',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Log API responses (may contain sensitive data)',
                'group' => 'logging',
                'default_value' => false,
            ],

            // AI Scoring Configuration
            [
                'key' => 'ai_scoring.enabled',
                'value' => true,
                'type' => 'boolean',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Enable AI-powered campaign scoring and optimization',
                'group' => 'ai_scoring',
                'default_value' => true,
            ],
            [
                'key' => 'ai_scoring.provider',
                'value' => 'gemini',
                'type' => 'string',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'AI provider for scoring system (gemini, openai, claude)',
                'group' => 'ai_scoring',
                'default_value' => 'gemini',
                'validation_rules' => ['in:gemini,openai,claude'],
            ],
            [
                'key' => 'ai_scoring.model',
                'value' => 'gemini-2.0-flash',
                'type' => 'string',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'AI model for campaign analysis (optimized for cost-efficiency and real-time analysis)',
                'group' => 'ai_scoring',
                'default_value' => 'gemini-2.0-flash',
                'validation_rules' => ['string', 'max:100'],
            ],
            [
                'key' => 'ai_scoring.api_key',
                'value' => env('GEMINI_API_KEY'),
                'type' => 'string',
                'is_encrypted' => true,
                'is_public' => false,
                'description' => 'API key for AI provider (encrypted)',
                'group' => 'ai_scoring',
                'validation_rules' => ['required', 'string', 'min:10'],
            ],
            [
                'key' => 'ai_scoring.features',
                'value' => json_encode(['roi_prediction', 'budget_optimization', 'performance_alerts', 'campaign_recommendations'], JSON_THROW_ON_ERROR),
                'type' => 'json',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Enabled AI features for campaign analysis',
                'group' => 'ai_scoring',
                'default_value' => json_encode(['roi_prediction', 'budget_optimization', 'performance_alerts', 'campaign_recommendations'], JSON_THROW_ON_ERROR),
                'validation_rules' => ['array'],
            ],
            [
                'key' => 'ai_scoring.analysis_frequency',
                'value' => 'hourly',
                'type' => 'string',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Frequency of AI analysis (realtime, hourly, daily)',
                'group' => 'ai_scoring',
                'default_value' => 'hourly',
                'validation_rules' => ['in:realtime,hourly,daily'],
            ],
            [
                'key' => 'ai_scoring.confidence_threshold',
                'value' => 0.75,
                'type' => 'float',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Minimum confidence threshold for AI recommendations (0.0 - 1.0)',
                'group' => 'ai_scoring',
                'default_value' => 0.75,
                'validation_rules' => ['numeric', 'min:0', 'max:1'],
            ],
            [
                'key' => 'ai_scoring.max_requests_per_hour',
                'value' => 1000,
                'type' => 'integer',
                'is_encrypted' => false,
                'is_public' => true,
                'description' => 'Maximum AI API requests per hour (rate limiting)',
                'group' => 'ai_scoring',
                'default_value' => 1000,
                'validation_rules' => ['integer', 'min:10', 'max:10000'],
            ],
        ];

        foreach ($settings as $settingData) {
            TikTokSettings::updateOrCreate(
                ['key' => $settingData['key']],
                $settingData
            );
        }

        $this->command->info('Created ' . count($settings) . ' TikTok settings');
    }
}
