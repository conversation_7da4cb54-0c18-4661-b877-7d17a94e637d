<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Shop;
use Illuminate\Support\Str;

class ShopSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $shops = [
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'TechStore Vietnam',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'VN',
                'currency' => 'VND',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'granted',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Fashion Hub VN',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'VN',
                'currency' => 'VND',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'granted',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Beauty Corner',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'VN',
                'currency' => 'VND',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'pending',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Home & Living',
                'status' => 'active',
                'is_eligible_gmv_max' => false,
                'region' => 'VN',
                'currency' => 'VND',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'none',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Sports & Fitness',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'VN',
                'currency' => 'VND',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'granted',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Thai Electronics',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'TH',
                'currency' => 'THB',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'granted',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Malaysia Fashion',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'MY',
                'currency' => 'USD',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'granted',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Singapore Gadgets',
                'status' => 'active',
                'is_eligible_gmv_max' => true,
                'region' => 'SG',
                'currency' => 'USD',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'granted',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Indonesia Marketplace',
                'status' => 'inactive',
                'is_eligible_gmv_max' => false,
                'region' => 'ID',
                'currency' => 'USD',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'none',
            ],
            [
                'shop_id' => 'shop_' . Str::random(10),
                'name' => 'Philippines Store',
                'status' => 'pending',
                'is_eligible_gmv_max' => false,
                'region' => 'PH',
                'currency' => 'USD',
                'advertiser_id' => 'adv_' . Str::random(8),
                'exclusive_authorization_status' => 'pending',
            ],
        ];

        foreach ($shops as $shopData) {
            Shop::create($shopData);
        }

        $this->command->info('Created ' . count($shops) . ' sample shops');
    }
}
