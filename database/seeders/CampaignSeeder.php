<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campaign;
use App\Models\Shop;
use Illuminate\Support\Str;
use Carbon\Carbon;

class CampaignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Lấy các shops đã tạo để link campaigns
        $shops = Shop::where('is_eligible_gmv_max', true)->get();

        if ($shops->isEmpty()) {
            $this->command->warn('No eligible shops found for campaigns');
            return;
        }

        $campaigns = [
            [
                'campaign_id' => 'camp_' . Str::random(10),
                'name' => 'Tết Sale 2025 - Electronics',
                'status' => 'active',
                'target_roi' => 250.00,
                'budget' => 50000000.00, // 50M VND
                'daily_budget' => 2000000.00, // 2M VND
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(20),
                'advertiser_id' => 'adv_' . Str::random(8),
                'shop_id' => $shops->first()->id,
            ],
            [
                'campaign_id' => 'camp_' . Str::random(10),
                'name' => 'Fashion Week Promotion',
                'status' => 'active',
                'target_roi' => 300.00,
                'budget' => 30000000.00, // 30M VND
                'daily_budget' => 1500000.00, // 1.5M VND
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->addDays(25),
                'advertiser_id' => 'adv_' . Str::random(8),
                'shop_id' => $shops->skip(1)->first()->id ?? $shops->first()->id,
            ],
            [
                'campaign_id' => 'camp_' . Str::random(10),
                'name' => 'Beauty Summer Collection',
                'status' => 'paused',
                'target_roi' => 200.00,
                'budget' => 20000000.00, // 20M VND
                'daily_budget' => 800000.00, // 800K VND
                'start_date' => Carbon::now()->subDays(15),
                'end_date' => Carbon::now()->addDays(15),
                'advertiser_id' => 'adv_' . Str::random(8),
                'shop_id' => $shops->skip(2)->first()->id ?? $shops->first()->id,
            ],
            [
                'campaign_id' => 'camp_' . Str::random(10),
                'name' => 'Sports Equipment Sale',
                'status' => 'active',
                'target_roi' => 180.00,
                'budget' => 40000000.00, // 40M VND
                'daily_budget' => 1800000.00, // 1.8M VND
                'start_date' => Carbon::now()->subDays(7),
                'end_date' => Carbon::now()->addDays(23),
                'advertiser_id' => 'adv_' . Str::random(8),
                'shop_id' => $shops->skip(3)->first()->id ?? $shops->first()->id,
            ],
            [
                'campaign_id' => 'camp_' . Str::random(10),
                'name' => 'Back to School Campaign',
                'status' => 'draft',
                'target_roi' => 220.00,
                'budget' => 25000000.00, // 25M VND
                'daily_budget' => 1200000.00, // 1.2M VND
                'start_date' => Carbon::now()->addDays(5),
                'end_date' => Carbon::now()->addDays(35),
                'advertiser_id' => 'adv_' . Str::random(8),
                'shop_id' => $shops->skip(4)->first()->id ?? $shops->first()->id,
            ],
        ];

        foreach ($campaigns as $campaignData) {
            Campaign::create($campaignData);
        }

        $this->command->info('Created ' . count($campaigns) . ' sample campaigns');
    }
}
