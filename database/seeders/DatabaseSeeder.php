<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Tạo admin user cho testing
        User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'TikTok GMV Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'email_verified_at' => now(),
        ]);

        // Production mode: Chỉ chạy essential seeders
        $this->call([
            TikTokSettingsSeeder::class,    // TikTok Settings (system configuration)
        ]);

        // Demo data seeders (commented out for production)
        // Uncomment these for development/testing with sample data:
        /*
        $this->call([
            ShopSeeder::class,              // Sample shops
            AdvertiserAccountSeeder::class, // Sample advertiser accounts
            CampaignSeeder::class,          // Sample campaigns
            SessionSeeder::class,           // Sample sessions
            ProductSeeder::class,           // Sample products
            VideoSeeder::class,             // Sample videos
            CampaignReportSeeder::class,    // Sample reports
        ]);
        */

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('🔑 Admin login: <EMAIL> / admin123');
        $this->command->info('🌐 Access admin panel: http://localhost:8000/admin');
    }
}
