<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campaign;
use App\Models\CampaignReport;
use App\Models\Shop;
use Illuminate\Support\Str;
use Carbon\Carbon;

class CampaignReportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Lấy các campaigns đã có để tạo reports
        $campaigns = Campaign::all();

        if ($campaigns->isEmpty()) {
            $this->command->info('No campaigns found. Please run CampaignSeeder first.');
            return;
        }

        // Tạo reports cho mỗi campaign trong 30 ngày qua
        foreach ($campaigns as $campaign) {
            for ($i = 29; $i >= 0; $i--) {
                $reportDate = now()->subDays($i);

                // Tạo dữ liệu ngẫu nhiên nhưng realistic
                $impressions = rand(1000, 10000);
                $clicks = rand(50, $impressions * 0.1);
                $orders = rand(1, $clicks * 0.05);
                $totalCost = rand(100000, 1000000); // 100K - 1M VND
                $grossRevenue = $totalCost * (rand(80, 300) / 100); // ROI từ 80% - 300%

                CampaignReport::create([
                    'report_id' => 'report_' . Str::random(10),
                    'campaign_id' => $campaign->id,
                    'report_date' => $reportDate->format('Y-m-d'),
                    'total_cost' => $totalCost,
                    'orders_count' => $orders,
                    'gross_revenue' => $grossRevenue,
                    'cost_per_order' => $orders > 0 ? $totalCost / $orders : 0,
                    'roi' => $totalCost > 0 ? (($grossRevenue - $totalCost) / $totalCost) * 100 : 0,
                    'impressions' => $impressions,
                    'clicks' => $clicks,
                    'ctr' => $impressions > 0 ? ($clicks / $impressions) * 100 : 0,
                    'conversion_rate' => $clicks > 0 ? ($orders / $clicks) * 100 : 0,
                ]);
            }
        }

        $this->command->info('Created ' . count($campaigns) . ' campaigns with 30 days of reports each');
    }
}
