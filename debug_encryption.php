<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TikTokSettings;
use Illuminate\Support\Facades\Crypt;

echo "=== Debug Encrypted Data ===\n";

// Get raw encrypted data
$accessToken = TikTokSettings::where('key', 'api.access_token')->first();
$secret = TikTokSettings::where('key', 'api.secret')->first();

if ($accessToken) {
    echo "Access Token Debug:\n";
    echo "  Raw encrypted value length: " . strlen($accessToken->getRawOriginal('value')) . "\n";
    echo "  Raw value starts with: " . substr($accessToken->getRawOriginal('value'), 0, 20) . "...\n";
    
    // Try to decrypt manually
    try {
        $decrypted = Crypt::decryptString($accessToken->getRawOriginal('value'));
        echo "  Manual decryption: SUCCESS\n";
        echo "  Decrypted value: " . $decrypted . "\n";
    } catch (Exception $e) {
        echo "  Manual decryption: FAILED\n";
        echo "  Error: " . $e->getMessage() . "\n";
    }
}

echo "\n";

if ($secret) {
    echo "Secret Debug:\n";
    echo "  Raw encrypted value length: " . strlen($secret->getRawOriginal('value')) . "\n";
    echo "  Raw value starts with: " . substr($secret->getRawOriginal('value'), 0, 20) . "...\n";
    
    // Try to decrypt manually
    try {
        $decrypted = Crypt::decryptString($secret->getRawOriginal('value'));
        echo "  Manual decryption: SUCCESS\n";
        echo "  Decrypted value: " . $decrypted . "\n";
    } catch (Exception $e) {
        echo "  Manual decryption: FAILED\n";
        echo "  Error: " . $e->getMessage() . "\n";
    }
}

echo "\n=== APP_KEY Info ===\n";
echo "APP_KEY length: " . strlen(config('app.key')) . "\n";
echo "APP_KEY starts with: " . substr(config('app.key'), 0, 10) . "...\n";
