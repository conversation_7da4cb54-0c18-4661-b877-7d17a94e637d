@php
    $analyses = [
        'recommendations' => $recommendationsAnalysis,
        'roi' => $roiAnalysis,
        'budget' => $budgetAnalysis,
    ];

    $totalAnalyses = count(array_filter($analyses));
    $staleAnalyses = 0;

    foreach ($analyses as $analysis) {
        if ($analysis && $analysis->isStale(24)) {
            $staleAnalyses++;
        }
    }

    // Get AI Score from ROI analysis or cached service result
    $aiScore = null;

    // First, try to get from ROI analysis (which has ai_score field)
    if ($roiAnalysis && isset($roiAnalysis->ai_result['ai_score'])) {
        $aiScore = $roiAnalysis->ai_result['ai_score'];
    }

    // Fallback: try to get from AIScoringService cache
    if (!$aiScore) {
        $cacheKey = "ai_score_campaign_{$campaign->id}";
        $cachedResult = cache()->get($cacheKey);
        $aiScore = $cachedResult['ai_score'] ?? 'N/A';
    }

    $scoreColor = is_numeric($aiScore) ?
        ($aiScore >= 80 ? 'success' : ($aiScore >= 60 ? 'warning' : 'danger')) : 'gray';
@endphp

<div class="space-y-6">
    {{-- Header Summary --}}
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ $campaign->name }}
            </h3>
            <div class="flex items-center space-x-3">
                @if(is_numeric($aiScore))
                    <x-filament::badge :color="$scoreColor" size="lg">
                        Điểm AI: {{ $aiScore }}/100
                    </x-filament::badge>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        @if($aiScore >= 80)
                            Hiệu suất xuất sắc
                        @elseif($aiScore >= 60)
                            Hiệu suất tốt, cần cải thiện
                        @else
                            Cần tối ưu hóa
                        @endif
                    </div>
                @else
                    <x-filament::badge color="gray" size="lg">
                        Điểm AI: Chưa có
                    </x-filament::badge>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        Chạy phân tích AI để có điểm số
                    </div>
                @endif
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <span class="text-gray-500 dark:text-gray-400">Trạng thái:</span>
                <x-filament::badge :color="$campaign->status === 'active' ? 'success' : 'warning'">
                    {{ $campaign->status === 'active' ? 'Hoạt động' : 'Không hoạt động' }}
                </x-filament::badge>
            </div>
            <div>
                <span class="text-gray-500 dark:text-gray-400">ROI Mục tiêu:</span>
                <span class="font-medium">{{ $campaign->target_roi ? $campaign->target_roi . '%' : 'Chưa thiết lập' }}</span>
            </div>
            <div>
                <span class="text-gray-500 dark:text-gray-400">Ngân sách:</span>
                <span class="font-medium">{{ $campaign->budget ? number_format($campaign->budget) . ' VND' : 'Chưa thiết lập' }}</span>
            </div>
        </div>
    </div>

    {{-- Analysis Status Overview --}}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        @foreach([
            'recommendations' => ['icon' => '💡', 'title' => 'Khuyến nghị', 'analysis' => $recommendationsAnalysis],
            'roi' => ['icon' => '📈', 'title' => 'Dự đoán ROI', 'analysis' => $roiAnalysis],
            'budget' => ['icon' => '💰', 'title' => 'Tối ưu Ngân sách', 'analysis' => $budgetAnalysis]
        ] as $key => $item)
            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-lg">{{ $item['icon'] }}</span>
                    <h4 class="font-medium text-gray-900 dark:text-white">{{ $item['title'] }}</h4>
                </div>

                @if($item['analysis'])
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            @if($item['analysis']->isStale(24))
                                <x-filament::badge color="warning" size="sm">Cũ</x-filament::badge>
                            @else
                                <x-filament::badge color="success" size="sm">Hiện tại</x-filament::badge>
                            @endif

                            @if($item['analysis']->confidence_score)
                                <span class="text-xs text-gray-500">
                                    {{ $item['analysis']->confidence_score }}% độ tin cậy
                                </span>
                            @endif
                        </div>

                        <p class="text-xs text-gray-600 dark:text-gray-400">
                            Phân tích {{ $item['analysis']->analyzed_at->diffForHumans() }}
                        </p>
                    </div>
                @else
                    <x-filament::badge color="gray" size="sm">Chưa phân tích</x-filament::badge>
                @endif
            </div>
        @endforeach
    </div>

    {{-- Detailed Analysis Sections --}}
    @if($recommendationsAnalysis)
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="flex items-center space-x-2 mb-4">
                <span class="text-xl">💡</span>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Phân tích Khuyến nghị</h3>
                @if($recommendationsAnalysis->isStale(24))
                    <x-filament::badge color="warning" size="sm">Cần cập nhật</x-filament::badge>
                @endif
            </div>

            @if($recommendationsAnalysis->recommendations)
                <div class="space-y-4">
                    @foreach(array_slice($recommendationsAnalysis->recommendations, 0, 5) as $index => $recommendation)
                        @php
                            $recData = is_array($recommendation) ? $recommendation : [];
                            $type = $recData['type'] ?? 'General';
                            $description = $recData['description'] ?? $recData['rationale'] ?? (is_string($recommendation) ? $recommendation : 'No description');
                            $priority = $recData['priority'] ?? 'Medium';
                            $details = $recData['details'] ?? [];
                        @endphp

                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                            <div class="flex items-start space-x-3">
                                <span class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white text-sm font-semibold rounded-full flex items-center justify-center">
                                    {{ $index + 1 }}
                                </span>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <h4 class="font-semibold text-gray-900 dark:text-white">{{ $type }}</h4>
                                        <x-filament::badge
                                            :color="$priority === 'High' ? 'danger' : ($priority === 'Medium' ? 'warning' : 'success')"
                                            size="sm"
                                        >
                                            {{ $priority }}
                                        </x-filament::badge>
                                    </div>

                                    <p class="text-sm text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
                                        {{ $description }}
                                    </p>

                                    @if(!empty($details))
                                        <div class="bg-white dark:bg-gray-800 rounded-md p-3 text-xs">
                                            @foreach($details as $key => $value)
                                                @if(is_string($value) || is_numeric($value))
                                                    <div class="flex justify-between py-1">
                                                        <span class="text-gray-500 dark:text-gray-400 capitalize">{{ str_replace('_', ' ', $key) }}:</span>
                                                        <span class="text-gray-900 dark:text-white font-medium">{{ $value }}</span>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                {{-- Priority Actions --}}
                @if(isset($recommendationsAnalysis->ai_result['priority_actions']) && !empty($recommendationsAnalysis->ai_result['priority_actions']))
                    <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                            Hành động Ưu tiên
                        </h4>
                        <div class="space-y-3">
                            @foreach($recommendationsAnalysis->ai_result['priority_actions'] as $index => $action)
                                <div class="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <div class="flex-shrink-0 w-6 h-6 bg-yellow-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                                        {{ $index + 1 }}
                                    </div>
                                    <span class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{{ $action }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                {{-- Performance Insights --}}
                @if(isset($recommendationsAnalysis->ai_result['performance_insights']) && !empty($recommendationsAnalysis->ai_result['performance_insights']))
                    <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                            Thông tin Hiệu suất
                        </h4>
                        <div class="space-y-3">
                            @foreach($recommendationsAnalysis->ai_result['performance_insights'] as $insight)
                                @if(is_array($insight))
                                    <div class="border-l-4 border-green-500 pl-3">
                                        <div class="font-medium text-sm text-gray-900 dark:text-white">
                                            {{ $insight['area'] ?? 'General' }}
                                        </div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                            {{ $insight['finding'] ?? $insight['insight'] ?? $insight['implication'] ?? '' }}
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif
            @else
                <p class="text-gray-500 dark:text-gray-400 italic">No specific recommendations available.</p>
            @endif

            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500 dark:text-gray-400">
                        Độ tin cậy: {{ $recommendationsAnalysis->confidence_score ?? 'Không có' }}%
                    </span>
                    <span class="text-gray-500 dark:text-gray-400">
                        {{ $recommendationsAnalysis->analyzed_at->format('M j, Y \a\t g:i A') }}
                    </span>
                </div>
            </div>
        </div>
    @endif

    @if($roiAnalysis)
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="flex items-center space-x-2 mb-4">
                <span class="text-xl">📈</span>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Phân tích Dự đoán ROI</h3>
                @if($roiAnalysis->isStale(24))
                    <x-filament::badge color="warning" size="sm">Cần cập nhật</x-filament::badge>
                @endif
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    @if(isset($roiAnalysis->ai_result['predicted_roi']))
                        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                                {{ $roiAnalysis->ai_result['predicted_roi'] }}%
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">ROI Dự đoán</div>
                        </div>
                    @endif

                    @if(isset($roiAnalysis->ai_result['risk_level']))
                        <div class="mt-4">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Mức độ Rủi ro:</span>
                            <x-filament::badge
                                :color="$roiAnalysis->ai_result['risk_level'] === 'low' ? 'success' : ($roiAnalysis->ai_result['risk_level'] === 'medium' ? 'warning' : 'danger')"
                                size="sm"
                            >
                                {{ ucfirst($roiAnalysis->ai_result['risk_level']) }}
                            </x-filament::badge>
                        </div>
                    @endif
                </div>

                <div>
                    @if(isset($roiAnalysis->ai_result['risk_factors']))
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">Yếu tố Rủi ro:</h4>
                        <ul class="space-y-2 text-sm">
                            @foreach(array_slice($roiAnalysis->ai_result['risk_factors'], 0, 4) as $factor)
                                <li class="flex items-start space-x-2">
                                    <span class="w-2 h-2 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></span>
                                    <span class="text-gray-600 dark:text-gray-400">{{ $factor }}</span>
                                </li>
                            @endforeach
                        </ul>
                    @endif

                    @if(isset($roiAnalysis->ai_result['recommendations']))
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2 mt-4">Recommendations:</h4>
                        <ul class="space-y-2 text-sm">
                            @foreach(array_slice($roiAnalysis->ai_result['recommendations'], 0, 3) as $recommendation)
                                <li class="flex items-start space-x-2">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></span>
                                    <span class="text-gray-600 dark:text-gray-400">{{ $recommendation }}</span>
                                </li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500 dark:text-gray-400">
                        Độ tin cậy: {{ $roiAnalysis->confidence_score ?? 'Không có' }}%
                    </span>
                    <span class="text-gray-500 dark:text-gray-400">
                        {{ $roiAnalysis->analyzed_at->format('M j, Y \a\t g:i A') }}
                    </span>
                </div>
            </div>
        </div>
    @endif

    @if($budgetAnalysis)
        <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="flex items-center space-x-2 mb-4">
                <span class="text-xl">💰</span>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Phân tích Tối ưu Ngân sách</h3>
                @if($budgetAnalysis->isStale(24))
                    <x-filament::badge color="warning" size="sm">Cần cập nhật</x-filament::badge>
                @endif
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    @if(isset($budgetAnalysis->ai_result['recommended_budget']))
                        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                            <div class="text-xl font-bold text-yellow-600 dark:text-yellow-400">
                                {{ number_format($budgetAnalysis->ai_result['recommended_budget']) }} VND
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Ngân sách Đề xuất</div>
                        </div>
                    @endif

                    @if(isset($budgetAnalysis->ai_result['expected_improvement']))
                        <div class="mt-4 text-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400">Cải thiện Dự kiến:</span>
                            <div class="text-lg font-semibold text-green-600 dark:text-green-400">
                                +{{ $budgetAnalysis->ai_result['expected_improvement'] }}%
                            </div>
                        </div>
                    @endif
                </div>

                <div>
                    @if(isset($budgetAnalysis->ai_result['optimization_suggestions']))
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">Optimization Suggestions:</h4>
                        <ul class="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                            @foreach(array_slice($budgetAnalysis->ai_result['optimization_suggestions'], 0, 4) as $suggestion)
                                <li class="flex items-center space-x-2">
                                    <span class="w-1.5 h-1.5 bg-yellow-500 rounded-full"></span>
                                    <span>{{ $suggestion }}</span>
                                </li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500 dark:text-gray-400">
                        Độ tin cậy: {{ $budgetAnalysis->confidence_score ?? 'Không có' }}%
                    </span>
                    <span class="text-gray-500 dark:text-gray-400">
                        {{ $budgetAnalysis->analyzed_at->format('M j, Y \a\t g:i A') }}
                    </span>
                </div>
            </div>
        </div>
    @endif

    {{-- No Analysis Available --}}
    @if(!$recommendationsAnalysis && !$roiAnalysis && !$budgetAnalysis)
        <div class="text-center py-12">
            <div class="text-6xl mb-4">🤖</div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Chưa có Phân tích AI</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6">
                Chiến dịch này chưa được phân tích. Nhấn "Làm mới Phân tích" để tạo thông tin chi tiết AI.
            </p>
        </div>
    @endif
</div>
