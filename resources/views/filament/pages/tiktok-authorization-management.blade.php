<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header Stats --}}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @php
                $totalAuthorizations = \App\Models\ExclusiveAuthorization::count();
                $effectiveAuthorizations = \App\Models\ExclusiveAuthorization::where('status', 'EFFECTIVE')->count();
                $unauthorizedAuthorizations = \App\Models\ExclusiveAuthorization::where('status', 'UNAUTHORIZED')->count();
                $shopsNeedingAuth = \App\Models\Shop::whereDoesntHave('exclusiveAuthorizations', function($q) {
                    $q->where('status', 'EFFECTIVE');
                })->count();
            @endphp

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <x-heroicon-o-key class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tổng authorizations</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalAuthorizations) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <x-heroicon-o-check-circle class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Authorizations hiệu lực</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($effectiveAuthorizations) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                        <x-heroicon-o-x-circle class="w-6 h-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Chưa được ủy quyền</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($unauthorizedAuthorizations) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <x-heroicon-o-building-storefront class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Shops cần authorization</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($shopsNeedingAuth) }}</p>
                    </div>
                </div>
            </div>
        </div>

        {{-- Grant Authorization Form --}}
        <x-filament::section>
            <x-slot name="heading">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-key class="w-5 h-5" />
                    Cấp Quyền Ủy Quyền Mới
                </div>
            </x-slot>

            <x-slot name="description">
                Cấp quyền exclusive authorization cho ad account để tạo GMV Max Campaigns
            </x-slot>

            <div class="space-y-4">
                {{ $this->form }}

                <div class="flex gap-3">
                    <x-filament::button
                        wire:click="grantNewAuthorization"
                        color="success"
                        icon="heroicon-o-key"
                        :disabled="$operationInProgress"
                    >
                        @if($operationInProgress)
                            <x-filament::loading-indicator class="h-4 w-4" />
                            Đang xử lý...
                        @else
                            Cấp Quyền
                        @endif
                    </x-filament::button>

                    <x-filament::button
                        wire:click="$refresh"
                        color="gray"
                        icon="heroicon-o-arrow-path"
                    >
                        Làm Mới
                    </x-filament::button>
                </div>
            </div>
        </x-filament::section>

        {{-- Quick Actions --}}
        <x-filament::section>
            <x-slot name="heading">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-bolt class="w-5 h-5" />
                    Thao Tác Nhanh
                </div>
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                            <x-heroicon-o-key class="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-green-900 dark:text-green-100">Cấp Quyền Hàng Loạt</h3>
                            <p class="text-sm text-green-700 dark:text-green-300">Cấp quyền cho nhiều shops</p>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                            <x-heroicon-o-arrow-path class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-blue-900 dark:text-blue-100">Đồng Bộ Tất Cả</h3>
                            <p class="text-sm text-blue-700 dark:text-blue-300">Cập nhật trạng thái từ API</p>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-purple-100 dark:bg-purple-800 rounded-lg">
                            <x-heroicon-o-clipboard-document-check class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-purple-900 dark:text-purple-100">Kiểm Tra Điều Kiện</h3>
                            <p class="text-sm text-purple-700 dark:text-purple-300">Validate shop eligibility</p>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-orange-100 dark:bg-orange-800 rounded-lg">
                            <x-heroicon-o-chart-bar class="w-5 h-5 text-orange-600 dark:text-orange-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-orange-900 dark:text-orange-100">Xem Thống Kê</h3>
                            <p class="text-sm text-orange-700 dark:text-orange-300">Chi tiết authorization status</p>
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>

        {{-- Authorization Status Overview --}}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <x-filament::section>
                <x-slot name="heading">
                    <div class="flex items-center gap-2 text-green-600">
                        <x-heroicon-o-check-circle class="w-5 h-5" />
                        Authorizations Hiệu Lực
                    </div>
                </x-slot>

                <div class="space-y-2">
                    @php
                        $effectiveAuths = \App\Models\ExclusiveAuthorization::where('status', 'EFFECTIVE')
                            ->with('shop')
                            ->latest()
                            ->take(5)
                            ->get();
                    @endphp

                    @forelse($effectiveAuths as $auth)
                        <div class="flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded">
                            <span class="text-sm font-medium">{{ $auth->shop?->name ?? 'N/A' }}</span>
                            <span class="text-xs text-green-600">{{ $auth->granted_at?->diffForHumans() }}</span>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500">Chưa có authorization hiệu lực</p>
                    @endforelse
                </div>
            </x-filament::section>

            <x-filament::section>
                <x-slot name="heading">
                    <div class="flex items-center gap-2 text-red-600">
                        <x-heroicon-o-x-circle class="w-5 h-5" />
                        Chưa Được Ủy Quyền
                    </div>
                </x-slot>

                <div class="space-y-2">
                    @php
                        $unauthorizedAuths = \App\Models\ExclusiveAuthorization::where('status', 'UNAUTHORIZED')
                            ->with('shop')
                            ->latest()
                            ->take(5)
                            ->get();
                    @endphp

                    @forelse($unauthorizedAuths as $auth)
                        <div class="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded">
                            <span class="text-sm font-medium">{{ $auth->shop?->name ?? 'N/A' }}</span>
                            <span class="text-xs text-red-600">{{ $auth->updated_at->diffForHumans() }}</span>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500">Không có authorization chưa được ủy quyền</p>
                    @endforelse
                </div>
            </x-filament::section>

            <x-filament::section>
                <x-slot name="heading">
                    <div class="flex items-center gap-2 text-blue-600">
                        <x-heroicon-o-building-storefront class="w-5 h-5" />
                        Shops Cần Authorization
                    </div>
                </x-slot>

                <div class="space-y-2">
                    @php
                        $shopsNeedingAuth = \App\Models\Shop::whereDoesntHave('exclusiveAuthorizations', function($q) {
                            $q->where('status', 'EFFECTIVE');
                        })->take(5)->get();
                    @endphp

                    @forelse($shopsNeedingAuth as $shop)
                        <div class="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                            <span class="text-sm font-medium">{{ $shop->name }}</span>
                            <span class="text-xs text-blue-600">{{ $shop->region }}</span>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500">Tất cả shops đã có authorization</p>
                    @endforelse
                </div>
            </x-filament::section>
        </div>

        {{-- Authorizations Table --}}
        <x-filament::section>
            <x-slot name="heading">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-table-cells class="w-5 h-5" />
                        Danh Sách Authorizations
                    </div>

                    <div class="flex items-center gap-2 text-sm text-gray-500">
                        <x-heroicon-o-clock class="w-4 h-4" />
                        Tự động làm mới mỗi 30 giây
                    </div>
                </div>
            </x-slot>

            {{ $this->table }}
        </x-filament::section>

        {{-- Help Section --}}
        <x-filament::section collapsible collapsed>
            <x-slot name="heading">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-question-mark-circle class="w-5 h-5" />
                    Hướng Dẫn Sử Dụng
                </div>
            </x-slot>

            <div class="prose dark:prose-invert max-w-none">
                <h4>Trạng Thái Authorization:</h4>
                <ul>
                    <li><strong>EFFECTIVE:</strong> Authorization đang có hiệu lực</li>
                    <li><strong>INEFFECTIVE:</strong> Authorization đã bị hủy hoặc không hiệu lực</li>
                    <li><strong>UNAUTHORIZED:</strong> Chưa được cấp quyền</li>
                </ul>

                <h4>Trạng Thái Advertiser:</h4>
                <ul>
                    <li><strong>STATUS_ENABLE:</strong> Advertiser đã được phê duyệt</li>
                    <li><strong>STATUS_PENDING_CONFIRM:</strong> Đang chờ xem xét</li>
                    <li><strong>STATUS_CONFIRM_FAIL:</strong> Không được phê duyệt</li>
                </ul>

                <h4>Quy Trình Cấp Quyền:</h4>
                <ol>
                    <li>Chọn shop cần cấp quyền (chỉ hiển thị shops đủ điều kiện)</li>
                    <li>Nhập Advertiser ID hợp lệ</li>
                    <li>Hệ thống sẽ gọi TikTok API để cấp quyền</li>
                    <li>Tự động đồng bộ trạng thái sau khi cấp quyền</li>
                </ol>

                <h4>Lưu Ý Quan Trọng:</h4>
                <ul>
                    <li>Mỗi shop chỉ có thể có một advertiser được cấp quyền độc quyền</li>
                    <li>Cần có quyền Business Center để thực hiện cấp quyền</li>
                    <li>Quá trình cấp quyền có thể mất vài phút để có hiệu lực</li>
                </ul>
            </div>
        </x-filament::section>
    </div>

    {{-- Loading Overlay --}}
    @if($operationInProgress)
        <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4">
                <div class="flex items-center gap-3">
                    <x-filament::loading-indicator class="h-6 w-6" />
                    <div>
                        <h3 class="font-medium">Đang xử lý authorization...</h3>
                        <p class="text-sm text-gray-500">Vui lòng đợi trong giây lát</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</x-filament-panels::page>
