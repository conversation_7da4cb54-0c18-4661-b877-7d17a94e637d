<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Header Stats --}}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @php
                $totalProducts = \App\Models\Product::count();
                $availableProducts = \App\Models\Product::where('api_status', 'AVAILABLE')->count();
                $gmvMaxProducts = \App\Models\Product::where('gmv_max_ads_status', 'UNOCCUPIED')->count();
                $lastUpdate = \App\Models\Product::latest('updated_at')->first()?->updated_at?->diffForHumans() ?? 'Chưa có';
            @endphp

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <x-heroicon-o-shopping-bag class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tổng sản phẩm</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($totalProducts) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <x-heroicon-o-check-circle class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Sản phẩm có sẵn</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($availableProducts) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <x-heroicon-o-star class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Sản phẩm GMV Max</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($gmvMaxProducts) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                        <x-heroicon-o-clock class="w-6 h-6 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Cập nhật gần nhất</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $lastUpdate }}</p>
                    </div>
                </div>
            </div>
        </div>

        {{-- Sync Configuration Form --}}
        <x-filament::section>
            <x-slot name="heading">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-cog-6-tooth class="w-5 h-5" />
                    Cấu Hình Đồng Bộ
                </div>
            </x-slot>

            <x-slot name="description">
                Cấu hình các tùy chọn để đồng bộ sản phẩm từ TikTok API vào hệ thống
            </x-slot>

            <div class="space-y-4">
                {{ $this->form }}

                <div class="flex gap-3">
                    <x-filament::button
                        wire:click="syncByConfiguration"
                        color="success"
                        icon="heroicon-o-play"
                        :disabled="$syncInProgress"
                    >
                        @if($syncInProgress)
                            <x-filament::loading-indicator class="h-4 w-4" />
                            Đang đồng bộ...
                        @else
                            Bắt Đầu Đồng Bộ
                        @endif
                    </x-filament::button>

                    <x-filament::button
                        wire:click="$refresh"
                        color="gray"
                        icon="heroicon-o-arrow-path"
                    >
                        Làm Mới
                    </x-filament::button>
                </div>
            </div>
        </x-filament::section>

        {{-- Quick Actions --}}
        <x-filament::section>
            <x-slot name="heading">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-bolt class="w-5 h-5" />
                    Thao Tác Nhanh
                </div>
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                            <x-heroicon-o-shopping-bag class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-blue-900 dark:text-blue-100">Đồng Bộ Tất Cả</h3>
                            <p class="text-sm text-blue-700 dark:text-blue-300">Đồng bộ sản phẩm từ tất cả shops</p>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                            <x-heroicon-o-star class="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-green-900 dark:text-green-100">GMV Max Only</h3>
                            <p class="text-sm text-green-700 dark:text-green-300">Chỉ sản phẩm eligible cho GMV Max</p>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-purple-100 dark:bg-purple-800 rounded-lg">
                            <x-heroicon-o-shopping-cart class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                            <h3 class="font-medium text-purple-900 dark:text-purple-100">Shopping Ads</h3>
                            <p class="text-sm text-purple-700 dark:text-purple-300">Chỉ sản phẩm cho Shopping Ads</p>
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>

        {{-- Products Table --}}
        <x-filament::section>
            <x-slot name="heading">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-table-cells class="w-5 h-5" />
                        Danh Sách Sản Phẩm
                    </div>

                    <div class="flex items-center gap-2 text-sm text-gray-500">
                        <x-heroicon-o-clock class="w-4 h-4" />
                        Tự động làm mới mỗi 30 giây
                    </div>
                </div>
            </x-slot>

            {{ $this->table }}
        </x-filament::section>

        {{-- Help Section --}}
        <x-filament::section collapsible collapsed>
            <x-slot name="heading">
                <div class="flex items-center gap-2">
                    <x-heroicon-o-question-mark-circle class="w-5 h-5" />
                    Hướng Dẫn Sử Dụng
                </div>
            </x-slot>

            <div class="prose dark:prose-invert max-w-none">
                <h4>Các Loại Đồng Bộ:</h4>
                <ul>
                    <li><strong>Tất cả sản phẩm:</strong> Đồng bộ toàn bộ sản phẩm từ TikTok Shop</li>
                    <li><strong>GMV Max:</strong> Chỉ đồng bộ sản phẩm eligible cho GMV Max Campaigns</li>
                    <li><strong>Shopping Ads:</strong> Chỉ đồng bộ sản phẩm eligible cho Shopping Ads</li>
                </ul>

                <h4>Trạng Thái Sản Phẩm:</h4>
                <ul>
                    <li><strong>AVAILABLE:</strong> Sản phẩm có sẵn để sử dụng</li>
                    <li><strong>NOT_AVAILABLE:</strong> Sản phẩm không có sẵn</li>
                    <li><strong>OCCUPIED:</strong> Đang được sử dụng trong GMV Max Campaign</li>
                    <li><strong>UNOCCUPIED:</strong> Chưa được sử dụng trong GMV Max Campaign</li>
                </ul>

                <h4>Lưu Ý:</h4>
                <ul>
                    <li>Quá trình đồng bộ có thể mất vài phút tùy thuộc vào số lượng sản phẩm</li>
                    <li>Hệ thống sẽ tự động xử lý lỗi permissions và sử dụng fallback data khi cần</li>
                    <li>Dữ liệu được cache để tối ưu performance</li>
                </ul>
            </div>
        </x-filament::section>
    </div>

    {{-- Loading Overlay --}}
    @if($syncInProgress)
        <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4">
                <div class="flex items-center gap-3">
                    <x-filament::loading-indicator class="h-6 w-6" />
                    <div>
                        <h3 class="font-medium">Đang đồng bộ sản phẩm...</h3>
                        <p class="text-sm text-gray-500">Vui lòng đợi trong giây lát</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</x-filament-panels::page>
