@php
    use App\Models\AiAnalysis;
    $campaign = $getRecord();

    // Calculate AI Score from current analyses
    $recommendationsAnalysis = $campaign->getCurrentAiAnalysis(AiAnalysis::TYPE_RECOMMENDATIONS);
    $roiAnalysis = $campaign->getCurrentAiAnalysis(AiAnalysis::TYPE_ROI_PREDICTION);
    $budgetAnalysis = $campaign->getCurrentAiAnalysis(AiAnalysis::TYPE_BUDGET_OPTIMIZATION);

    // Get AI Score from ROI analysis (which contains ai_score) or use cached service result
    $score = null;

    // First, try to get from ROI analysis (which has ai_score field)
    if ($roiAnalysis && isset($roiAnalysis->ai_result['ai_score'])) {
        $score = $roiAnalysis->ai_result['ai_score'];
    }

    // Fallback: try to get from AIScoringService cache
    if (!$score) {
        $cacheKey = "ai_score_campaign_{$campaign->id}";
        $cachedResult = cache()->get($cacheKey);
        $score = $cachedResult['ai_score'] ?? null;
    }

    // Last fallback: calculate basic score from available analyses
    if (!$score && ($recommendationsAnalysis || $roiAnalysis || $budgetAnalysis)) {
        $totalAnalyses = 0;
        $totalConfidence = 0;

        if ($recommendationsAnalysis) {
            $totalAnalyses++;
            $totalConfidence += ($recommendationsAnalysis->confidence_score ?? 0);
        }

        if ($roiAnalysis) {
            $totalAnalyses++;
            $totalConfidence += ($roiAnalysis->confidence_score ?? 0);
        }

        if ($budgetAnalysis) {
            $totalAnalyses++;
            $totalConfidence += ($budgetAnalysis->confidence_score ?? 0);
        }

        // Basic score calculation based on analysis availability and confidence
        if ($totalAnalyses > 0) {
            $avgConfidence = $totalConfidence / $totalAnalyses;
            $score = round(($totalAnalyses / 3) * 100 * $avgConfidence);
        }
    }

    // Determine score status and color
    if (is_numeric($score)) {
        $numericScore = (int) $score;
        if ($numericScore >= 80) {
            $color = 'success';
            $emoji = '🟢';
            $status = 'Excellent';
        } elseif ($numericScore >= 60) {
            $color = 'warning';
            $emoji = '🟡';
            $status = 'Good';
        } else {
            $color = 'danger';
            $emoji = '🔴';
            $status = 'Poor';
        }
    } else {
        $color = 'gray';
        $emoji = '⚪';
        $status = 'N/A';
        $numericScore = 0;
    }
@endphp

<div class="flex items-center justify-center space-x-1"
     x-data
     x-tooltip.raw="{{ is_numeric($score) ? 'AI Score: ' . $score . '/100 - ' . $status . ' performance' : 'No AI analysis available' }}">

    @if(is_numeric($score))
        <span class="text-sm">{{ $emoji }}</span>
        <x-filament::badge :color="$color" size="sm">
            {{ $score }}
        </x-filament::badge>
    @else
        <span class="text-sm">{{ $emoji }}</span>
        <x-filament::badge color="gray" size="sm">
            N/A
        </x-filament::badge>
    @endif
</div>
