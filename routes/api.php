<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

# TikTok Campaign API
Route::prefix('tiktok')->group(function () {
    Route::prefix('campaigns')->group(function () {
        Route::get('/', [App\Http\Controllers\API\TikTok\CampaignController::class, 'getCampaigns']);
        Route::get('/{campaign_id}/info', [App\Http\Controllers\API\TikTok\CampaignController::class, 'getCampaignInfo']);
    });
    Route::get('/test-connection', [App\Http\Controllers\API\TikTok\CampaignController::class, 'testConnection']);
});
