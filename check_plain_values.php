<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TikTokSettings;

echo "=== Check if values are plain text ===\n";

// Get raw values
$accessToken = TikTokSettings::where('key', 'api.access_token')->first();
$secret = TikTokSettings::where('key', 'api.secret')->first();

if ($accessToken) {
    $rawValue = $accessToken->getRawOriginal('value');
    echo "Access Token:\n";
    echo "  Full raw value: " . $rawValue . "\n";
    echo "  Length: " . strlen($rawValue) . "\n";
    echo "  Is encrypted flag: " . ($accessToken->is_encrypted ? 'YES' : 'NO') . "\n";
    echo "  Looks like fake data: " . (str_contains($rawValue, 'b8b8b8') ? 'YES' : 'NO') . "\n";
}

echo "\n";

if ($secret) {
    $rawValue = $secret->getRawOriginal('value');
    echo "Secret:\n";
    echo "  Full raw value: " . $rawValue . "\n";
    echo "  Length: " . strlen($rawValue) . "\n";
    echo "  Is encrypted flag: " . ($secret->is_encrypted ? 'YES' : 'NO') . "\n";
    echo "  Looks like fake data: " . (str_contains($rawValue, '0a0a0a') ? 'YES' : 'NO') . "\n";
}

echo "\n=== Check .env example values ===\n";
echo "From .env.example:\n";
echo "  TIKTOK_ACCESS_TOKEN=b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8\n";
echo "  TIKTOK_SECRET=0a0a0a0a0a0a0a0a0a0a0a0a0a0a0\n";

echo "\n=== Solution ===\n";
echo "The values in database are PLAIN TEXT fake values from .env.example\n";
echo "They are marked as encrypted but contain plain text fake data.\n";
echo "Need to either:\n";
echo "1. Set is_encrypted=false for these records\n";
echo "2. Or replace with real encrypted values\n";
