APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:N7bLBl0su5hfAWHu4ZsHcDKup1Xd6HoTBENrsUM1QR8=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel_tiktok_gmv_max_manager
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"

# TikTok Business API Configuration
TIKTOK_ACCESS_TOKEN=b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8
TIKTOK_APP_ID=686868686868686868
TIKTOK_SECRET=0a0a0a0a0a0a0a0a0a0a0a0a0a0a0
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

AI_ANALYSIS_ENABLED=true
AI_ANALYSIS_RECOMMENDATIONS_HOURS=24
AI_ANALYSIS_ROI_HOURS=12
AI_ANALYSIS_BUDGET_HOURS=6
AI_ANALYSIS_BATCH_SIZE=10
AI_ANALYSIS_MAX_TIME=300
