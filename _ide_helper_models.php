<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * @property int $id
 * @property string $advertiser_id TikTok Advertiser ID từ API
 * @property string $advertiser_name Tên tài khoản quảng cáo
 * @property string $status Trạng thái tài khoản
 * @property string|null $currency Tiền tệ mặc định
 * @property string|null $timezone Múi giờ
 * @property string|null $company_name Tên công ty
 * @property string|null $industry Ngành nghề
 * @property array|null $permissions Quyền hạn của tài khoản
 * @property string $balance Số dư tài khoản
 * @property \Illuminate\Support\Carbon|null $authorized_at Thời gian đ<PERSON>y quyền
 * @property \Illuminate\Support\Carbon|null $last_sync_at Lần sync cuối cùng
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Campaign> $campaigns
 * @property-read int|null $campaigns_count
 * @property-read string $formatted_balance
 * @property-read bool $is_recently_authorized
 * @property-read bool $needs_sync
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop> $shops
 * @property-read int|null $shops_count
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount active()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount byCurrency($currency)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount query()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereAdvertiserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereAdvertiserName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereAuthorizedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereIndustry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereLastSyncAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount wherePermissions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AdvertiserAccount withoutTrashed()
 */
	class AdvertiserAccount extends \Eloquent {}
}

namespace App\Models{
/**
 * Model Campaign - Quản lý chiến dịch GMV Max
 *
 * @property string $campaign_id Campaign ID từ TikTok API
 * @property string $name Tên chiến dịch
 * @property string $status Trạng thái chiến dịch
 * @property float $target_roi Target ROI (%)
 * @property float $budget Ngân sách tổng
 * @property float $daily_budget Ngân sách hàng ngày
 * @property Carbon $start_date Ngày bắt đầu
 * @property Carbon $end_date Ngày kết thúc
 * @property string $advertiser_id Advertiser ID
 * @property int $shop_id ID cửa hàng
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read int|null $days_remaining
 * @property-read bool $is_running
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\CampaignReport> $reports
 * @property-read int|null $reports_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Session> $sessions
 * @property-read int|null $sessions_count
 * @property-read \App\Models\Shop $shop
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Video> $videos
 * @property-read int|null $videos_count
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign active()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign byShop($shopId)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign query()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign running()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereAdvertiserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereBudget($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDailyBudget($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereShopId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereTargetRoi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Campaign withoutTrashed()
 */
	class Campaign extends \Eloquent {}
}

namespace App\Models{
/**
 * Model CampaignReport - Báo cáo hiệu suất chiến dịch
 *
 * @property string $report_id Report ID từ TikTok API
 * @property int $campaign_id ID chiến dịch
 * @property Carbon $report_date Ngày báo cáo
 * @property float $total_cost Chi phí tổng
 * @property int $orders_count Số đơn hàng
 * @property float $gross_revenue Doanh thu tổng
 * @property float $cost_per_order Chi phí trên mỗi đơn hàng
 * @property float $roi Return on Investment (%)
 * @property int $impressions Số lượt hiển thị
 * @property int $clicks Số lượt click
 * @property float $ctr Click-through rate (%)
 * @property float $conversion_rate Tỷ lệ chuyển đổi (%)
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Campaign $campaign
 * @property-read string $formatted_cost
 * @property-read string $formatted_profit
 * @property-read string $formatted_revenue
 * @property-read float|null $growth_rate
 * @property-read bool $is_profitable
 * @property-read float $profit
 * @property-read float $roas
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport byCampaign($campaignId)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport byDateRange(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport byMonth(int $year, int $month)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport goodPerformance()
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport highRoi($minRoi = 100)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport query()
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport recent(int $days = 30)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereClicks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereConversionRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereCostPerOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereCtr($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereGrossRevenue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereImpressions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereOrdersCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereReportDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereReportId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereRoi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereTotalCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CampaignReport whereUpdatedAt($value)
 */
	class CampaignReport extends \Eloquent {}
}

namespace App\Models{
/**
 * Model ExclusiveAuthorization - Quản lý quyền ủy quyền độc quyền
 *
 * @property string $authorization_id Authorization ID từ TikTok API
 * @property int $shop_id ID cửa hàng
 * @property string $advertiser_id Advertiser ID
 * @property string $status Trạng thái ủy quyền
 * @property Carbon $granted_at Ngày cấp quyền
 * @property Carbon $expires_at Ngày hết hạn
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read int|null $days_remaining
 * @property-read string $display_status
 * @property-read bool $is_active
 * @property-read bool $is_expired
 * @property-read \App\Models\Shop $shop
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization active()
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization byAdvertiser($advertiserId)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization expired()
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization expiringSoon($days = 7)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization granted()
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization query()
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereAdvertiserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereAuthorizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereGrantedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereShopId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExclusiveAuthorization whereUpdatedAt($value)
 */
	class ExclusiveAuthorization extends \Eloquent {}
}

namespace App\Models{
/**
 * Model Identity - Quản lý identity cho GMV Max
 *
 * @property string $identity_id Identity ID từ TikTok API
 * @property string $name Tên identity
 * @property string $type Loại identity
 * @property string $status Trạng thái identity
 * @property string $advertiser_id Advertiser ID liên kết
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $display_name
 * @method static \Illuminate\Database\Eloquent\Builder|Identity active()
 * @method static \Illuminate\Database\Eloquent\Builder|Identity byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity byType($type)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Identity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Identity query()
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereAdvertiserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereIdentityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Identity whereUpdatedAt($value)
 */
	class Identity extends \Eloquent {}
}

namespace App\Models{
/**
 * Model Product - Quản lý sản phẩm của shop
 *
 * @property string $product_id Product ID từ TikTok API
 * @property int $shop_id ID cửa hàng
 * @property string $name Tên sản phẩm
 * @property float $price Giá sản phẩm
 * @property string $status Trạng thái sản phẩm
 * @property string $category Danh mục sản phẩm
 * @property bool $is_occupied Đã được sử dụng trong quảng cáo
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_price
 * @property-read \App\Models\Shop $shop
 * @method static \Illuminate\Database\Eloquent\Builder|Product active()
 * @method static \Illuminate\Database\Eloquent\Builder|Product available()
 * @method static \Illuminate\Database\Eloquent\Builder|Product byCategory($category)
 * @method static \Illuminate\Database\Eloquent\Builder|Product byPriceRange($minPrice, $maxPrice)
 * @method static \Illuminate\Database\Eloquent\Builder|Product byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereIsOccupied($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereShopId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereUpdatedAt($value)
 */
	class Product extends \Eloquent {}
}

namespace App\Models{
/**
 * Model Session - Quản lý sessions trong campaign
 *
 * @property string $session_id Session ID từ TikTok API
 * @property int $campaign_id ID chiến dịch
 * @property string $name Tên session
 * @property string $status Trạng thái session
 * @property string $delivery_type Loại triển khai
 * @property float $budget Ngân sách session
 * @property Carbon $start_time Thời gian bắt đầu
 * @property Carbon $end_time Thời gian kết thúc
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Campaign $campaign
 * @property-read bool $is_running
 * @property-read int|null $minutes_remaining
 * @method static \Illuminate\Database\Eloquent\Builder|Session active()
 * @method static \Illuminate\Database\Eloquent\Builder|Session byDeliveryType($deliveryType)
 * @method static \Illuminate\Database\Eloquent\Builder|Session byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|Session newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Session newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Session query()
 * @method static \Illuminate\Database\Eloquent\Builder|Session running()
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereBudget($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereDeliveryType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereSessionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Session whereUpdatedAt($value)
 */
	class Session extends \Eloquent {}
}

namespace App\Models{
/**
 * Model Shop - Quản lý cửa hàng TikTok
 *
 * @property string $shop_id TikTok Shop ID từ API
 * @property string $name Tên cửa hàng
 * @property string $status Trạng thái cửa hàng
 * @property bool $is_eligible_gmv_max Đủ điều kiện GMV Max
 * @property string $region Khu vực
 * @property string $currency Tiền tệ
 * @property string $advertiser_id Advertiser ID từ TikTok
 * @property string $exclusive_authorization_status Trạng thái ủy quyền độc quyền
 * @property int $id
 * @property int|null $advertiser_account_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $store_authorized_bc_id ID của Business Center được ủy quyền truy cập shop
 * @property bool $is_owner_bc BC có sở hữu shop không
 * @property string|null $bc_id ID của Business Center
 * @property string|null $bc_name Tên Business Center
 * @property string|null $bc_profile_image URL ảnh profile của BC
 * @property string|null $user_role Vai trò user trong BC
 * @property string|null $store_code Mã code của TikTok Shop
 * @property string|null $thumbnail_url URL thumbnail của shop
 * @property string|null $store_role Quyền của BC user đối với shop
 * @property string|null $advertiser_name Tên advertiser account
 * @property array|null $targeting_region_codes Danh sách mã vùng shop có thể target
 * @property-read \App\Models\AdvertiserAccount|null $advertiserAccount
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Campaign> $campaigns
 * @property-read int|null $campaigns_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ExclusiveAuthorization> $exclusiveAuthorizations
 * @property-read int|null $exclusive_authorizations_count
 * @property-read string $display_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Product> $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|Shop byRegion($region)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop eligibleForGmvMax()
 * @method static \Illuminate\Database\Eloquent\Builder|Shop newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Shop newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Shop onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Shop query()
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereAdvertiserAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereAdvertiserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereAdvertiserName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereBcId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereBcName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereBcProfileImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereExclusiveAuthorizationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereIsEligibleGmvMax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereIsOwnerBc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereRegion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereShopId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereStoreAuthorizedBcId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereStoreCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereStoreRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereTargetingRegionCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereThumbnailUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop whereUserRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shop withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Shop withoutTrashed()
 */
	class Shop extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $key Configuration key identifier
 * @property string|null $value Configuration value (encrypted for sensitive data)
 * @property string $type Data type: string, boolean, integer, json
 * @property bool $is_encrypted Whether the value is encrypted
 * @property bool $is_public Whether the setting can be displayed in UI
 * @property string|null $description Human-readable description
 * @property string $group Settings group for organization
 * @property array|null $validation_rules Validation rules for the setting
 * @property string|null $default_value Default value if not set
 * @property int|null $updated_by User who last updated this setting
 * @property \Illuminate\Support\Carbon|null $last_tested_at Last time this setting was tested
 * @property array|null $test_result Result of last test
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $decrypted_value
 * @property-read \App\Models\User|null $updatedBy
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings group(string $group)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings public()
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings query()
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereDefaultValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereIsEncrypted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereIsPublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereLastTestedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereTestResult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereValidationRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TikTokSettings whereValue($value)
 */
	class TikTokSettings extends \Eloquent {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property mixed $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 */
	class User extends \Eloquent {}
}

namespace App\Models{
/**
 * Model Video - Quản lý video content cho campaigns
 *
 * @property string $video_id Video ID từ TikTok API
 * @property int $campaign_id ID chiến dịch
 * @property string $title Tiêu đề video
 * @property string $url URL video
 * @property string $thumbnail Đường dẫn thumbnail
 * @property int $duration Thời lượng video (giây)
 * @property string $status Trạng thái video
 * @property bool $is_custom_anchor Là custom anchor video
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Campaign $campaign
 * @property-read string $formatted_duration
 * @property-read string|null $thumbnail_url
 * @method static \Illuminate\Database\Eloquent\Builder|Video active()
 * @method static \Illuminate\Database\Eloquent\Builder|Video byCampaign($campaignId)
 * @method static \Illuminate\Database\Eloquent\Builder|Video byDurationRange($minDuration, $maxDuration)
 * @method static \Illuminate\Database\Eloquent\Builder|Video byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder|Video customAnchor()
 * @method static \Illuminate\Database\Eloquent\Builder|Video newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Video newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Video query()
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereIsCustomAnchor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereThumbnail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Video whereVideoId($value)
 */
	class Video extends \Eloquent {}
}

