# TikTok Store Products API Permissions Guide

## Vấn đề

Khi sử dụng TikTok Store Products API endpoint `/store/product/get/`, bạn có thể gặp lỗi:

```
Error Code: 40001
Message: "advertiser does not grant you /store/product/get/:GET permission"
```

## Nguyên nhân

Endpoint `/store/product/get/` yêu cầu permissions đặc biệt khác với các GMV Max endpoints thông thường:

1. **Store Management Permissions**: Quyền truy cập quản lý cửa hàng
2. **Product Management Permissions**: Quyền truy cập quản lý sản phẩm
3. **Business Center Authorization**: Quyền truy cập từ Business Center

## Giải pháp

### 1. Liên hệ TikTok Business Support

**Cách thực hiện:**
1. Truy cập [TikTok Business Help Center](https://ads.tiktok.com/help/)
2. Tạo support ticket với thông tin:
   - **Subject**: Request Store Product API Access
   - **App ID**: [Your TikTok App ID]
   - **Advertiser ID**: [Your Advertiser ID]
   - **Business Center ID**: [Your BC ID]
   - **Use Case**: Product management for GMV Max campaigns

**Template email:**
```
Subject: Request Store Product API Access for GMV Max Integration

Dear TikTok Business Support,

We are developing a GMV Max campaign management system and need access to the Store Products API endpoint (/store/product/get/).

Our application details:
- App ID: [YOUR_APP_ID]
- Advertiser ID: [YOUR_ADVERTISER_ID]
- Business Center ID: [YOUR_BC_ID]
- Use Case: Synchronizing store products for GMV Max campaign optimization

Currently receiving error 40001: "advertiser does not grant you /store/product/get/:GET permission"

Please grant the following permissions:
- store_management
- product_management

Thank you for your assistance.
```

### 2. Verify Business Center Permissions

**Kiểm tra quyền trong Business Center:**
1. Đăng nhập vào [TikTok Business Center](https://business.tiktok.com/)
2. Vào **Settings** > **User Management**
3. Kiểm tra role của user có quyền:
   - Store Management
   - Product Management
   - API Access

### 3. Update API Scopes

**Cập nhật scopes trong TikTok Developer Portal:**
1. Truy cập [TikTok for Developers](https://developers.tiktok.com/)
2. Vào app của bạn
3. Thêm scopes:
   ```
   store_management
   product_management
   business_management
   ```

## Fallback Solution

Hệ thống đã implement fallback mechanism khi không có permissions:

### Automatic Fallback
- Khi gặp permission error, hệ thống tự động chuyển sang sử dụng occupied ads endpoint
- Dữ liệu sẽ bị hạn chế nhưng vẫn có thể sync một phần products

### Limitations của Fallback
- Chỉ hiển thị products đang được sử dụng trong ads
- Không có thông tin historical sales
- Không có đầy đủ product details
- Không có price information

## Monitoring & Troubleshooting

### Check Logs
```bash
tail -f storage/logs/laravel.log | grep "TikTok API"
```

### Test Permissions
```bash
php artisan tiktok:sync-products --shop-id=YOUR_SHOP_ID
```

### Verify API Configuration
```bash
php artisan tinker
>>> $api = app(\App\Services\TikTok\TikTokApiService::class);
>>> $result = $api->testConnection();
>>> dd($result);
```

## Error Codes Reference

| Code | Message | Solution |
|------|---------|----------|
| 40001 | Permission denied | Request store management permissions |
| 40002 | Invalid advertiser | Verify advertiser ID |
| 40003 | Invalid business center | Check BC permissions |
| 40004 | Store not found | Verify store ID |

## Best Practices

1. **Always handle permission errors gracefully**
2. **Use fallback data when available**
3. **Monitor logs for permission issues**
4. **Keep documentation updated with latest permissions**
5. **Test permissions regularly**

## Contact Information

- **TikTok Business Support**: [ads.tiktok.com/help](https://ads.tiktok.com/help/)
- **Developer Portal**: [developers.tiktok.com](https://developers.tiktok.com/)
- **Business Center**: [business.tiktok.com](https://business.tiktok.com/)

## Implementation Notes

Hệ thống đã được cập nhật với:
- ✅ Automatic permission error detection
- ✅ Fallback to occupied ads endpoint
- ✅ Detailed error messages with suggestions
- ✅ Graceful degradation of functionality
- ✅ Comprehensive logging

Khi permissions được cấp, hệ thống sẽ tự động chuyển về sử dụng direct API endpoint.
