# TikTok API Fields Update Documentation

## Overview

Updated the TikTok Shop synchronization to handle **ALL** fields from the official TikTok API response, based on the complete API documentation provided. Previously, the system was only handling a subset of available fields.

## API Response Structure

Based on TikTok's official documentation for `/gmv_max/store/list/` endpoint:

```json
{
    "code": 0,
    "message": "OK",
    "request_id": "{{request_id}}",
    "data": {
        "store_list": [
            {
                "store_id": "{{store_id}}",
                "store_name": "{{store_name}}",
                "store_code": "{{store_code}}",
                "store_status": "ACTIVE|INACTIVE|NEW_CREATE",
                "store_role": "AD_PROMOTION|MANAGER|UNSET",
                "is_gmv_max_available": true|false,
                "is_owner_bc": true|false,
                "thumbnail_url": "{{thumbnail_url}}",
                "targeting_region_codes": ["VN", "TH", "MY"],
                "store_authorized_bc_id": "{{store_authorized_bc_id}}",
                "store_authorized_bc_info": {
                    "bc_id": "{{bc_id}}",
                    "bc_name": "{{bc_name}}",
                    "bc_profile_image": "{{bc_profile_image}}",
                    "user_role": "ADMIN|STANDARD"
                },
                "exclusive_authorized_advertiser_info": {
                    "advertiser_id": "{{advertiser_id}}",
                    "advertiser_name": "{{advertiser_name}}",
                    "advertiser_status": "STATUS_ENABLE|STATUS_PENDING_CONFIRM|..."
                }
            }
        ]
    }
}
```

## Database Schema Updates

### New Fields Added

**Business Center Information:**
- `store_authorized_bc_id` - ID của Business Center được ủy quyền
- `is_owner_bc` - BC có sở hữu shop không (boolean)
- `bc_id` - ID của Business Center
- `bc_name` - Tên Business Center
- `bc_profile_image` - URL ảnh profile của BC
- `user_role` - Vai trò user trong BC (ADMIN|STANDARD)

**Shop Details:**
- `store_code` - Mã code của TikTok Shop
- `thumbnail_url` - URL thumbnail của shop
- `store_role` - Quyền của BC user đối với shop (AD_PROMOTION|MANAGER|UNSET)

**Advertiser Information:**
- `advertiser_name` - Tên advertiser account

**Enhanced Fields:**
- `targeting_region_codes` - JSON array thay vì single region
- `exclusive_authorization_status` - Thêm 'suspended' status

## Status Mapping Updates

### Store Status Mapping
```php
'ACTIVE' => 'active',
'INACTIVE' => 'inactive', 
'NEW_CREATE' => 'pending'
```

### Advertiser Status Mapping
```php
'STATUS_ENABLE' => 'granted',
'STATUS_CONFIRM_FAIL' => 'revoked',
'STATUS_PENDING_CONFIRM' => 'pending',
'STATUS_PENDING_CONFIRM_MODIFY' => 'pending',
'STATUS_PENDING_VERIFIED' => 'pending',
'STATUS_LIMIT' => 'suspended',
'STATUS_CONTRACT_PENDING' => 'pending',
'STATUS_DISABLE' => 'revoked',
'STATUS_SELF_SERVICE_UNAUDITED' => 'pending',
'STATUS_WAIT_FOR_BPM_AUDIT' => 'pending',
'STATUS_CONFIRM_FAIL_END' => 'revoked',
'STATUS_CONFIRM_MODIFY_FAIL' => 'revoked'
```

## Code Changes

### 1. Database Migrations
- `2025_07_23_104200_add_missing_fields_to_tiktok_shops_table.php`
- `2025_07_23_104353_update_exclusive_authorization_status_enum.php`

### 2. Model Updates
**Shop.php:**
- Added new fillable fields
- Updated casts for boolean and array fields

### 3. Service Updates
**TikTokSyncService.php:**
- Enhanced `syncShops()` method to map all API fields
- Updated `mapShopStatus()` for TikTok API status values
- Updated `mapAuthStatus()` for comprehensive advertiser status mapping

### 4. Resource Updates
**ShopResource.php:**
- Added Business Center information section in form
- Added Shop Details section with new fields
- Enhanced table columns with new data
- Updated status colors and formatting

## UI Enhancements

### Form Sections
1. **Basic Information** - Shop name, ID, status, GMV Max eligibility
2. **Business Center Information** - BC details, ownership, roles (collapsible)
3. **Shop Details** - Store code, thumbnail, targeting regions (collapsible)

### Table Columns
- **Visible by default**: Name, Shop ID, Status, GMV Max, Region, Currency, Advertiser Account, Authorization Status
- **Toggleable (hidden by default)**: Advertiser Name, Advertiser ID, BC Name, BC Owner, Store Role

## Benefits

### 1. Complete Data Coverage
- **Before**: ~40% of API fields captured
- **After**: 100% of API fields captured and stored

### 2. Enhanced Business Logic
- Better understanding of BC ownership relationships
- Comprehensive advertiser authorization tracking
- Multi-region targeting support

### 3. Improved User Experience
- More detailed shop information in admin interface
- Better filtering and search capabilities
- Comprehensive status tracking

### 4. Future-Proof Architecture
- Ready for additional TikTok API features
- Extensible field mapping system
- Comprehensive status handling

## Migration Guide

### For Existing Data
1. Run migrations to add new fields
2. Existing shops will have NULL values for new fields
3. Next sync will populate all new fields from TikTok API

### For Developers
1. New fields are available in Shop model
2. Use proper relationships for BC and advertiser data
3. Leverage enhanced status mapping for business logic

## Testing

Comprehensive mapping test confirms:
- ✅ All API fields properly mapped
- ✅ Status enums correctly converted
- ✅ Nested object data extracted
- ✅ Array fields properly handled
- ✅ Backward compatibility maintained

## API Documentation Reference

This implementation follows TikTok's official API documentation for:
- **Endpoint**: `/gmv_max/store/list/`
- **Version**: Latest (as of implementation date)
- **Fields**: All documented response fields implemented

## Conclusion

The system now captures and utilizes **100% of available TikTok API data** for shops, providing a complete foundation for GMV Max campaign management and business intelligence.
