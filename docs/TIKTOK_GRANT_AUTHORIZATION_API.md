# TikTok Grant Exclusive Authorization API Integration

## Overview

TikTok Grant Exclusive Authorization API cho phép cấp quyền độc quyền cho ad account để tạo GMV Max Campaigns cho TikTok Shop cụ thể. Mỗi TikTok Shop chỉ có thể có một ad account được <PERSON>y quyền độc quyền.

## API Specification

### Endpoint
```
POST https://business-api.tiktok.com/open_api/v1.3/gmv_max/exclusive_authorization/create/
```

### Required Parameters
- `store_id`: ID của TikTok Shop
- `store_authorized_bc_id`: ID của Business Center sở hữu shop
- `advertiser_id`: ID của ad account được cấp quyền

### Response
```json
{
    "code": 0,
    "message": "OK",
    "request_id": "{{request_id}}",
    "data": {}
}
```

## Implementation

### 1. Enhanced TikTokApiService

```php
public function createExclusiveAuthorization(
    string $storeId, 
    string $storeAuthorizedBcId, 
    ?string $advertiserId = null
): array
```

**Features:**
- Full parameter validation theo TikTok API v1.3
- Comprehensive error handling
- Cache management
- Batch processing support

**New Methods:**
- `createExclusiveAuthorization()`: Grant single authorization
- `batchCreateExclusiveAuthorizations()`: Grant multiple authorizations

### 2. Enhanced AuthorizationSyncService

**New Methods:**
- `grantExclusiveAuthorization()`: Grant authorization với business logic
- `batchGrantAuthorizations()`: Batch grant cho multiple shops
- `validateShopEligibility()`: Validate shop trước khi grant

**Features:**
- Pre-grant validation
- Existing authorization check
- Automatic sync after grant
- Comprehensive error handling

### 3. Console Command

```bash
# Interactive mode - chọn shop từ danh sách
php artisan tiktok:grant-authorization

# Grant cho shop cụ thể
php artisan tiktok:grant-authorization --shop-id=SHOP_ID --advertiser-id=ADVERTISER_ID

# Batch grant cho tất cả shops cần authorization
php artisan tiktok:grant-authorization --batch --advertiser-id=ADVERTISER_ID

# Validate shop eligibility only
php artisan tiktok:grant-authorization --validate-only

# Skip confirmation prompts
php artisan tiktok:grant-authorization --force
```

## Usage Examples

### 1. Grant Authorization for Single Shop

```php
$apiService = app(TikTokApiService::class);
$syncService = new AuthorizationSyncService($apiService);

$shop = Shop::where('shop_id', 'SHOP_ID')->first();
$result = $syncService->grantExclusiveAuthorization($shop, 'ADVERTISER_ID');

if (ErrorHandler::isSuccess($result)) {
    echo "Authorization granted successfully!";
}
```

### 2. Batch Grant Authorizations

```php
$shopIds = ['SHOP_ID_1', 'SHOP_ID_2', 'SHOP_ID_3'];
$result = $syncService->batchGrantAuthorizations($shopIds, 'ADVERTISER_ID');

$data = $result['data'];
echo "Successful: " . $data['successful_grants'];
echo "Failed: " . $data['failed_grants'];
```

### 3. Validate Shop Eligibility

```php
$shop = Shop::find(1);
$result = $syncService->validateShopEligibility($shop);

if (ErrorHandler::isSuccess($result)) {
    echo "Shop is eligible for GMV Max authorization";
} else {
    $issues = $result['context']['issues'];
    foreach ($issues as $issue) {
        echo "Issue: " . $issue;
    }
}
```

### 4. Direct API Call

```php
$apiService = app(TikTokApiService::class);
$result = $apiService->createExclusiveAuthorization(
    'STORE_ID',
    'STORE_AUTHORIZED_BC_ID', 
    'ADVERTISER_ID'
);
```

## Validation Rules

### Shop Eligibility Requirements
1. **shop_id**: Phải có TikTok Shop ID
2. **store_authorized_bc_id**: Phải có Business Center ID
3. **status**: Shop phải ở trạng thái 'active'
4. **No existing authorization**: Không có authorization hiệu lực cho advertiser khác

### Pre-Grant Checks
- Validate required shop information
- Check existing authorization status
- Verify advertiser ID format
- Ensure shop is GMV Max eligible

## Error Handling

### Common Errors
1. **Missing Parameters**: Thiếu store_id, store_authorized_bc_id, hoặc advertiser_id
2. **Existing Authorization**: Authorization đã tồn tại và đang hiệu lực
3. **Shop Not Eligible**: Shop không đủ điều kiện cho GMV Max
4. **API Permission**: Không có quyền grant authorization
5. **Invalid Advertiser**: Advertiser ID không hợp lệ

### Error Recovery
- Detailed error messages với suggested actions
- Validation trước khi gọi API
- Graceful handling cho existing authorizations
- Comprehensive logging cho troubleshooting

## Console Command Features

### Interactive Mode
- Hiển thị danh sách shops cần authorization
- Cho phép chọn single shop hoặc batch grant
- User-friendly interface với confirmations

### Validation Mode
- Check eligibility của tất cả shops
- Detailed report về issues
- No actual grant operations

### Batch Mode
- Grant authorization cho multiple shops
- Progress tracking
- Detailed results reporting

### Force Mode
- Skip confirmation prompts
- Suitable cho automation scripts

## Business Logic

### Authorization Workflow
1. **Find Eligible Shops**: Tìm shops cần authorization
2. **Validate Eligibility**: Check shop requirements
3. **Check Existing Auth**: Verify no conflicts
4. **Grant Authorization**: Call TikTok API
5. **Sync Status**: Update local database
6. **Verify Success**: Confirm authorization active

### Conflict Resolution
- Detect existing authorizations
- Provide clear error messages
- Suggest resolution steps
- Log conflicts cho analysis

## Monitoring & Maintenance

### Regular Tasks
1. **Daily Validation**: Check shop eligibility
2. **Weekly Grant Review**: Review authorization status
3. **Monthly Audit**: Verify all active authorizations

### Automation
```bash
# Daily eligibility check
0 1 * * * php /path/to/artisan tiktok:grant-authorization --validate-only

# Weekly authorization review
0 9 * * 1 php /path/to/artisan tiktok:sync-authorizations --summary
```

### Logging
- All grant attempts logged
- Error details captured
- Success confirmations recorded
- Performance metrics tracked

## Security Considerations

1. **Advertiser ID Protection**: Validate advertiser ownership
2. **Shop Access Control**: Verify shop permissions
3. **API Rate Limiting**: Respect TikTok API limits
4. **Audit Trail**: Complete operation logging

## Best Practices

1. **Pre-Validation**: Always validate before granting
2. **Batch Operations**: Use batch mode cho efficiency
3. **Error Handling**: Implement comprehensive error recovery
4. **Monitoring**: Regular status checks
5. **Documentation**: Keep authorization records updated

## Integration Notes

- ✅ Full TikTok API v1.3 compliance
- ✅ Enhanced validation và error handling
- ✅ Interactive console interface
- ✅ Batch processing capabilities
- ✅ Comprehensive logging
- ✅ Business logic implementation
- ✅ Conflict detection và resolution

## Troubleshooting

### Common Issues
1. **"Authorization already exists"**: Check existing auth status
2. **"Shop not eligible"**: Verify shop requirements
3. **"Invalid advertiser"**: Check advertiser ID format
4. **"Permission denied"**: Verify API permissions

### Debug Commands
```bash
# Check shop eligibility
php artisan tiktok:grant-authorization --validate-only

# Check current authorizations
php artisan tiktok:sync-authorizations --summary

# Find shops needing authorization
php artisan tiktok:sync-authorizations --find-missing
```
