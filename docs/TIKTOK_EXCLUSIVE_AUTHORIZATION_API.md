# TikTok Exclusive Authorization API Integration

## Overview

TikTok Exclusive Authorization API cho phép kiểm tra trạng thái <PERSON>y quyền độc quyền của ad account để tạo GMV Max Campaigns cho TikTok Shop cụ thể.

## API Specification

### Endpoint
```
GET https://business-api.tiktok.com/open_api/v1.3/gmv_max/exclusive_authorization/get/
```

### Required Parameters
- `store_id`: ID của TikTok Shop
- `store_authorized_bc_id`: ID của Business Center được ủy quyền truy cập shop
- `advertiser_id`: ID của ad account cần kiểm tra

### Response Fields
- `authorization_status`: EFFECTIVE | INEFFECTIVE | UNAUTHORIZED
- `advertiser_name`: Tên ad account (nếu có authorization)
- `advertiser_status`: Trạng thái ad account
- `identity_id`: ID của official TikTok account (nếu có)

## Implementation

### 1. Enhanced TikTokApiService

```php
public function getExclusiveAuthorization(
    string $storeId, 
    string $storeAuthorizedBcId, 
    ?string $advertiserId = null
): array
```

**Features:**
- Full parameter compliance với TikTok API v1.3
- Batch processing cho multiple shops
- Comprehensive error handling
- Caching với appropriate TTL

### 2. Updated ExclusiveAuthorization Model

**New Fields:**
- `store_id`: TikTok Store ID
- `store_authorized_bc_id`: Business Center ID
- `advertiser_name`: Advertiser account name
- `advertiser_status`: Advertiser account status
- `identity_id`: Official TikTok account identity

**Enhanced Status Handling:**
- Support cho TikTok API v1.3 status values
- Business methods: `isEffective()`, `isIneffective()`, `isUnauthorized()`
- Display methods với Vietnamese translations

### 3. AuthorizationSyncService

**Core Methods:**
- `syncShopAuthorization()`: Sync single shop
- `syncAllShopsAuthorizations()`: Sync all shops
- `batchSyncAuthorizations()`: Sync multiple shops
- `getAuthorizationSummary()`: Statistics overview

**Features:**
- Comprehensive error handling
- Detailed logging
- Business logic cho status mapping
- Performance optimized queries

### 4. Console Command

```bash
# Sync all shops
php artisan tiktok:sync-authorizations

# Sync specific shop
php artisan tiktok:sync-authorizations --shop-id=SHOP_ID --advertiser-id=ADVERTISER_ID

# Show summary
php artisan tiktok:sync-authorizations --summary

# Find shops needing authorization
php artisan tiktok:sync-authorizations --find-missing
```

## Usage Examples

### 1. Sync Authorization for Single Shop

```php
$apiService = app(TikTokApiService::class);
$syncService = new AuthorizationSyncService($apiService);

$shop = Shop::where('shop_id', 'SHOP_ID')->first();
$result = $syncService->syncShopAuthorization($shop, 'ADVERTISER_ID');

if (ErrorHandler::isSuccess($result)) {
    $authorization = $result['data']['authorization'];
    echo "Status: " . $authorization->status;
}
```

### 2. Get Authorization Summary

```php
$result = $syncService->getAuthorizationSummary();
$stats = $result['data']['data'];

echo "Total: " . $stats['total_authorizations'];
echo "Effective: " . $stats['effective_authorizations'];
```

### 3. Find Shops Needing Authorization

```php
$result = $syncService->findShopsNeedingAuthorization();
$shops = $result['data']['shops_needing_authorization'];

foreach ($shops as $shop) {
    echo "Shop {$shop->name} needs authorization";
}
```

## Status Mapping

### Authorization Status
- `EFFECTIVE`: Có hiệu lực
- `INEFFECTIVE`: Không hiệu lực  
- `UNAUTHORIZED`: Chưa được ủy quyền

### Advertiser Status
- `STATUS_ENABLE`: Đã phê duyệt
- `STATUS_CONFIRM_FAIL`: Không được phê duyệt
- `STATUS_PENDING_CONFIRM`: Đang xem xét
- `STATUS_LIMIT`: Bị tạm ngưng
- `STATUS_DISABLE`: Bị vô hiệu hóa

## Database Schema

```sql
-- New fields added to exclusive_authorizations table
ALTER TABLE exclusive_authorizations ADD COLUMN store_id VARCHAR(255);
ALTER TABLE exclusive_authorizations ADD COLUMN store_authorized_bc_id VARCHAR(255);
ALTER TABLE exclusive_authorizations ADD COLUMN advertiser_name VARCHAR(255);
ALTER TABLE exclusive_authorizations ADD COLUMN advertiser_status ENUM(...);
ALTER TABLE exclusive_authorizations ADD COLUMN identity_id VARCHAR(255);

-- Updated status enum
ALTER TABLE exclusive_authorizations MODIFY status ENUM(
    'pending', 'granted', 'expired', 'revoked',
    'EFFECTIVE', 'INEFFECTIVE', 'UNAUTHORIZED'
);
```

## Error Handling

### Common Errors
1. **Missing advertiser_id**: Cần cung cấp advertiser ID
2. **Invalid store_id**: Store không tồn tại
3. **Permission denied**: Không có quyền truy cập
4. **Advertiser not found**: Advertiser không tồn tại

### Error Recovery
- Graceful error handling với detailed messages
- Logging cho troubleshooting
- Fallback strategies khi có thể

## Monitoring & Maintenance

### Regular Tasks
1. **Daily sync**: Đồng bộ authorization status
2. **Weekly summary**: Review authorization statistics
3. **Monthly audit**: Kiểm tra shops cần authorization

### Automation
```bash
# Add to crontab for daily sync
0 2 * * * php /path/to/artisan tiktok:sync-authorizations

# Weekly summary report
0 9 * * 1 php /path/to/artisan tiktok:sync-authorizations --summary
```

## Troubleshooting

### 1. Advertiser ID Issues
- Verify advertiser exists và active
- Check permissions trong Business Center
- Ensure correct advertiser_id format

### 2. Store Authorization Issues
- Verify store_authorized_bc_id
- Check Business Center permissions
- Ensure shop is eligible for GMV Max

### 3. API Permission Issues
- Verify access token có đủ scopes
- Check Business Center user permissions
- Contact TikTok support nếu cần

## Best Practices

1. **Regular Monitoring**: Sync authorization status regularly
2. **Error Logging**: Monitor logs cho permission issues
3. **Data Validation**: Validate required fields trước khi sync
4. **Performance**: Use batch operations cho multiple shops
5. **Security**: Protect sensitive advertiser information

## Integration Notes

- ✅ Full TikTok API v1.3 compliance
- ✅ Enhanced error handling và logging
- ✅ Comprehensive status mapping
- ✅ Performance optimized
- ✅ Console commands cho automation
- ✅ Database schema updated
- ✅ Business logic implemented
