# Filament Actions Refactor Documentation

## Overview

This document describes the refactoring of Filament Resource actions from inline definitions to dedicated action classes, following the single responsibility principle and Filament's recommended structure.

## Directory Structure

```
app/Filament/Resources/
├── ShopResource/
│   └── Actions/
│       ├── SyncAllShopsAction.php
│       ├── SyncByAdvertiserAction.php
│       ├── SyncShopAction.php
│       ├── SyncWithTikTokAction.php
│       └── SyncAllBulkAction.php
├── AdvertiserAccountResource/
│   └── Actions/
│       ├── SyncSingleAction.php
│       └── SyncAllFromApiAction.php
├── CampaignResource/
│   └── Actions/
│       ├── AiAnalyzeAction.php
│       ├── SyncAllCampaignsAction.php
│       ├── SyncSessionsAction.php
│       └── AiOptimizeBudgetAction.php
├── CampaignReportResource/
│   └── Actions/
│       ├── AiPerformanceAnalysisAction.php
│       └── SyncReportsAction.php
├── VideoResource/
│   └── Actions/
│       └── SyncVideosAction.php
└── TikTokSettingsResource/
    └── Actions/
        ├── TestSettingAction.php
        ├── TestConnectionAction.php
        ├── TestAiConnectionAction.php
        └── ClearCacheAction.php
```

## Action Classes

### ShopResource Actions

#### 1. SyncAllShopsAction
- **Type**: Header Action
- **Purpose**: Sync all shops from all advertiser accounts
- **Features**: Confirmation modal, background job dispatch, success notifications

#### 2. SyncByAdvertiserAction
- **Type**: Header Action
- **Purpose**: Sync shops from a specific advertiser account
- **Features**: Form with advertiser selection, validation, background job dispatch

#### 3. SyncShopAction
- **Type**: Row Action
- **Purpose**: Quick sync for individual shop's advertiser
- **Features**: Advertiser ID validation, tooltip, success notifications

#### 4. SyncWithTikTokAction
- **Type**: Row Action
- **Purpose**: Detailed sync with confirmation for individual shop
- **Features**: Confirmation modal, advertiser ID validation, background job dispatch

#### 5. SyncAllBulkAction
- **Type**: Bulk Action
- **Purpose**: Sync all selected shops
- **Features**: Confirmation modal, bulk processing, background job dispatch

### AdvertiserAccountResource Actions

#### 1. SyncSingleAction
- **Type**: Row Action
- **Purpose**: Mark individual advertiser account as synced
- **Features**: Confirmation modal, timestamp update, success notifications

#### 2. SyncAllFromApiAction
- **Type**: Header Action
- **Purpose**: Sync all advertiser accounts from TikTok API
- **Features**: Confirmation modal, background job dispatch, detailed descriptions

### CampaignResource Actions

#### 1. AiAnalyzeAction
- **Type**: Row Action
- **Purpose**: Analyze campaign performance using AI
- **Features**: AI service integration, detailed result notifications, error handling

#### 2. SyncAllCampaignsAction
- **Type**: Header Action
- **Purpose**: Sync all GMV Max campaigns from TikTok API
- **Features**: Confirmation modal, background processing, detailed statistics

#### 3. SyncSessionsAction
- **Type**: Row Action
- **Purpose**: Sync sessions for individual campaign
- **Features**: Campaign validation, session statistics, error handling

#### 4. AiOptimizeBudgetAction
- **Type**: Row Action
- **Purpose**: AI-powered budget optimization for campaigns
- **Features**: Budget analysis, improvement predictions, financial recommendations

### CampaignReportResource Actions

#### 1. AiPerformanceAnalysisAction
- **Type**: Header Action
- **Purpose**: Generate AI-powered performance insights across all campaigns
- **Features**: Performance alerts, insights generation, recommendation system

#### 2. SyncReportsAction
- **Type**: Header Action
- **Purpose**: Sync campaign reports from TikTok API
- **Features**: Date range sync (30 days), detailed sync statistics, error categorization

### VideoResource Actions

#### 1. SyncVideosAction
- **Type**: Row Action
- **Purpose**: Sync videos for campaign from TikTok API
- **Features**: Campaign validation, video statistics, visibility controls

### TikTokSettingsResource Actions

#### 1. TestSettingAction
- **Type**: Row Action
- **Purpose**: Test individual TikTok settings
- **Features**: Setting validation, detailed test results, error suggestions

#### 2. TestConnectionAction
- **Type**: Header Action
- **Purpose**: Test TikTok API connection
- **Features**: Connection validation, ErrorHandler integration, status reporting

#### 3. TestAiConnectionAction
- **Type**: Header Action
- **Purpose**: Test AI service connection
- **Features**: Provider detection, model information, response time measurement

#### 4. ClearCacheAction
- **Type**: Header Action
- **Purpose**: Clear TikTok configuration cache
- **Features**: Confirmation modal, cache statistics, performance impact warnings

## Benefits of Refactoring

### 1. Code Organization
- **Separation of Concerns**: Each action has its own class and responsibility
- **Maintainability**: Easier to modify individual actions without affecting others
- **Readability**: Resource files are cleaner and more focused

### 2. Reusability
- **Cross-Resource Usage**: Actions can be reused across different resources
- **Inheritance**: Common functionality can be extracted to base action classes
- **Testing**: Individual actions can be unit tested independently

### 3. Scalability
- **Easy Extension**: New actions can be added without modifying existing code
- **Configuration**: Actions can have their own configuration and dependencies
- **Documentation**: Each action class can have detailed documentation

## Usage in Resources

### Before Refactoring
```php
->actions([
    Tables\Actions\EditAction::make(),
    Tables\Actions\Action::make('sync_shop')
        ->label('🔄 Đồng bộ')
        ->color('info')
        ->action(function (Shop $record) {
            // Inline action logic...
        }),
])
```

### After Refactoring
```php
->actions([
    Tables\Actions\EditAction::make(),
    SyncShopAction::make(),
])
```

## Action Class Structure

Each action class follows this pattern:

```php
<?php

namespace App\Filament\Resources\ResourceName\Actions;

use Filament\Tables\Actions\Action;

class ActionNameAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'action_name';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Action Label')
            ->icon('heroicon-o-icon')
            ->color('color')
            ->action(function ($record) {
                $this->performAction($record);
            });
    }

    protected function performAction($record): void
    {
        // Action logic here
    }
}
```

## Best Practices

### 1. Naming Conventions
- **Class Names**: Use descriptive names ending with "Action"
- **Method Names**: Use clear, action-oriented method names
- **File Names**: Match class names exactly

### 2. Error Handling
- **Try-Catch Blocks**: Wrap action logic in try-catch blocks
- **User Feedback**: Provide clear success/error notifications
- **Logging**: Log errors for debugging purposes

### 3. Validation
- **Input Validation**: Validate all inputs before processing
- **State Validation**: Check record state before performing actions
- **Permission Checks**: Implement proper authorization checks

### 4. Performance
- **Background Jobs**: Use jobs for long-running operations
- **Caching**: Cache expensive operations where appropriate
- **Database Optimization**: Use efficient queries and eager loading

## Testing

Action classes can be tested independently:

```php
public function test_sync_shop_action()
{
    $shop = Shop::factory()->create();
    $action = SyncShopAction::make();
    
    // Test action behavior
    $this->assertInstanceOf(Action::class, $action);
}
```

## Migration Guide

To apply this pattern to other resources:

1. **Create Actions Directory**: `mkdir app/Filament/Resources/ResourceName/Actions`
2. **Extract Actions**: Move inline action logic to dedicated classes
3. **Update Resource**: Import and reference action classes
4. **Test Functionality**: Ensure all actions work as expected
5. **Clear Cache**: Run `php artisan optimize:clear`

## Conclusion

This refactoring improves code organization, maintainability, and follows Filament's best practices. The modular approach makes it easier to manage complex actions and enables better testing and reusability.
