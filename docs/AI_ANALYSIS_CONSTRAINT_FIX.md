# AI Analysis Database Constraint Fix

## Problem Identified

**Error Message:**
```
Manual AI analysis failed {"campaign_id":19,"analysis_type":"recommendations","error":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '19-recommendations-0' for key 'ai_analyses.unique_current_analysis'"}
```

## Root Cause Analysis

### **Database Constraint Issue**
The `ai_analyses` table had a problematic unique constraint:

```sql
UNIQUE KEY `unique_current_analysis` (`campaign_id`, `analysis_type`, `is_current`)
```

**Problem:** This constraint required the combination of `(campaign_id, analysis_type, is_current)` to be unique across ALL records, including historical ones with `is_current = 0`.

### **Workflow That Caused the Error**
1. **Existing record**: `(19, recommendations, 1)` - current analysis
2. **Update operation**: Set `is_current = 0` → creates `(19, recommendations, 0)`
3. **Conflict**: If another historical record with `(19, recommendations, 0)` already exists
4. **Result**: Duplicate entry violation

### **Why This Constraint Was Wrong**
- **Multiple historical records**: We want to keep multiple historical analyses with `is_current = 0`
- **Only one current**: We only need to ensure one record has `is_current = 1` per campaign/type
- **Constraint too restrictive**: The original constraint prevented legitimate historical data

## Solution Implemented

### **1. Removed Problematic Constraint**
```php
// Migration: 2025_07_24_120100_remove_problematic_unique_constraint.php

// Check if constraint exists before dropping
$indexes = DB::select("SHOW INDEX FROM ai_analyses WHERE Key_name = 'unique_current_analysis'");

if (!empty($indexes)) {
    Schema::table('ai_analyses', function (Blueprint $table) {
        $table->dropUnique('unique_current_analysis');
    });
}
```

### **2. Cleaned Up Duplicate Records**
```sql
DELETE a1 FROM ai_analyses a1
INNER JOIN ai_analyses a2 
WHERE a1.id > a2.id 
AND a1.campaign_id = a2.campaign_id 
AND a1.analysis_type = a2.analysis_type 
AND a1.is_current = a2.is_current
```

### **3. Enhanced Application Logic**
```php
// AiAnalysis::createOrUpdateAnalysis() with database transaction

public static function createOrUpdateAnalysis(
    int $campaignId,
    string $analysisType,
    array $aiResult,
    ?float $confidenceScore = null
): self {
    return DB::transaction(function () use ($campaignId, $analysisType, $aiResult, $confidenceScore) {
        // Mark existing current analysis as not current
        self::where('campaign_id', $campaignId)
            ->where('analysis_type', $analysisType)
            ->where('is_current', true)
            ->update(['is_current' => false]);

        // Create new current analysis
        return self::create([
            'campaign_id' => $campaignId,
            'analysis_type' => $analysisType,
            'ai_result' => $aiResult,
            'confidence_score' => $confidenceScore,
            'is_current' => true,
            'analyzed_at' => Carbon::now(),
        ]);
    });
}
```

## Benefits of the Fix

### **1. Proper Data Model**
- **Multiple historical records**: ✅ Allowed with `is_current = 0`
- **Single current record**: ✅ Ensured by application logic
- **Data integrity**: ✅ Maintained through database transactions

### **2. Flexible Analysis History**
- **Track analysis evolution**: Multiple versions of analysis for same campaign/type
- **Performance comparison**: Compare current vs historical analysis results
- **Audit trail**: Complete history of AI analysis changes

### **3. Robust Error Handling**
- **Transaction safety**: All operations wrapped in database transactions
- **Atomic updates**: Either all operations succeed or all fail
- **Consistency**: No partial updates that could cause data inconsistency

### **4. Better Performance**
- **No constraint violations**: Eliminates database errors during updates
- **Faster operations**: No need to check for constraint violations
- **Cleaner code**: Application logic handles business rules

## Database Schema After Fix

### **Current Structure**
```sql
CREATE TABLE `ai_analyses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `campaign_id` bigint unsigned NOT NULL,
  `analysis_type` enum('recommendations','roi_prediction','budget_optimization') NOT NULL,
  `ai_result` json NOT NULL COMMENT 'Complete AI response data',
  `confidence_score` decimal(5,2) DEFAULT NULL COMMENT 'AI confidence score 0-100',
  `is_current` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Mark latest analysis for each type',
  `analyzed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When analysis was performed',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ai_analyses_campaign_id_foreign` (`campaign_id`),
  KEY `ai_analyses_campaign_id_analysis_type_is_current_index` (`campaign_id`,`analysis_type`,`is_current`),
  KEY `ai_analyses_campaign_id_is_current_index` (`campaign_id`,`is_current`),
  KEY `ai_analyses_analysis_type_is_current_index` (`analysis_type`,`is_current`),
  KEY `ai_analyses_analyzed_at_index` (`analyzed_at`),
  CONSTRAINT `ai_analyses_campaign_id_foreign` FOREIGN KEY (`campaign_id`) REFERENCES `gmv_max_campaigns` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### **Key Changes**
- ❌ **Removed**: `unique_current_analysis` constraint
- ✅ **Kept**: All performance indexes
- ✅ **Added**: Database transaction logic in application

## Testing Results

### **Before Fix**
```
❌ Error: Integrity constraint violation: 1062 Duplicate entry '19-recommendations-0'
```

### **After Fix**
```
✅ recommendations analysis completed
✅ roi_prediction analysis completed  
✅ budget_optimization analysis completed
✅ AI Campaign Analysis completed successfully!
```

## Data Integrity Guarantees

### **Application-Level Constraints**
1. **Single current analysis**: Only one record with `is_current = 1` per campaign/type
2. **Historical preservation**: Multiple records with `is_current = 0` allowed
3. **Atomic operations**: All updates wrapped in database transactions
4. **Consistent state**: No partial updates possible

### **Query Patterns**
```php
// Get current analysis (always returns 0 or 1 record)
$current = AiAnalysis::where('campaign_id', $campaignId)
    ->where('analysis_type', $type)
    ->where('is_current', true)
    ->first();

// Get historical analyses (can return multiple records)
$history = AiAnalysis::where('campaign_id', $campaignId)
    ->where('analysis_type', $type)
    ->where('is_current', false)
    ->orderBy('analyzed_at', 'desc')
    ->get();
```

## Migration Safety

### **Backward Compatibility**
- ✅ **Existing data**: Preserved and cleaned up
- ✅ **Application code**: No breaking changes required
- ✅ **API responses**: Same structure maintained

### **Rollback Plan**
```php
// If needed, can restore constraint (after ensuring no duplicates)
Schema::table('ai_analyses', function (Blueprint $table) {
    $table->unique(['campaign_id', 'analysis_type', 'is_current'], 'unique_current_analysis');
});
```

## Result

The AI analysis system now works reliably without database constraint violations while maintaining proper data integrity through application-level logic and database transactions.

**Key Improvements:**
✅ **No more constraint violations** during AI analysis updates
✅ **Proper historical data** preservation
✅ **Robust transaction handling** for data consistency
✅ **Better error handling** and recovery
✅ **Maintained performance** with existing indexes
