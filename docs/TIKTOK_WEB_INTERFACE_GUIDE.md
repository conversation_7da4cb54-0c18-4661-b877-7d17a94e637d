# TikTok API Web Interface Guide

## Overview

Hệ thống đã được trang bị 2 web interface chuyên nghiệp để quản lý TikTok API functions thay vì chỉ sử dụng command line. Các interface này được xây dựng trên Filament admin panel với UI/UX hiện đại và user-friendly.

## Pages Available

### 1. TikTok Products Management
**URL**: `/admin/tik-tok-products-management`
**Navigation**: TikTok API Management > Quản Lý Sản Phẩm TikTok

### 2. TikTok Authorization Management  
**URL**: `/admin/tik-tok-authorization-management`
**Navigation**: TikTok API Management > Quản Lý Ủy Quyền TikTok

## Page 1: TikTok Products Management

### Features

#### Header Statistics
- **Tổng sản phẩm**: Số lượng products trong hệ thống
- **Sản phẩm có sẵn**: Products với status AVAILABLE
- **Sản phẩm GMV Max**: Products eligible cho GMV Max campaigns
- **Cập nhật gần nhất**: Timestamp của sync gần nhất

#### Sync Configuration Form
- **Chọn Cửa Hàng**: Dropdown list các shops active
- **Loại Đồng Bộ**: 
  - Tất cả sản phẩm
  - Chỉ sản phẩm GMV Max
  - Chỉ sản phẩm Shopping Ads
- **Số Sản Phẩm Mỗi Trang**: Page size cho API calls (1-100)

#### Quick Actions Cards
- **Đồng Bộ Tất Cả**: Visual card cho sync all products
- **GMV Max Only**: Visual card cho GMV Max eligible products
- **Shopping Ads**: Visual card cho Shopping Ads eligible products

#### Products Table
- **Columns**: SPU ID, Tiêu đề, Cửa hàng, Giá, Trạng thái API, GMV Max status, Shopping Ads status, Lượt bán, Cập nhật
- **Filters**: Shop, API status, GMV Max status, Shopping Ads status
- **Actions**: Sync single product, bulk sync selected products
- **Auto-refresh**: Mỗi 30 giây

#### Header Actions
- **Đồng Bộ Tất Cả**: Sync all products từ all shops
- **Đồng Bộ Theo Cấu Hình**: Modal form để config sync options

### User Experience Features

#### Real-time Operations
- Loading indicators during sync operations
- Progress tracking với visual feedback
- Non-blocking UI với overlay loading

#### Error Handling
- User-friendly error messages
- Permission error handling với suggestions
- Fallback data notifications

#### Responsive Design
- Mobile-friendly layout
- Adaptive grid system
- Touch-friendly interactions

## Page 2: TikTok Authorization Management

### Features

#### Header Statistics
- **Tổng authorizations**: Số lượng authorizations trong hệ thống
- **Authorizations hiệu lực**: Count của EFFECTIVE authorizations
- **Chưa được ủy quyền**: Count của UNAUTHORIZED authorizations
- **Shops cần authorization**: Shops chưa có effective authorization

#### Grant Authorization Form
- **Chọn Cửa Hàng**: Dropdown chỉ hiển thị shops eligible
- **Advertiser ID**: Input field với validation

#### Quick Actions Cards
- **Cấp Quyền Hàng Loạt**: Visual card cho batch grant
- **Đồng Bộ Tất Cả**: Visual card cho sync all authorizations
- **Kiểm Tra Điều Kiện**: Visual card cho validation
- **Xem Thống Kê**: Visual card cho statistics

#### Authorization Status Overview
3 sections hiển thị:
- **Authorizations Hiệu Lực**: List 5 effective authorizations gần nhất
- **Chưa Được Ủy Quyền**: List 5 unauthorized authorizations
- **Shops Cần Authorization**: List 5 shops cần authorization

#### Authorizations Table
- **Columns**: Store ID, Cửa hàng, Advertiser ID, Advertiser Name, Trạng thái Authorization, Trạng thái Advertiser, Ngày cấp, Cập nhật
- **Filters**: Authorization status, Shop, Effective only filter
- **Actions**: Sync authorization, Grant authorization (cho non-effective)
- **Auto-refresh**: Mỗi 30 giây

#### Header Actions
- **Cấp Quyền Mới**: Modal form để grant authorization cho shop mới
- **Cấp Quyền Hàng Loạt**: Modal form với options validate-only
- **Đồng Bộ Tất Cả**: Sync all authorizations từ API
- **Xem Thống Kê**: Hiển thị detailed statistics

### Advanced Features

#### Interactive Forms
- Dynamic shop filtering (chỉ eligible shops)
- Real-time validation
- Confirmation dialogs cho destructive actions

#### Batch Operations
- Multi-select support
- Progress tracking
- Detailed results reporting

#### Smart Notifications
- Context-aware messages
- Error type-specific styling
- Persistent notifications cho important info

## Technical Implementation

### Architecture
- **Filament Pages**: Custom page classes extending Filament\Pages\Page
- **Service Integration**: Direct integration với TikTokApiService và sync services
- **Real-time Updates**: Polling every 30 seconds
- **Error Handling**: Comprehensive error handling với user-friendly messages

### Performance Optimizations
- **Caching**: API responses cached appropriately
- **Lazy Loading**: Tables load data on demand
- **Batch Processing**: Efficient bulk operations
- **Background Jobs**: Long-running operations handled properly

### Security Features
- **Authentication**: Filament auth system
- **Authorization**: Role-based access control
- **Input Validation**: Server-side validation cho all forms
- **CSRF Protection**: Built-in CSRF protection

## User Workflows

### Product Sync Workflow
1. **Access Page**: Navigate to TikTok Products Management
2. **Review Stats**: Check current product statistics
3. **Configure Sync**: Select shop và sync type
4. **Execute Sync**: Click sync button
5. **Monitor Progress**: Watch loading indicators
6. **Review Results**: Check notifications và updated table

### Authorization Grant Workflow
1. **Access Page**: Navigate to TikTok Authorization Management
2. **Review Status**: Check authorization overview
3. **Select Shop**: Choose shop from eligible list
4. **Enter Advertiser ID**: Input valid advertiser ID
5. **Grant Authorization**: Submit form
6. **Verify Success**: Check updated status

### Batch Operations Workflow
1. **Select Items**: Use table checkboxes
2. **Choose Action**: Select bulk action
3. **Confirm Operation**: Review confirmation dialog
4. **Monitor Progress**: Watch progress indicators
5. **Review Results**: Check detailed results

## Help & Documentation

### In-page Help
- Collapsible help sections on each page
- Tooltips và helper text
- Status explanations
- Workflow guides

### Error Recovery
- Clear error messages
- Suggested actions
- Troubleshooting links
- Support contact info

## Mobile Experience

### Responsive Design
- Mobile-optimized layouts
- Touch-friendly buttons
- Swipe gestures support
- Adaptive table views

### Performance
- Optimized loading times
- Efficient data transfer
- Progressive enhancement
- Offline capability indicators

## Integration Benefits

### Compared to Command Line
- **User-Friendly**: Visual interface vs terminal commands
- **Real-time Feedback**: Immediate visual feedback
- **Error Handling**: User-friendly error messages
- **Batch Operations**: Easy multi-select operations
- **Monitoring**: Real-time status updates
- **Accessibility**: No technical knowledge required

### Business Value
- **Efficiency**: Faster operations với visual interface
- **Reliability**: Built-in error handling và recovery
- **Scalability**: Handle large datasets efficiently
- **Maintainability**: Easy to update và extend
- **User Adoption**: Lower barrier to entry

## Future Enhancements

### Planned Features
- **Dashboard Widgets**: Summary widgets cho main dashboard
- **Advanced Filters**: More sophisticated filtering options
- **Export Functions**: Export data to various formats
- **Scheduled Operations**: Automated sync scheduling
- **Audit Logs**: Detailed operation logging
- **API Monitoring**: Real-time API health monitoring

### Integration Opportunities
- **Notification System**: Email/SMS notifications
- **Reporting Module**: Advanced reporting features
- **Analytics Dashboard**: Performance analytics
- **Webhook Support**: Real-time event notifications
