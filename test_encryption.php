<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\TikTokSettings;

echo "=== Testing TikTok Settings Encryption ===\n";

// Test access_token
$accessToken = TikTokSettings::where('key', 'api.access_token')->first();
if ($accessToken) {
    echo "Access Token:\n";
    echo "  Raw value: " . ($accessToken->value ? '[ENCRYPTED DATA]' : '[NULL]') . "\n";
    echo "  Is encrypted: " . ($accessToken->is_encrypted ? 'YES' : 'NO') . "\n";
    echo "  Decrypted value: " . ($accessToken->decrypted_value ?: '[NULL]') . "\n";
    echo "  Typed value: " . ($accessToken->getTypedValue() ?: '[NULL]') . "\n";
} else {
    echo "Access Token: NOT FOUND\n";
}

echo "\n";

// Test secret
$secret = TikTokSettings::where('key', 'api.secret')->first();
if ($secret) {
    echo "Secret:\n";
    echo "  Raw value: " . ($secret->value ? '[ENCRYPTED DATA]' : '[NULL]') . "\n";
    echo "  Is encrypted: " . ($secret->is_encrypted ? 'YES' : 'NO') . "\n";
    echo "  Decrypted value: " . ($secret->decrypted_value ?: '[NULL]') . "\n";
    echo "  Typed value: " . ($secret->getTypedValue() ?: '[NULL]') . "\n";
} else {
    echo "Secret: NOT FOUND\n";
}

echo "\n";

// Test app_id (non-encrypted)
$appId = TikTokSettings::where('key', 'api.app_id')->first();
if ($appId) {
    echo "App ID:\n";
    echo "  Raw value: " . ($appId->value ?: '[NULL]') . "\n";
    echo "  Is encrypted: " . ($appId->is_encrypted ? 'YES' : 'NO') . "\n";
    echo "  Decrypted value: " . ($appId->decrypted_value ?: '[NULL]') . "\n";
    echo "  Typed value: " . ($appId->getTypedValue() ?: '[NULL]') . "\n";
} else {
    echo "App ID: NOT FOUND\n";
}

echo "\n=== Testing Encryption/Decryption ===\n";

// Test encryption manually
try {
    $testValue = "test_secret_123";
    $encrypted = \Illuminate\Support\Facades\Crypt::encryptString($testValue);
    $decrypted = \Illuminate\Support\Facades\Crypt::decryptString($encrypted);
    
    echo "Manual encryption test:\n";
    echo "  Original: $testValue\n";
    echo "  Encrypted: [ENCRYPTED]\n";
    echo "  Decrypted: $decrypted\n";
    echo "  Match: " . ($testValue === $decrypted ? 'YES' : 'NO') . "\n";
} catch (Exception $e) {
    echo "Encryption test failed: " . $e->getMessage() . "\n";
}
