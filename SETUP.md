# TikTok GMV Max Manager - Production Setup Guide

## Production Installation

### 1. Installation

```bash
# Clone repository
git clone <repository-url>
cd laravel-tiktok-gmv-max-manager

# Install dependencies
composer install
npm install

# Environment setup
cp .env.example .env
php artisan key:generate

# Configure database in .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

# Production database setup (clean, no demo data)
php artisan migrate --seed

# Start server
php artisan serve
```

### 2. Admin Access

```
URL: http://localhost:8000/admin
Login: <EMAIL>
Password: admin123
```

### 3. **REQUIRED: Configure TikTok API Credentials**

The system requires real TikTok Business API credentials to function. All API calls are made to live TikTok endpoints.

## TikTok API Configuration

### Method 1: Admin Panel (Recommended)

1. Visit: `http://localhost:8000/admin/tik-tok-settings`
2. Configure API credentials:
   - **api.access_token**: Your TikTok Business API access token
   - **api.app_id**: Your TikTok App ID
   - **api.secret**: Your TikTok App Secret
   - **api.advertiser_id**: Default advertiser ID
3. Test connection using "Test kết nối API" button

### Method 2: Programmatic Migration (For Existing .env)

If you have existing TikTok credentials in `.env` file:

```php
// Via Tinker
php artisan tinker

use App\Services\TikTok\TikTokConfigService;
$configService = new TikTokConfigService();
$result = $configService->migrateFromEnv();
dd($result);
```

### Method 3: Direct Database Insert

```sql
INSERT INTO tiktok_settings (key, value, type, is_encrypted, group, description) VALUES
('api.access_token', 'your_encrypted_token', 'string', 1, 'api', 'TikTok Business API Access Token'),
('api.app_id', 'your_app_id', 'string', 0, 'api', 'TikTok App ID'),
('api.secret', 'your_encrypted_secret', 'string', 1, 'api', 'TikTok App Secret'),
('api.advertiser_id', 'your_advertiser_id', 'string', 0, 'api', 'Default Advertiser ID');
```

## Available Commands

```bash
# Test TikTok API integration
php artisan tiktok:test

# Sync advertiser accounts from TikTok API
php artisan tiktok:sync-advertisers

# Sync in background queue
php artisan tiktok:sync-advertisers --queue

# Force sync even if recently synced
php artisan tiktok:sync-advertisers --force
```

## Configuration Groups

### API Settings
- `api.base_url`: TikTok Business API base URL
- `api.access_token`: API access token (encrypted)
- `api.app_id`: TikTok App ID
- `api.secret`: App secret (encrypted)
- `api.advertiser_id`: Default advertiser ID

### Sync Settings
- `sync.enabled`: Enable automatic synchronization
- `sync.auto_sync_interval`: Auto sync interval in seconds
- `sync.batch_size`: Batch size for sync operations
- `sync.retry_attempts`: Number of retry attempts

### Cache Settings
- `cache.default_ttl`: Default cache TTL in seconds
- `cache.api_response_ttl`: API response cache TTL

### Logging Settings
- `logging.enabled`: Enable TikTok API logging
- `logging.level`: Logging level (debug, info, warning, error)
- `logging.log_requests`: Log all API requests
- `logging.log_responses`: Log API responses

## Production Deployment

### 1. Environment Variables

```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=your-db-name
DB_USERNAME=your-db-user
DB_PASSWORD=your-db-password
```

### 2. Optimize for Production

```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

### 3. Queue Workers

```bash
# Start queue worker for background jobs
php artisan queue:work --daemon

# Or use supervisor for production
sudo supervisorctl start laravel-worker:*
```

### 4. Scheduled Tasks

Add to crontab:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## Troubleshooting

### TikTok API Connection Issues

1. **Check credentials**: Verify API credentials in admin panel
2. **Test connection**: Use "Test kết nối API" button
3. **Check logs**: Review Laravel logs for API errors
4. **Demo mode**: System falls back to demo mode if credentials invalid

### Database Issues

```bash
# Reset database
php artisan migrate:fresh --seed

# Check migration status
php artisan migrate:status

# Rollback migrations
php artisan migrate:rollback
```

### Cache Issues

```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Clear TikTok settings cache
# Via admin panel: TikTok Settings > "Xóa cache" button
```

## Support

- **Admin Panel**: http://localhost:8000/admin
- **TikTok Settings**: http://localhost:8000/admin/tik-tok-settings
- **Advertiser Accounts**: http://localhost:8000/admin/advertiser-accounts
- **Documentation**: See `TikTok_GMV_Max_Development_Task.md` for detailed development history
