<?php

namespace App\Services\AI;

use App\Models\Campaign;
use App\Services\TikTok\TikTokConfigService;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use JsonException;

/**
 * Service để xử lý AI scoring và analysis cho TikTok campaigns
 */
class AIScoringService
{
    protected TikTokConfigService $configService;
    protected array $config;

    public function __construct()
    {
        $this->configService = new TikTokConfigService();
        $this->loadAIConfig();
    }

    /**
     * Load AI scoring configuration
     */
    protected function loadAIConfig(): void
    {
        $this->config = [
            'enabled' => $this->configService->get('ai_scoring.enabled', true),
            'provider' => $this->configService->get('ai_scoring.provider', 'gemini'),
            'model' => $this->configService->get('ai_scoring.model', 'gemini-2.0-flash'),
            'api_key' => $this->configService->get('ai_scoring.api_key'),
            'features' => $this->configService->get('ai_scoring.features', []),
            'analysis_frequency' => $this->configService->get('ai_scoring.analysis_frequency', 'hourly'),
            'confidence_threshold' => $this->configService->get('ai_scoring.confidence_threshold', 0.75),
            'max_requests_per_hour' => $this->configService->get('ai_scoring.max_requests_per_hour', 1000),
        ];
    }

    /**
     * Test AI provider connection
     */
    public function testConnection(): array
    {
        if (!$this->config['enabled']) {
            return [
                'success' => false,
                'message' => 'AI scoring is disabled',
            ];
        }

        if (!$this->config['api_key']) {
            return [
                'success' => false,
                'message' => 'AI API key is not configured',
            ];
        }

        try {
            $response = $this->makeTestRequest();

            return [
                'success' => true,
                'message' => 'AI provider connection successful',
                'provider' => $this->config['provider'],
                'model' => $this->config['model'],
                'response' => $response,
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'AI provider connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Analyze campaign performance và generate AI score
     * @throws JsonException
     */
    public function analyzeCampaign(Campaign $campaign): array
    {
        if (!$this->isFeatureEnabled('roi_prediction')) {
            return ['error' => 'ROI prediction feature is disabled'];
        }

        try {
            $campaignData = $this->prepareCampaignData($campaign);
            $analysis = $this->callAIProvider('analyze_campaign', $campaignData);

            return [
                'success' => true,
                'campaign_id' => $campaign->id,
                'ai_score' => $analysis['score'] ?? 0,
                'confidence' => $analysis['confidence'] ?? 0,
                'recommendations' => $analysis['recommendations'] ?? [],
                'predicted_roi' => $analysis['predicted_roi'] ?? null,
                'risk_factors' => $analysis['risk_factors'] ?? [],
                'optimization_suggestions' => $analysis['optimization_suggestions'] ?? [],
            ];
        } catch (Exception $e) {
            Log::error('AI campaign analysis failed', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate budget optimization recommendations
     * @throws JsonException
     */
    public function optimizeBudget(Campaign $campaign): array
    {
        if (!$this->isFeatureEnabled('budget_optimization')) {
            return ['error' => 'Budget optimization feature is disabled'];
        }

        try {
            $budgetData = $this->prepareBudgetData($campaign);
            $optimization = $this->callAIProvider('optimize_budget', $budgetData);

            return [
                'success' => true,
                'current_budget' => $campaign->budget,
                'recommended_budget' => $optimization['recommended_budget'] ?? $campaign->budget,
                'daily_budget_suggestion' => $optimization['daily_budget'] ?? $campaign->daily_budget,
                'budget_allocation' => $optimization['allocation'] ?? [],
                'expected_improvement' => $optimization['expected_improvement'] ?? 0,
                'confidence' => $optimization['confidence'] ?? 0,
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate performance alerts
     * @throws JsonException
     */
    public function generatePerformanceAlerts(): array
    {
        if (!$this->isFeatureEnabled('performance_alerts')) {
            return ['error' => 'Performance alerts feature is disabled'];
        }

        try {
            $campaigns = Campaign::with('reports')->where('status', 'active')->get();
            $alerts = [];

            foreach ($campaigns as $campaign) {
                $campaignAlerts = $this->analyzeCampaignAlerts($campaign);
                if (!empty($campaignAlerts)) {
                    $alerts[] = [
                        'campaign_id' => $campaign->id,
                        'campaign_name' => $campaign->name,
                        'alerts' => $campaignAlerts,
                    ];
                }
            }

            return [
                'success' => true,
                'alerts' => $alerts,
                'total_alerts' => count($alerts),
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate campaign recommendations
     * @throws JsonException
     */
    public function generateCampaignRecommendations(Campaign $campaign): array
    {
        Log::info('AIScoringService: generateCampaignRecommendations called', [
            'campaign_id' => $campaign->id,
            'campaign_name' => $campaign->name
        ]);

        if (!$this->isFeatureEnabled('campaign_recommendations')) {
            Log::info('AIScoringService: Campaign recommendations feature is disabled');
            return [
                'success' => false,
                'error' => 'Campaign recommendations feature is disabled',
                'error_type' => 'configuration_error'
            ];
        }

        try {
            $campaignData = $this->prepareCampaignData($campaign);
            $recommendations = $this->callAIProvider('generate_recommendations', $campaignData);

            Log::info('AIScoringService: AI provider response', [
                'recommendations_raw' => $recommendations,
                'recommendations_type' => gettype($recommendations['recommendations'] ?? null),
                'recommendations_count' => is_array($recommendations['recommendations'] ?? null) ? count($recommendations['recommendations']) : 'not_array'
            ]);

            $result = [
                'success' => true,
                'recommendations' => $recommendations['recommendations'] ?? [],
                'priority_actions' => $recommendations['priority_actions'] ?? [],
                'performance_insights' => $recommendations['insights'] ?? [],
                'confidence' => $recommendations['confidence'] ?? 0,
            ];

            Log::info('AIScoringService: Final result', [
                'result' => $result,
                'recommendations_final_type' => gettype($result['recommendations']),
                'recommendations_final_count' => is_array($result['recommendations']) ? count($result['recommendations']) : 'not_array'
            ]);

            return $result;
        } catch (Exception $e) {
            Log::error('AIScoringService: Exception in generateCampaignRecommendations', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_type' => 'api_error'
            ];
        }
    }

    /**
     * Make test request to AI provider
     */
    protected function makeTestRequest(): array
    {
        return match ($this->config['provider']) {
            'gemini' => $this->testGeminiConnection(),
            'openai' => $this->testOpenAIConnection(),
            'claude' => $this->testClaudeConnection(),
            default => [
                'success' => false,
                'error' => 'Unsupported AI provider: ' . $this->config['provider'],
                'error_type' => 'configuration_error'
            ]
        };
    }

    /**
     * Test Gemini API connection
     */
    protected function testGeminiConnection(): array
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post("https://generativelanguage.googleapis.com/v1beta/models/{$this->config['model']}:generateContent?key={$this->config['api_key']}", [
            'contents' => [
                [
                    'parts' => [
                        ['text' => 'Test connection for TikTok campaign analysis. Respond with "Connection successful".']
                    ]
                ]
            ]
        ]);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('Gemini API test failed', [
            'status_code' => $response->status(),
            'response_body' => $response->body()
        ]);

        return [
            'success' => false,
            'error' => 'Gemini API test failed. Please check your API key and try again.',
            'details' => $response->body()
        ];
    }

    /**
     * Test OpenAI API connection
     */
    protected function testOpenAIConnection(): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->config['api_key'],
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/chat/completions', [
            'model' => $this->config['model'],
            'messages' => [
                ['role' => 'user', 'content' => 'Test connection. Respond with "Connection successful".']
            ],
            'max_tokens' => 10
        ]);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('OpenAI API test failed', [
            'status_code' => $response->status(),
            'response_body' => $response->body()
        ]);

        return [
            'success' => false,
            'error' => 'OpenAI API test failed. Please check your API key and try again.',
            'details' => $response->body()
        ];
    }

    /**
     * Test Claude API connection
     */
    protected function testClaudeConnection(): array
    {
        $response = Http::withHeaders([
            'x-api-key' => $this->config['api_key'],
            'Content-Type' => 'application/json',
            'anthropic-version' => '2023-06-01'
        ])->post('https://api.anthropic.com/v1/messages', [
            'model' => $this->config['model'],
            'max_tokens' => 10,
            'messages' => [
                ['role' => 'user', 'content' => 'Test connection. Respond with "Connection successful".']
            ]
        ]);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('Claude API test failed', [
            'status_code' => $response->status(),
            'response_body' => $response->body()
        ]);

        return [
            'success' => false,
            'error' => 'Claude API test failed. Please check your API key and try again.',
            'details' => $response->body()
        ];
    }

    /**
     * Call AI provider với specific action
     */
    protected function callAIProvider(string $action, array $data): array
    {
        try {
            return match ($this->config['provider']) {
                'gemini' => $this->callGeminiAPI($action, $data),
                'openai' => $this->callOpenAIAPI($action, $data),
                'claude' => $this->callClaudeAPI($action, $data),
                default => $this->handleUnsupportedProvider($this->config['provider'], $action),
            };
        } catch (Exception $e) {
            Log::error('AI provider call failed', [
                'provider' => $this->config['provider'],
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'AI analysis temporarily unavailable. Please try again later.',
                'error_type' => 'api_error'
            ];
        }
    }

    /**
     * Handle unsupported AI provider
     */
    protected function handleUnsupportedProvider(string $provider, string $action): array
    {
        Log::error('Unsupported AI provider configured', [
            'provider' => $provider,
            'action' => $action,
            'available_providers' => ['gemini', 'openai', 'claude']
        ]);

        return [
            'success' => false,
            'error' => 'AI provider not supported. Please configure a valid provider (Gemini, OpenAI, or Claude) in settings.',
            'error_type' => 'configuration_error'
        ];
    }

    /**
     * Call Gemini API for analysis
     */
    protected function callGeminiAPI(string $action, array $data): array
    {
        if (empty($this->config['api_key'])) {
            Log::warning('Gemini API key not configured', ['action' => $action]);
            return [
                'success' => false,
                'error' => 'Gemini API key not configured. Please add your API key in AI settings.',
                'error_type' => 'configuration_error'
            ];
        }

        try {
            $prompt = $this->buildPrompt($action, $data);

            $response = Http::timeout(30)->withHeaders([
                'Content-Type' => 'application/json',
            ])->post("https://generativelanguage.googleapis.com/v1beta/models/{$this->config['model']}:generateContent?key={$this->config['api_key']}", [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $prompt]
                        ]
                    ]
                ]
            ]);

            if (!$response->successful()) {
                $statusCode = $response->status();
                $errorBody = $response->body();

                Log::error('Gemini API call failed', [
                    'action' => $action,
                    'status_code' => $statusCode,
                    'error_body' => $errorBody,
                    'model' => $this->config['model']
                ]);

                $errorMessage = match ($statusCode) {
                    401 => 'Invalid Gemini API key. Please check your API key in settings.',
                    403 => 'Gemini API access denied. Please verify your API key permissions.',
                    429 => 'Gemini API rate limit exceeded. Please try again later.',
                    500, 502, 503, 504 => 'Gemini service temporarily unavailable. Please try again later.',
                    default => 'Gemini API request failed. Please check your configuration and try again.'
                };

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'error_type' => $statusCode >= 500 ? 'service_error' : 'api_error'
                ];
            }

            $result = $this->parseGeminiResponse($response->json());

            // Only mark as success if parsing was successful
            if (!isset($result['success']) || $result['success'] !== false) {
                $result['success'] = true;
            }

            return $result;

        } catch (Exception $e) {
            Log::error('Gemini API call exception', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Network error connecting to Gemini. Please check your internet connection and try again.',
                'error_type' => 'network_error'
            ];
        }
    }

    /**
     * Call OpenAI API for analysis
     */
    protected function callOpenAIAPI(string $action, array $data): array
    {
        if (empty($this->config['api_key'])) {
            Log::warning('OpenAI API key not configured', ['action' => $action]);
            return [
                'success' => false,
                'error' => 'OpenAI API key not configured. Please add your API key in AI settings.',
                'error_type' => 'configuration_error'
            ];
        }

        try {
            $prompt = $this->buildPrompt($action, $data);

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->post('https://api.openai.com/v1/chat/completions', [
                'model' => $this->config['model'],
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a TikTok campaign optimization expert.'],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => 1000
            ]);

            if (!$response->successful()) {
                $statusCode = $response->status();
                $errorBody = $response->body();

                Log::error('OpenAI API call failed', [
                    'action' => $action,
                    'status_code' => $statusCode,
                    'error_body' => $errorBody,
                    'model' => $this->config['model']
                ]);

                $errorMessage = match ($statusCode) {
                    401 => 'Invalid OpenAI API key. Please check your API key in settings.',
                    403 => 'OpenAI API access denied. Please verify your API key permissions.',
                    429 => 'OpenAI API rate limit exceeded. Please try again later.',
                    500, 502, 503, 504 => 'OpenAI service temporarily unavailable. Please try again later.',
                    default => 'OpenAI API request failed. Please check your configuration and try again.'
                };

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'error_type' => $statusCode >= 500 ? 'service_error' : 'api_error'
                ];
            }

            $result = $this->parseOpenAIResponse($response->json());

            // Only mark as success if parsing was successful
            if (!isset($result['success']) || $result['success'] !== false) {
                $result['success'] = true;
            }

            return $result;

        } catch (Exception $e) {
            Log::error('OpenAI API call exception', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Network error connecting to OpenAI. Please check your internet connection and try again.',
                'error_type' => 'network_error'
            ];
        }
    }

    /**
     * Call Claude API for analysis
     */
    protected function callClaudeAPI(string $action, array $data): array
    {
        if (empty($this->config['api_key'])) {
            Log::warning('Claude API key not configured', ['action' => $action]);
            return [
                'success' => false,
                'error' => 'Claude API key not configured. Please add your API key in AI settings.',
                'error_type' => 'configuration_error'
            ];
        }

        try {
            $prompt = $this->buildPrompt($action, $data);

            $response = Http::timeout(30)->withHeaders([
                'x-api-key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01'
            ])->post('https://api.anthropic.com/v1/messages', [
                'model' => $this->config['model'],
                'max_tokens' => 1000,
                'messages' => [
                    ['role' => 'user', 'content' => $prompt]
                ]
            ]);

            if (!$response->successful()) {
                $statusCode = $response->status();
                $errorBody = $response->body();

                Log::error('Claude API call failed', [
                    'action' => $action,
                    'status_code' => $statusCode,
                    'error_body' => $errorBody,
                    'model' => $this->config['model']
                ]);

                $errorMessage = match ($statusCode) {
                    401 => 'Invalid Claude API key. Please check your API key in settings.',
                    403 => 'Claude API access denied. Please verify your API key permissions.',
                    429 => 'Claude API rate limit exceeded. Please try again later.',
                    500, 502, 503, 504 => 'Claude service temporarily unavailable. Please try again later.',
                    default => 'Claude API request failed. Please check your configuration and try again.'
                };

                return [
                    'success' => false,
                    'error' => $errorMessage,
                    'error_type' => $statusCode >= 500 ? 'service_error' : 'api_error'
                ];
            }

            $result = $this->parseClaudeResponse($response->json());

            // Only mark as success if parsing was successful
            if (!isset($result['success']) || $result['success'] !== false) {
                $result['success'] = true;
            }

            return $result;

        } catch (Exception $e) {
            Log::error('Claude API call exception', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Network error connecting to Claude. Please check your internet connection and try again.',
                'error_type' => 'network_error'
            ];
        }
    }

    /**
     * Build GMV Max Analysis Prompt
     */
    private function buildGMVMaxAnalysisPrompt(array $data): string
    {
        return "Bạn là chuyên gia phân tích TikTok GMV Max với kinh nghiệm sâu về tối ưu hóa chiến dịch thương mại điện tử. Phân tích chiến dịch GMV Max này và cung cấp đánh giá chuyên nghiệp bằng tiếng Việt.

KIẾN THỨC GMV MAX CỐT LÕI:
- GMV Max là giải pháp thương mại tự động của TikTok tối ưu hóa tổng doanh thu từ TikTok Shop
- Sử dụng machine learning để chọn sản phẩm, creative assets và placement options hiệu quả nhất
- Chỉ cần 2 input: ROI target và daily budget
- Tự động test và chọn creative assets có hiệu suất cao nhất từ tất cả nguồn có sẵn

TIÊU CHÍ ĐÁNH GIÁ:
1. ROI Performance: So sánh với target ROI (0.8x-1.2x target là bình thường)
2. Budget Utilization: >80% daily budget = cần tăng budget 10-30%
3. Creative Diversity: Cần ít nhất 50 videos từ các nguồn khác nhau
4. Sales History: Cần >$1,500 GMV trong 7 ngày qua
5. Scaling Strategy: Max Delivery vs Target ROI mode

PHÂN TÍCH VÀ TRẢ VỀ JSON:
{
  \"success\": true,
  \"ai_score\": [0-100],
  \"confidence\": [0-1],
  \"predicted_roi\": [số thập phân],
  \"risk_factors\": [\"Yếu tố rủi ro 1\", \"Yếu tố rủi ro 2\"],
  \"recommendations\": [\"Khuyến nghị 1\", \"Khuyến nghị 2\"],
  \"optimization_suggestions\": [\"Gợi ý tối ưu 1\", \"Gợi ý tối ưu 2\"]
}

DỮ LIỆU CHIẾN DỊCH:\n" . json_encode($data, JSON_THROW_ON_ERROR);
    }

    /**
     * Build GMV Max Budget Optimization Prompt
     */
    private function buildGMVMaxBudgetPrompt(array $data): string
    {
        return "Bạn là chuyên gia tối ưu hóa ngân sách TikTok GMV Max. Phân tích và đưa ra khuyến nghị ngân sách chuyên nghiệp bằng tiếng Việt.

NGUYÊN TẮC NGÂN SÁCH GMV MAX:
1. DAILY BUDGET MINIMUM: Ít nhất 10x AOV (Average Order Value)
2. SCALING GUIDELINES:
   - Tăng 10-30% nếu spend >80% daily budget
   - Đợi 48h trước khi điều chỉnh tiếp
   - Gradual scaling tốt hơn aggressive jumps
3. BUDGET MODES:
   - Target ROI: Ưu tiên ROI ổn định, chỉ spend khi đạt target
   - Max Delivery: Ưu tiên GMV, có thể hy sinh ROI để maximize sales

CHIẾN LƯỢC THEO QUY MÔ:
- Low Scale (<$500/day): Focus creative testing, affiliate building
- Medium Scale ($500-2000/day): Balance ROI và growth
- High Scale (>$2000/day): Aggressive creative production, diverse content

PHÂN TÍCH VÀ TRẢ VỀ JSON:
{
  \"success\": true,
  \"current_budget\": [số hiện tại],
  \"recommended_budget\": [số đề xuất],
  \"daily_budget\": [ngân sách hàng ngày],
  \"expected_improvement\": [% cải thiện],
  \"confidence\": [0-1],
  \"budget_strategy\": \"Target ROI|Max Delivery\",
  \"scaling_timeline\": \"Immediate|3-5 days|1-2 weeks\",
  \"allocation\": {
    \"creative_testing\": [%],
    \"proven_assets\": [%],
    \"new_products\": [%]
  }
}

DỮ LIỆU CHIẾN DỊCH:\n" . json_encode($data, JSON_THROW_ON_ERROR);
    }

    /**
     * Build GMV Max Recommendations Prompt
     */
    private function buildGMVMaxRecommendationsPrompt(array $data): string
    {
        return "Bạn là chuyên gia TikTok GMV Max với chuyên môn sâu về tối ưu hóa hiệu suất chiến dịch. Tạo khuyến nghị chi tiết và actionable bằng tiếng Việt.

FRAMEWORK TỐI ƯU GMV MAX:
1. CREATIVE STRATEGY:
   - Post 3 shoppable videos/day từ business account
   - Leverage affiliate content và UGC
   - Test diverse creative formats và trends
   - Maintain content pipeline để avoid ad fatigue

2. PERFORMANCE OPTIMIZATION:
   - Monitor ROI vs target (0.8x-1.2x acceptable range)
   - Track budget utilization (>80% = scale signal)
   - Analyze creative performance metrics
   - Balance automation với manual oversight

3. SCALING BEST PRACTICES:
   - Start Max Delivery cho new products (3-5 days)
   - Switch to Target ROI cho stable performance
   - Increase budget 10-30% when spending >80%
   - Wait 48h between major changes

4. COMMON PITFALLS:
   - Too aggressive ROI targets
   - Insufficient creative diversity
   - Too many changes too quickly
   - Inadequate inventory planning

TRẢ VỀ JSON STRUCTURE:
{
  \"success\": true,
  \"confidence\": \"High|Medium|Low\",
  \"recommendations\": [
    {
      \"type\": \"Loại khuyến nghị (Creative|Budget|Targeting|Product)\",
      \"description\": \"Mô tả chi tiết hành động cần thực hiện\",
      \"rationale\": \"Lý do dựa trên data và best practices\",
      \"priority\": \"High|Medium|Low\",
      \"timeline\": \"Immediate|3-5 days|1-2 weeks\",
      \"expected_impact\": \"Tác động dự kiến\",
      \"details\": {
        \"specific_actions\": [\"Hành động cụ thể 1\", \"Hành động cụ thể 2\"],
        \"metrics_to_watch\": [\"Metric 1\", \"Metric 2\"],
        \"success_criteria\": \"Tiêu chí thành công\"
      }
    }
  ],
  \"priority_actions\": [
    \"Hành động ưu tiên cao nhất cần thực hiện ngay\",
    \"Hành động ưu tiên thứ hai trong 24-48h\",
    \"Hành động ưu tiên thứ ba trong tuần tới\"
  ],
  \"performance_insights\": [
    {
      \"area\": \"Creative Performance|Budget Efficiency|ROI Optimization|Scaling Potential\",
      \"finding\": \"Phát hiện quan trọng từ data\",
      \"implication\": \"Ý nghĩa và tác động đến chiến dịch\",
      \"recommendation\": \"Khuyến nghị cụ thể cho insight này\"
    }
  ]
}

DỮ LIỆU CHIẾN DỊCH:\n" . json_encode($data, JSON_THROW_ON_ERROR);
    }

    /**
     * Build prompt for AI analysis
     * @throws JsonException
     */
    protected function buildPrompt(string $action, array $data): string
    {
        return match ($action) {
            'analyze_campaign' => $this->buildGMVMaxAnalysisPrompt($data),
            'optimize_budget' => $this->buildGMVMaxBudgetPrompt($data),
            'generate_recommendations' => $this->buildGMVMaxRecommendationsPrompt($data),
            default => "Phân tích dữ liệu chiến dịch TikTok GMV Max này bằng tiếng Việt:\n\n" . json_encode($data, JSON_THROW_ON_ERROR),
        };
    }

    /**
     * Parse Gemini API response
     */
    protected function parseGeminiResponse(array $response): array
    {
        $text = $response['candidates'][0]['content']['parts'][0]['text'] ?? '';

        if (empty($text)) {
            Log::warning('Empty text received from Gemini API response', ['response' => $response]);
            return [
                'success' => false,
                'error' => 'Empty response from Gemini API',
                'score' => 75,
                'confidence' => 0.5
            ];
        }

        return $this->parseJSONFromText($text);
    }

    /**
     * Parse OpenAI API response
     */
    protected function parseOpenAIResponse(array $response): array
    {
        $text = $response['choices'][0]['message']['content'] ?? '';

        if (empty($text)) {
            Log::warning('Empty text received from OpenAI API response', ['response' => $response]);
            return [
                'success' => false,
                'error' => 'Empty response from OpenAI API',
                'score' => 75,
                'confidence' => 0.5
            ];
        }

        return $this->parseJSONFromText($text);
    }

    /**
     * Parse Claude API response
     */
    protected function parseClaudeResponse(array $response): array
    {
        $text = $response['content'][0]['text'] ?? '';

        if (empty($text)) {
            Log::warning('Empty text received from Claude API response', ['response' => $response]);
            return [
                'success' => false,
                'error' => 'Empty response from Claude API',
                'score' => 75,
                'confidence' => 0.5
            ];
        }

        return $this->parseJSONFromText($text);
    }

    /**
     * Extract JSON from AI response text with robust error handling
     */
    protected function parseJSONFromText(string $text): array
    {
        // Log the raw response for debugging
        Log::info('AI Response received for JSON parsing', [
            'text_length' => strlen($text),
            'text_preview' => substr($text, 0, 500) . (strlen($text) > 500 ? '...' : '')
        ]);

        // Strategy 1: Try to extract JSON with improved regex
        $jsonPatterns = [
            '/\{(?:[^{}]|(?R))*\}/s',  // Recursive pattern for nested JSON
            '/\{.*\}/s',               // Original simple pattern
            '/```json\s*(\{.*?\})\s*```/s', // JSON in code blocks
            '/```\s*(\{.*?\})\s*```/s'      // JSON in generic code blocks
        ];

        foreach ($jsonPatterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $jsonString = $matches[1] ?? $matches[0];

                // Clean up the JSON string
                $jsonString = $this->sanitizeJsonString($jsonString);

                try {
                    $json = json_decode($jsonString, true, 512, JSON_THROW_ON_ERROR);
                    if ($json !== null && is_array($json)) {
                        Log::info('Successfully parsed JSON from AI response', [
                            'pattern_used' => $pattern,
                            'json_keys' => array_keys($json)
                        ]);
                        return $this->validateJsonResponse($json);
                    }
                } catch (\JsonException $e) {
                    Log::warning('JSON parsing failed with pattern', [
                        'pattern' => $pattern,
                        'error' => $e->getMessage(),
                        'json_string_preview' => substr($jsonString, 0, 200)
                    ]);
                    continue;
                }
            }
        }

        // Strategy 2: Try to parse the entire text as JSON
        try {
            $cleanText = $this->sanitizeJsonString($text);
            $json = json_decode($cleanText, true, 512, JSON_THROW_ON_ERROR);
            if (is_array($json)) {
                Log::info('Successfully parsed entire text as JSON');
                return $this->validateJsonResponse($json);
            }
        } catch (\JsonException $e) {
            Log::warning('Failed to parse entire text as JSON', ['error' => $e->getMessage()]);
        }

        // Log failure for debugging
        Log::error('All JSON parsing strategies failed', [
            'text_sample' => substr($text, 0, 1000),
            'text_length' => strlen($text)
        ]);

        // Fallback if all JSON parsing fails
        return $this->getDefaultFallbackResponse('JSON parsing failed');
    }

    /**
     * Validate and enhance parsed JSON response
     */
    private function validateJsonResponse(array $json): array
    {
        // Ensure required fields exist with defaults
        $defaults = [
            'success' => true,
            'score' => 75,
            'confidence' => 0.8,
            'recommendations' => [],
            'predicted_roi' => 2.0
        ];

        // Merge with defaults
        $json = array_merge($defaults, $json);

        // Validate and fix data types
        $json['score'] = is_numeric($json['score']) ? (int)$json['score'] : 75;
        $json['confidence'] = is_numeric($json['confidence']) ? (float)$json['confidence'] : 0.8;
        $json['predicted_roi'] = is_numeric($json['predicted_roi']) ? (float)$json['predicted_roi'] : 2.0;

        // Ensure recommendations is an array
        if (!is_array($json['recommendations'])) {
            $json['recommendations'] = [$json['recommendations']];
        }

        // Clamp values to reasonable ranges
        $json['score'] = max(0, min(100, $json['score']));
        $json['confidence'] = max(0.0, min(1.0, $json['confidence']));
        $json['predicted_roi'] = max(0.0, $json['predicted_roi']);

        return $json;
    }

    /**
     * Get default fallback response
     */
    private function getDefaultFallbackResponse(string $error = 'Unknown error'): array
    {
        return [
            'success' => false,
            'score' => 75,
            'confidence' => 0.5,
            'recommendations' => ['Unable to parse AI response - please try again'],
            'predicted_roi' => 2.0,
            'error' => $error
        ];
    }

    /**
     * Sanitize JSON string to fix common issues
     */
    private function sanitizeJsonString(string $jsonString): string
    {
        // Remove BOM and other invisible characters
        $jsonString = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $jsonString);

        // Remove leading/trailing whitespace
        $jsonString = trim($jsonString);

        // Fix common JSON issues
        $jsonString = str_replace([
            '\\n', '\\r', '\\t',  // Remove escaped newlines/tabs
            '\\"',                 // Fix escaped quotes
            '""',                  // Fix double quotes
        ], [
            ' ', ' ', ' ',
            '"',
            '"',
        ], $jsonString);

        // Remove trailing commas before closing braces/brackets
        $jsonString = preg_replace('/,(\s*[}\]])/', '$1', $jsonString);

        // Fix unescaped quotes in strings (basic attempt)
        $jsonString = preg_replace('/(?<!\\\\)"([^"]*)"([^,:}\]]*)"/', '"$1\\"$2"', $jsonString);

        return $jsonString;
    }

    /**
     * Prepare campaign data for AI analysis
     */
    protected function prepareCampaignData(Campaign $campaign): array
    {
        return [
            'campaign_id' => $campaign->campaign_id,
            'name' => $campaign->name,
            'status' => $campaign->status,
            'budget' => $campaign->budget,
            'daily_budget' => $campaign->daily_budget,
            'target_roi' => $campaign->target_roi,
            'start_date' => $campaign->start_date,
            'end_date' => $campaign->end_date,
            'recent_reports' => $campaign->reports()->latest()->limit(30)->get()->toArray(),
        ];
    }

    /**
     * Prepare budget data for optimization
     */
    protected function prepareBudgetData(Campaign $campaign): array
    {
        return [
            'current_budget' => $campaign->budget,
            'daily_budget' => $campaign->daily_budget,
            'performance_history' => $campaign->reports()->latest()->limit(14)->get()->toArray(),
            'target_roi' => $campaign->target_roi,
        ];
    }

    /**
     * Analyze campaign for alerts
     */
    protected function analyzeCampaignAlerts(Campaign $campaign): array
    {
        $alerts = [];
        $latestReport = $campaign->reports()->latest()->first();

        if ($latestReport) {
            if ($latestReport->roi < $campaign->target_roi * 0.8) {
                $alerts[] = [
                    'type' => 'low_roi',
                    'message' => 'ROI is significantly below target',
                    'severity' => 'high',
                ];
            }

            if ($latestReport->cost_per_order > $campaign->budget * 0.1) {
                $alerts[] = [
                    'type' => 'high_cpo',
                    'message' => 'Cost per order is too high',
                    'severity' => 'medium',
                ];
            }
        }

        return $alerts;
    }

    /**
     * Check if AI feature is enabled
     * @throws JsonException
     */
    protected function isFeatureEnabled(string $feature): bool
    {
        $features = $this->config['features'];
        if (is_string($features)) {
            $features = json_decode($features, true, 512, JSON_THROW_ON_ERROR) ?? [];
        }

        return in_array($feature, $features, true);
    }

    /**
     * Get AI configuration
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
