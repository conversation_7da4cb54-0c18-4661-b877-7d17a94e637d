<?php

namespace App\Services\Image;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageDownloadService
{
    protected array $allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp'
    ];

    protected array $allowedExtensions = [
        'jpg', 'jpeg', 'png', 'gif', 'webp'
    ];

    protected int $maxFileSize = 5 * 1024 * 1024; // 5MB
    protected int $timeout = 30; // 30 seconds

    /**
     * Download image từ URL và store locally
     */
    public function downloadAndStore(string $url, string $type = 'general', ?string $identifier = null): ?string
    {
        try {
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                Log::warning('Invalid URL provided for image download', ['url' => $url]);
                return null;
            }

            // Generate filename
            $filename = $this->generateFilename($url, $identifier);
            $directory = "images/{$type}";
            $fullPath = "{$directory}/{$filename}";

            // Check if file already exists
            if (Storage::disk('public')->exists($fullPath)) {
                Log::info('Image already exists, skipping download', ['path' => $fullPath]);
                return $fullPath;
            }

            // Download image
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ])
                ->get($url);

            if (!$response->successful()) {
                Log::warning('Failed to download image', [
                    'url' => $url,
                    'status' => $response->status()
                ]);
                return null;
            }

            $imageData = $response->body();

            // Validate file size
            if (strlen($imageData) > $this->maxFileSize) {
                Log::warning('Image file too large', [
                    'url' => $url,
                    'size' => strlen($imageData),
                    'max_size' => $this->maxFileSize
                ]);
                return null;
            }

            // Validate image content
            if (!$this->validateImageContent($imageData)) {
                Log::warning('Invalid image content', ['url' => $url]);
                return null;
            }

            // Ensure directory exists
            Storage::disk('public')->makeDirectory($directory);

            // Store image
            if (Storage::disk('public')->put($fullPath, $imageData)) {
                Log::info('Image downloaded successfully', [
                    'url' => $url,
                    'path' => $fullPath,
                    'size' => strlen($imageData)
                ]);
                return $fullPath;
            }

            Log::error('Failed to store image', ['url' => $url, 'path' => $fullPath]);
            return null;

        } catch (Exception $e) {
            Log::error('Image download failed', [
                'url' => $url,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }

    /**
     * Generate unique filename cho image
     */
    protected function generateFilename(string $url, ?string $identifier = null): string
    {
        // Extract extension từ URL
        $extension = $this->extractExtension($url);

        // Generate base name
        if ($identifier) {
            $baseName = Str::slug($identifier);
        } else {
            $baseName = Str::random(12);
        }

        // Add timestamp để ensure uniqueness
        $timestamp = now()->format('Ymd_His');

        return "{$baseName}_{$timestamp}.{$extension}";
    }

    /**
     * Extract file extension từ URL
     */
    protected function extractExtension(string $url): string
    {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        // Default to jpg nếu không detect được extension
        if (!in_array($extension, $this->allowedExtensions, true)) {
            $extension = 'jpg';
        }

        return $extension;
    }

    /**
     * Validate image content
     */
    protected function validateImageContent(string $imageData): bool
    {
        // Create temporary file để validate
        $tempFile = tempnam(sys_get_temp_dir(), 'img_validate_');
        file_put_contents($tempFile, $imageData);

        try {
            // Check if it's a valid image
            $imageInfo = getimagesize($tempFile);

            if ($imageInfo === false) {
                return false;
            }

            // Check MIME type
            $mimeType = $imageInfo['mime'];
            if (!in_array($mimeType, $this->allowedMimeTypes, true)) {
                return false;
            }

            // Check dimensions (minimum 10x10, maximum 5000x5000)
            $width = $imageInfo[0];
            $height = $imageInfo[1];

            return !($width < 10 || $height < 10 || $width > 5000 || $height > 5000);

        } finally {
            // Clean up temp file
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    /**
     * Get public URL cho stored image
     */
    public function getPublicUrl(string $path): string
    {
        return Storage::disk('public')->url($path);
    }

    /**
     * Delete stored image
     */
    public function deleteImage(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                return Storage::disk('public')->delete($path);
            }
            return true;
        } catch (Exception $e) {
            Log::error('Failed to delete image', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if image exists locally
     */
    public function imageExists(string $path): bool
    {
        return Storage::disk('public')->exists($path);
    }
}
