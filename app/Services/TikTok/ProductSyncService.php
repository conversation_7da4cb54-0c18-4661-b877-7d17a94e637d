<?php

namespace App\Services\TikTok;

use App\Models\Product;
use App\Models\Shop;
use App\Helpers\ErrorHandler;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use RuntimeException;

/**
 * Service để đồng bộ sản phẩm từ TikTok API
 */
class ProductSyncService
{
    protected TikTokApiService $apiService;

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Đồng bộ tất cả sản phẩm từ một shop
     */
    public function syncShopProducts(Shop $shop, array $options = []): array
    {
        try {
            Log::info('Starting product sync for shop', [
                'shop_id' => $shop->shop_id,
                'shop_name' => $shop->name
            ]);

            // Kiểm tra shop có đủ thông tin không
            if (!$shop->bc_id || !$shop->shop_id) {
                return ErrorHandler::createErrorResponse(
                    'Shop thiếu thông tin bc_id hoặc shop_id',
                    ErrorHandler::VALIDATION_ERROR,
                    ['shop' => $shop->only(['id', 'shop_id', 'bc_id'])]
                );
            }

            // Gọi API để lấy products
            $response = $this->apiService->getStoreProducts(
                $shop->bc_id,
                $shop->shop_id,
                $options
            );

            if (!ErrorHandler::isSuccess($response)) {
                // Check if it's a permission error and provide helpful message
                if (ErrorHandler::getErrorType($response) === ErrorHandler::PERMISSION_ERROR) {
                    Log::warning('Store products permission denied, using fallback data', [
                        'shop_id' => $shop->shop_id,
                        'shop_name' => $shop->name,
                        'error' => ErrorHandler::getErrorMessage($response)
                    ]);

                    // If fallback data is available, continue with limited sync
                    if (isset($response['data']['store_products'])) {
                        Log::info('Using fallback product data', [
                            'shop_id' => $shop->shop_id,
                            'product_count' => count($response['data']['store_products']),
                            'data_source' => $response['data']['data_source'] ?? 'unknown'
                        ]);
                        // Continue processing with fallback data
                    } else {
                        return ErrorHandler::createErrorResponse(
                            'Không thể truy cập dữ liệu sản phẩm cho shop ' . $shop->name . '. ' .
                            'Vui lòng kiểm tra quyền truy cập TikTok API hoặc liên hệ support để cấp quyền store management.',
                            ErrorHandler::PERMISSION_ERROR,
                            [
                                'shop_id' => $shop->shop_id,
                                'required_permissions' => ['store_management', 'product_management'],
                                'suggested_actions' => [
                                    'Contact TikTok Business support',
                                    'Request store product access permissions',
                                    'Verify Business Center permissions'
                                ]
                            ]
                        );
                    }
                } else {
                    return $response;
                }
            }

            $apiData = $response['data'] ?? [];
            $products = $apiData['store_products'] ?? [];
            $pageInfo = $apiData['page_info'] ?? [];

            // Đồng bộ từng sản phẩm
            $syncResults = [
                'total_api_products' => count($products),
                'created' => 0,
                'updated' => 0,
                'errors' => [],
                'page_info' => $pageInfo,
                'data_source' => $apiData['data_source'] ?? 'direct_api',
                'limitations' => $apiData['limitations'] ?? null
            ];

            DB::transaction(function () use ($products, $shop, &$syncResults) {
                foreach ($products as $productData) {
                    try {
                        $result = $this->syncSingleProduct($shop, $productData);
                        if ($result['action'] === 'created') {
                            $syncResults['created']++;
                        } elseif ($result['action'] === 'updated') {
                            $syncResults['updated']++;
                        }
                    } catch (Exception $e) {
                        $syncResults['errors'][] = [
                            'item_group_id' => $productData['item_group_id'] ?? 'unknown',
                            'error' => $e->getMessage()
                        ];
                        Log::error('Error syncing product', [
                            'product_data' => $productData,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });

            Log::info('Product sync completed', [
                'shop_id' => $shop->shop_id,
                'results' => $syncResults
            ]);

            return ErrorHandler::createSuccessResponse($syncResults);

        } catch (Exception $e) {
            Log::error('Product sync failed', [
                'shop_id' => $shop->shop_id,
                'error' => $e->getMessage()
            ]);

            return ErrorHandler::createErrorResponse(
                'Đồng bộ sản phẩm thất bại: ' . $e->getMessage()
            );
        }
    }

    /**
     * Đồng bộ một sản phẩm từ API data
     */
    protected function syncSingleProduct(Shop $shop, array $productData): array
    {
        $itemGroupId = $productData['item_group_id'] ?? null;

        if (!$itemGroupId) {
            throw new RuntimeException('Product data thiếu item_group_id');
        }

        // Tìm product hiện có hoặc tạo mới
        $product = Product::where('item_group_id', $itemGroupId)
                         ->where('shop_id', $shop->id)
                         ->first();

        $mappedData = $this->mapApiDataToModel($productData, $shop);

        if ($product) {
            $product->update($mappedData);
            return ['action' => 'updated', 'product' => $product];
        }

        $product = Product::create($mappedData);
        return ['action' => 'created', 'product' => $product];
    }

    /**
     * Map dữ liệu từ API response sang model fields
     */
    protected function mapApiDataToModel(array $apiData, Shop $shop): array
    {
        return [
            // Core TikTok API fields
            'item_group_id' => $apiData['item_group_id'],
            'store_id' => $apiData['store_id'],
            'title' => $apiData['title'],
            'product_image_url' => $apiData['product_image_url'] ?? null,

            // Price fields
            'min_price' => $this->parsePrice($apiData['min_price'] ?? 0),
            'max_price' => $this->parsePrice($apiData['max_price'] ?? 0),
            'currency' => $apiData['currency'] ?? 'USD',

            // Product details
            'category' => $apiData['category'] ?? null,
            'historical_sales' => (int) ($apiData['historical_sales'] ?? 0),

            // Status fields
            'api_status' => $apiData['status'] ?? 'NOT_AVAILABLE',
            'gmv_max_ads_status' => $apiData['gmv_max_ads_status'] ?? null,
            'is_running_custom_shop_ads' => (bool) ($apiData['is_running_custom_shop_ads'] ?? false),

            // Deprecated field
            'catalog_id' => $apiData['catalog_id'] ?? null,

            // Legacy fields (for backward compatibility)
            'product_id' => $apiData['item_group_id'], // Use item_group_id as product_id
            'name' => $apiData['title'],
            'price' => $this->parsePrice($apiData['min_price'] ?? 0),
            'status' => $this->mapApiStatusToLegacy($apiData['status'] ?? 'NOT_AVAILABLE'),
            'is_occupied' => ($apiData['gmv_max_ads_status'] ?? null) === 'OCCUPIED',

            // Relationship
            'shop_id' => $shop->id,
        ];
    }

    /**
     * Parse price string to decimal
     */
    protected function parsePrice(string|float|int $price): float
    {
        if (is_numeric($price)) {
            return (float) $price;
        }

        // Remove non-numeric characters except decimal point
        $cleanPrice = preg_replace('/[^\d.]/', '', (string) $price);
        return (float) ($cleanPrice ?: 0);
    }

    /**
     * Map API status to legacy status
     */
    protected function mapApiStatusToLegacy(string $apiStatus): string
    {
        return match ($apiStatus) {
            'AVAILABLE' => 'active',
            default => 'inactive'
        };
    }

    /**
     * Đồng bộ products cho GMV Max campaigns
     */
    public function syncGmvMaxEligibleProducts(Shop $shop, array $options = []): array
    {
        return $this->syncShopProducts($shop, array_merge($options, [
            'filtering' => ['ad_creation_eligible' => 'GMV_MAX']
        ]));
    }

    /**
     * Đồng bộ products cho Shopping Ads
     */
    public function syncShoppingAdsEligibleProducts(Shop $shop, array $options = []): array
    {
        return $this->syncShopProducts($shop, array_merge($options, [
            'filtering' => ['ad_creation_eligible' => 'CUSTOM_SHOP_ADS']
        ]));
    }

    /**
     * Đồng bộ tất cả products từ tất cả shops
     */
    public function syncAllShopsProducts(array $options = []): array
    {
        $shops = Shop::where('status', 'active')->get();
        $results = [
            'total_shops' => $shops->count(),
            'successful_shops' => 0,
            'failed_shops' => 0,
            'shop_results' => []
        ];

        foreach ($shops as $shop) {
            try {
                $shopResult = $this->syncShopProducts($shop, $options);

                if (ErrorHandler::isSuccess($shopResult)) {
                    $results['successful_shops']++;
                } else {
                    $results['failed_shops']++;
                }

                $results['shop_results'][$shop->shop_id] = $shopResult;

            } catch (Exception $e) {
                $results['failed_shops']++;
                $results['shop_results'][$shop->shop_id] = ErrorHandler::createErrorResponse(
                    $e->getMessage()
                );
            }
        }

        return ErrorHandler::createSuccessResponse($results);
    }
}
