<?php

namespace App\Services\TikTok\Examples;

use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use Illuminate\Support\Facades\Log;

/**
 * TikTok API Usage Examples
 * 
 * This class demonstrates how to use the TikTok API Service
 * with real examples based on TikTok Business API documentation
 */
class TikTokApiExamples
{
    protected TikTokApiService $apiService;

    public function __construct()
    {
        $this->apiService = new TikTokApiService();
    }

    /**
     * Example 1: Get Product GMV Max Campaigns
     */
    public function getProductGmvMaxCampaigns(): array
    {
        try {
            // Get all Product GMV Max campaigns
            $campaigns = $this->apiService->getProductGmvMaxCampaigns([
                'page' => 1,
                'page_size' => 10
            ]);

            Log::info('Product GMV Max Campaigns Retrieved', [
                'total_campaigns' => $campaigns['page_info']['total_number'] ?? 0,
                'current_page' => $campaigns['page_info']['page'] ?? 1
            ]);

            return $campaigns;

        } catch (\Exception $e) {
            Log::error('Failed to get Product GMV Max campaigns', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 2: Get Live GMV Max Campaigns
     */
    public function getLiveGmvMaxCampaigns(): array
    {
        try {
            // Get all Live GMV Max campaigns
            $campaigns = $this->apiService->getLiveGmvMaxCampaigns([
                'page' => 1,
                'page_size' => 10
            ]);

            Log::info('Live GMV Max Campaigns Retrieved', [
                'total_campaigns' => $campaigns['page_info']['total_number'] ?? 0,
                'current_page' => $campaigns['page_info']['page'] ?? 1
            ]);

            return $campaigns;

        } catch (\Exception $e) {
            Log::error('Failed to get Live GMV Max campaigns', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 3: Get campaigns by specific criteria
     */
    public function getCampaignsByCriteria(): array
    {
        try {
            // Get campaigns with specific filters
            $campaigns = $this->apiService->getCampaigns([
                'gmv_max_promotion_types' => ['PRODUCT_GMV_MAX'],
                'primary_status' => 'STATUS_DELIVERY_OK', // Active campaigns only
                'creation_filter_start_time' => '2024-01-01 00:00:00',
                'creation_filter_end_time' => '2024-12-31 23:59:59',
                'page' => 1,
                'page_size' => 50,
                'fields' => [
                    'campaign_id',
                    'campaign_name', 
                    'operation_status',
                    'create_time',
                    'modify_time',
                    'secondary_status'
                ]
            ]);

            return $campaigns;

        } catch (\Exception $e) {
            Log::error('Failed to get campaigns by criteria', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 4: Get campaigns for specific shops
     */
    public function getCampaignsForShops(array $shopIds): array
    {
        try {
            // Get campaigns for specific TikTok shops
            $campaigns = $this->apiService->getCampaigns([
                'gmv_max_promotion_types' => ['PRODUCT_GMV_MAX'],
                'store_ids' => array_slice($shopIds, 0, 10), // Max 10 shop IDs
                'page_size' => 100
            ]);

            Log::info('Campaigns for specific shops retrieved', [
                'shop_ids' => $shopIds,
                'total_campaigns' => $campaigns['page_info']['total_number'] ?? 0
            ]);

            return $campaigns;

        } catch (\Exception $e) {
            Log::error('Failed to get campaigns for shops', [
                'shop_ids' => $shopIds,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Example 5: Get campaign details
     */
    public function getCampaignDetails(string $campaignId): array
    {
        try {
            $campaignInfo = $this->apiService->getCampaignInfo($campaignId);

            Log::info('Campaign details retrieved', [
                'campaign_id' => $campaignId,
                'campaign_name' => $campaignInfo['campaign_name'] ?? 'Unknown'
            ]);

            return $campaignInfo;

        } catch (\Exception $e) {
            Log::error('Failed to get campaign details', [
                'campaign_id' => $campaignId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Example 6: Get TikTok shops available for GMV Max
     */
    public function getAvailableShops(): array
    {
        try {
            $shops = $this->apiService->getStores();

            // Filter shops that are available for GMV Max
            $gmvMaxShops = array_filter($shops, function($shop) {
                return isset($shop['is_gmv_max_available']) && $shop['is_gmv_max_available'] === true;
            });

            Log::info('GMV Max available shops retrieved', [
                'total_shops' => count($shops),
                'gmv_max_available' => count($gmvMaxShops)
            ]);

            return $gmvMaxShops;

        } catch (\Exception $e) {
            Log::error('Failed to get available shops', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 7: Sync all data from TikTok
     */
    public function syncAllDataFromTikTok(): array
    {
        try {
            $syncService = new TikTokSyncService($this->apiService);
            $results = $syncService->syncAll();

            Log::info('Full TikTok data sync completed', $results);

            return $results;

        } catch (\Exception $e) {
            Log::error('Failed to sync data from TikTok', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 8: Test API connection
     */
    public function testApiConnection(): bool
    {
        try {
            $isConnected = $this->apiService->testConnection();

            if ($isConnected) {
                Log::info('TikTok API connection successful');
            } else {
                Log::warning('TikTok API connection failed');
            }

            return $isConnected;

        } catch (\Exception $e) {
            Log::error('TikTok API connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Example 9: Get rate limit status
     */
    public function getRateLimitStatus(): array
    {
        try {
            $rateLimitStatus = $this->apiService->getRateLimitStatus();

            Log::info('Rate limit status retrieved', $rateLimitStatus);

            return $rateLimitStatus;

        } catch (\Exception $e) {
            Log::error('Failed to get rate limit status', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example 10: Clear all API caches
     */
    public function clearAllCaches(): void
    {
        try {
            $this->apiService->clearAllCaches();
            Log::info('All TikTok API caches cleared');

        } catch (\Exception $e) {
            Log::error('Failed to clear API caches', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Example response structure for getCampaigns()
     * 
     * This shows what the actual TikTok API returns:
     * 
     * {
     *     "code": 0,
     *     "message": "OK", 
     *     "request_id": "{{request_id}}",
     *     "data": {
     *         "list": [
     *             {
     *                 "campaign_name": "My Product Campaign",
     *                 "advertiser_id": "1234567890",
     *                 "objective_type": "PRODUCT_SALES",
     *                 "modify_time": "2024-01-15 10:30:00",
     *                 "secondary_status": "CAMPAIGN_STATUS_ENABLE",
     *                 "campaign_id": "campaign_123456",
     *                 "create_time": "2024-01-01 09:00:00",
     *                 "operation_status": "ENABLE",
     *                 "roi_protection_compensation_status": "IN_EFFECT"
     *             }
     *         ],
     *         "page_info": {
     *             "page": 1,
     *             "page_size": 10,
     *             "total_page": 1,
     *             "total_number": 1
     *         }
     *     }
     * }
     */
}
