<?php

namespace App\Services\TikTok;

use App\Helpers\ErrorHandler;
use App\Models\ExclusiveAuthorization;
use App\Models\Shop;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Service để đồng bộ exclusive authorization từ TikTok API
 */
class AuthorizationSyncService
{
    protected TikTokApiService $apiService;

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Đồng bộ authorization cho một shop
     */
    public function syncShopAuthorization(Shop $shop, ?string $advertiserId = null): array
    {
        try {
            Log::info('Starting authorization sync for shop', [
                'shop_id' => $shop->shop_id,
                'shop_name' => $shop->name,
                'advertiser_id' => $advertiserId
            ]);

            // Kiểm tra shop có đủ thông tin không
            if (!$shop->shop_id || !$shop->store_authorized_bc_id) {
                return ErrorHandler::createErrorResponse(
                    'Shop thiếu thông tin shop_id hoặc store_authorized_bc_id',
                    ErrorHandler::VALIDATION_ERROR,
                    ['shop' => $shop->only(['id', 'shop_id', 'store_authorized_bc_id'])]
                );
            }

            // Gọi API để lấy authorization status
            $response = $this->apiService->getExclusiveAuthorization(
                $shop->shop_id,
                $shop->store_authorized_bc_id,
                $advertiserId
            );

            if (!ErrorHandler::isSuccess($response)) {
                return $response;
            }

            $authData = $response['data'] ?? [];

            // Sync authorization data
            $authorization = $this->syncSingleAuthorization($shop, $authData);

            Log::info('Authorization sync completed', [
                'shop_id' => $shop->shop_id,
                'authorization_status' => $authData['authorization_status'] ?? 'unknown'
            ]);

            return ErrorHandler::createSuccessResponse([
                'authorization' => $authorization,
                'api_data' => $authData
            ]);

        } catch (Exception $e) {
            Log::error('Authorization sync failed', [
                'shop_id' => $shop->shop_id,
                'error' => $e->getMessage()
            ]);

            return ErrorHandler::createErrorResponse(
                'Đồng bộ authorization thất bại: ' . $e->getMessage()
            );
        }
    }

    /**
     * Đồng bộ authorization từ API data
     */
    protected function syncSingleAuthorization(Shop $shop, array $authData): ExclusiveAuthorization
    {
        $mappedData = $this->mapApiDataToModel($authData, $shop);

        // Tìm authorization hiện có hoặc tạo mới
        return ExclusiveAuthorization::updateOrCreate(
            [
                'store_id' => $authData['store_id'],
                'advertiser_id' => $authData['advertiser_id'],
                'shop_id' => $shop->id
            ],
            $mappedData
        );
    }

    /**
     * Map dữ liệu từ API response sang model fields
     */
    protected function mapApiDataToModel(array $apiData, Shop $shop): array
    {
        return [
            // Core API fields
            'authorization_id' => $apiData['authorization_id'] ?? 'auth_' . ($apiData['store_id'] ?? $shop->shop_id),
            'store_id' => $apiData['store_id'],
            'store_authorized_bc_id' => $shop->store_authorized_bc_id,
            'advertiser_id' => $apiData['advertiser_id'],
            'advertiser_name' => $apiData['advertiser_name'] ?? null,
            'advertiser_status' => $apiData['advertiser_status'] ?? null,
            'identity_id' => $apiData['identity_id'] ?? null,
            'status' => $apiData['authorization_status'] ?? 'UNAUTHORIZED',

            // Timestamps (API không cung cấp, sử dụng logic business)
            'granted_at' => $this->determineGrantedAt($apiData),
            'expires_at' => null, // API không cung cấp thông tin expires

            // Relationship
            'shop_id' => $shop->id,
        ];
    }

    /**
     * Determine granted_at timestamp based on status
     */
    protected function determineGrantedAt(array $apiData): ?string
    {
        $status = $apiData['authorization_status'] ?? 'UNAUTHORIZED';

        if ($status === 'EFFECTIVE') {
            // Nếu là EFFECTIVE và chưa có granted_at, set thành hiện tại
            return now();
        }

        return null;
    }

    /**
     * Đồng bộ authorization cho tất cả shops
     */
    public function syncAllShopsAuthorizations(?string $advertiserId = null): array
    {
        $shops = Shop::where('status', 'active')
            ->whereNotNull('shop_id')
            ->whereNotNull('store_authorized_bc_id')
            ->get();

        $results = [
            'total_shops' => $shops->count(),
            'successful_shops' => 0,
            'failed_shops' => 0,
            'shop_results' => []
        ];

        foreach ($shops as $shop) {
            try {
                $shopResult = $this->syncShopAuthorization($shop, $advertiserId);

                if (ErrorHandler::isSuccess($shopResult)) {
                    $results['successful_shops']++;
                } else {
                    $results['failed_shops']++;
                }

                $results['shop_results'][$shop->shop_id] = $shopResult;

            } catch (Exception $e) {
                $results['failed_shops']++;
                $results['shop_results'][$shop->shop_id] = ErrorHandler::createErrorResponse(
                    $e->getMessage()
                );
            }
        }

        return ErrorHandler::createSuccessResponse($results);
    }

    /**
     * Đồng bộ authorization cho một advertiser cụ thể
     */
    public function syncAdvertiserAuthorizations(string $advertiserId): array
    {
        return $this->syncAllShopsAuthorizations($advertiserId);
    }

    /**
     * Batch sync authorizations cho multiple shops
     */
    public function batchSyncAuthorizations(array $shopIds, ?string $advertiserId = null): array
    {
        $shops = Shop::whereIn('shop_id', $shopIds)
            ->whereNotNull('store_authorized_bc_id')
            ->get();

        $results = [
            'requested_shops' => count($shopIds),
            'found_shops' => $shops->count(),
            'successful_syncs' => 0,
            'failed_syncs' => 0,
            'results' => []
        ];

        foreach ($shops as $shop) {
            try {
                $result = $this->syncShopAuthorization($shop, $advertiserId);

                if (ErrorHandler::isSuccess($result)) {
                    $results['successful_syncs']++;
                } else {
                    $results['failed_syncs']++;
                }

                $results['results'][$shop->shop_id] = $result;

            } catch (Exception $e) {
                $results['failed_syncs']++;
                $results['results'][$shop->shop_id] = ErrorHandler::createErrorResponse(
                    $e->getMessage()
                );
            }
        }

        return ErrorHandler::createSuccessResponse($results);
    }

    /**
     * Get authorization summary statistics
     */
    public function getAuthorizationSummary(): array
    {
        try {
            $stats = [
                'total_authorizations' => ExclusiveAuthorization::count(),
                'effective_authorizations' => ExclusiveAuthorization::where('status', 'EFFECTIVE')->count(),
                'ineffective_authorizations' => ExclusiveAuthorization::where('status', 'INEFFECTIVE')->count(),
                'unauthorized_shops' => ExclusiveAuthorization::where('status', 'UNAUTHORIZED')->count(),
                'approved_advertisers' => ExclusiveAuthorization::where('advertiser_status', 'STATUS_ENABLE')->count(),
                'pending_advertisers' => ExclusiveAuthorization::whereIn('advertiser_status', [
                    'STATUS_PENDING_CONFIRM',
                    'STATUS_PENDING_VERIFIED',
                    'STATUS_PENDING_CONFIRM_MODIFY'
                ])->count(),
            ];

            return ErrorHandler::createSuccessResponse($stats);
        } catch (Exception $e) {
            return ErrorHandler::createErrorResponse(
                'Failed to get authorization summary: ' . $e->getMessage()
            );
        }
    }

    /**
     * Grant exclusive authorization for a shop
     */
    public function grantExclusiveAuthorization(Shop $shop, ?string $advertiserId = null): array
    {
        try {
            Log::info('Granting exclusive authorization for shop', [
                'shop_id' => $shop->shop_id,
                'shop_name' => $shop->name,
                'advertiser_id' => $advertiserId
            ]);

            // Validate shop information
            if (!$shop->shop_id || !$shop->store_authorized_bc_id) {
                return ErrorHandler::createErrorResponse(
                    'Shop thiếu thông tin shop_id hoặc store_authorized_bc_id',
                    ErrorHandler::VALIDATION_ERROR,
                    ['shop' => $shop->only(['id', 'shop_id', 'store_authorized_bc_id'])]
                );
            }

            // Check if authorization already exists and is effective
            $existingAuth = ExclusiveAuthorization::where('store_id', $shop->shop_id)
                ->where('advertiser_id', $advertiserId ?? $this->apiService->getAdvertiserId())
                ->where('shop_id', $shop->id)
                ->first();

            if ($existingAuth && $existingAuth->isEffective()) {
                return ErrorHandler::createErrorResponse(
                    'Authorization đã tồn tại và đang có hiệu lực cho shop này',
                    ErrorHandler::VALIDATION_ERROR,
                    ['existing_authorization' => $existingAuth]
                );
            }

            // Grant authorization via API
            $response = $this->apiService->createExclusiveAuthorization(
                $shop->shop_id,
                $shop->store_authorized_bc_id,
                $advertiserId
            );

            if (!ErrorHandler::isSuccess($response)) {
                return $response;
            }

            // Sync the new authorization status
            $syncResult = $this->syncShopAuthorization($shop, $advertiserId);

            if (!ErrorHandler::isSuccess($syncResult)) {
                Log::warning('Authorization granted but sync failed', [
                    'shop_id' => $shop->shop_id,
                    'sync_error' => ErrorHandler::getErrorMessage($syncResult)
                ]);
            }

            Log::info('Exclusive authorization granted successfully', [
                'shop_id' => $shop->shop_id,
                'advertiser_id' => $advertiserId
            ]);

            return ErrorHandler::createSuccessResponse([
                'authorization_granted' => true,
                'shop' => $shop,
                'sync_result' => $syncResult['data'] ?? null
            ]);

        } catch (Exception $e) {
            Log::error('Failed to grant exclusive authorization', [
                'shop_id' => $shop->shop_id,
                'error' => $e->getMessage()
            ]);

            return ErrorHandler::createErrorResponse(
                'Cấp quyền authorization thất bại: ' . $e->getMessage()
            );
        }
    }

    /**
     * Batch grant authorization for multiple shops
     */
    public function batchGrantAuthorizations(array $shopIds, ?string $advertiserId = null): array
    {
        $shops = Shop::whereIn('shop_id', $shopIds)
            ->whereNotNull('store_authorized_bc_id')
            ->get();

        $results = [
            'requested_shops' => count($shopIds),
            'found_shops' => $shops->count(),
            'successful_grants' => 0,
            'failed_grants' => 0,
            'results' => []
        ];

        foreach ($shops as $shop) {
            try {
                $result = $this->grantExclusiveAuthorization($shop, $advertiserId);

                if (ErrorHandler::isSuccess($result)) {
                    $results['successful_grants']++;
                } else {
                    $results['failed_grants']++;
                }

                $results['results'][$shop->shop_id] = $result;

            } catch (Exception $e) {
                $results['failed_grants']++;
                $results['results'][$shop->shop_id] = ErrorHandler::createErrorResponse(
                    $e->getMessage()
                );
            }
        }

        return ErrorHandler::createSuccessResponse($results);
    }

    /**
     * Find shops that need authorization
     */
    public function findShopsNeedingAuthorization(): array
    {
        try {
            $shopsWithoutAuth = Shop::leftJoin('exclusive_authorizations', 'tiktok_shops.id', '=', 'exclusive_authorizations.shop_id')
                ->whereNull('exclusive_authorizations.id')
                ->orWhere('exclusive_authorizations.status', 'UNAUTHORIZED')
                ->select('tiktok_shops.*')
                ->get();

            return ErrorHandler::createSuccessResponse([
                'shops_needing_authorization' => $shopsWithoutAuth,
                'count' => $shopsWithoutAuth->count()
            ]);
        } catch (Exception $e) {
            return ErrorHandler::createErrorResponse(
                'Failed to find shops needing authorization: ' . $e->getMessage()
            );
        }
    }

    /**
     * Validate shop eligibility for GMV Max authorization
     */
    public function validateShopEligibility(Shop $shop): array
    {
        $issues = [];

        // Check required fields
        if (!$shop->shop_id) {
            $issues[] = 'Shop thiếu shop_id';
        }

        if (!$shop->store_authorized_bc_id) {
            $issues[] = 'Shop thiếu store_authorized_bc_id';
        }

        if ($shop->status !== 'active') {
            $issues[] = 'Shop không ở trạng thái active';
        }

        // Check if shop is GMV Max available (would need API call)
        // This could be enhanced with actual API validation

        if (empty($issues)) {
            return ErrorHandler::createSuccessResponse([
                'eligible' => true,
                'shop' => $shop
            ]);
        }

        return ErrorHandler::createErrorResponse(
            'Shop không đủ điều kiện cho GMV Max authorization',
            ErrorHandler::VALIDATION_ERROR,
            ['issues' => $issues]
        );
    }
}
