<?php

namespace App\Services\TikTok;

use App\Helpers\ErrorHandler;
use App\Models\TikTokSettings;
use Illuminate\Support\Facades\Cache;

/**
 * Service để quản lý cấu hình TikTok API
 * Kết hợp file-based config và database settings
 */
class TikTokConfigService
{
    protected array $config = [];
    protected bool $configLoaded = false;

    /**
     * Lấy toàn bộ cấu hình TikTok
     */
    public function getConfig(): array
    {
        if (!$this->configLoaded) {
            $this->loadConfig();
        }

        return $this->config;
    }

    /**
     * Lấy giá trị cấu hình theo key
     */
    public function get(string $key, $default = null)
    {
        $config = $this->getConfig();

        return data_get($config, $key, $default);
    }

    /**
     * Set giá trị cấu hình (lưu vào database)
     */
    public function set(string $key, $value, array $options = []): void
    {
        // Determine setting group based on key
        $group = $this->determineGroup($key);

        // Determine if should be encrypted
        $isEncrypted = $this->shouldEncrypt($key);

        // Determine if is public
        $isPublic = $this->isPublicSetting($key);

        TikTokSettings::set($key, $value, array_merge([
            'group' => $group,
            'is_encrypted' => $isEncrypted,
            'is_public' => $isPublic,
            'type' => $this->determineType($value),
            'description' => $this->getDescription($key),
        ], $options));

        // Clear config cache
        $this->clearCache();
    }

    /**
     * Load cấu hình từ file và database
     */
    protected function loadConfig(): void
    {
        // 1. Load base config từ file
        $fileConfig = config('tiktok', []);

        // 2. Load database settings
        $databaseConfig = $this->loadDatabaseConfig();

        // 3. Merge configs (database overrides file)
        $this->config = array_replace_recursive($fileConfig, $databaseConfig);

        $this->configLoaded = true;
    }

    /**
     * Load cấu hình từ database
     */
    protected function loadDatabaseConfig(): array
    {
        $cacheKey = 'tiktok_config_merged';

        // Kiểm tra xem cache có tồn tại không
        if (!Cache::has($cacheKey)) {
            // Nếu không có cache, force rebuild và cập nhật các cache key riêng lẻ
            $this->rebuildConfigCache();
        }

        // Lấy từ cache hoặc rebuild nếu cần
        return Cache::remember($cacheKey, 1800, function () {
            $settings = TikTokSettings::all();
            $config = [];

            foreach ($settings as $setting) {
                $value = $setting->getTypedValue();
                $this->setNestedConfig($config, $setting->key, $value);

                // Cập nhật cache riêng lẻ cho mỗi setting
                $settingCacheKey = "tiktok_setting_{$setting->key}";
                Cache::put($settingCacheKey, $value, 3600);
            }

            return $config;
        });
    }

    /**
     * Force rebuild config cache
     */
    public function rebuildConfigCache(): array
    {
        $cacheKey = 'tiktok_config_merged';
        Cache::forget($cacheKey);

        $settings = TikTokSettings::all();
        $config = [];

        foreach ($settings as $setting) {
            $value = $setting->getTypedValue();
            $this->setNestedConfig($config, $setting->key, $value);

            // Cập nhật cache riêng lẻ cho mỗi setting
            $settingCacheKey = "tiktok_setting_{$setting->key}";
            Cache::put($settingCacheKey, $value, 3600);
        }

        // Lưu config vào cache
        Cache::put($cacheKey, $config, 1800);

        return $config;
    }

    /**
     * Set nested configuration value
     */
    protected function setNestedConfig(array &$config, string $key, $value): void
    {
        $keys = explode('.', $key);
        $current = &$config;

        // Navigate to the parent of the final key
        for ($i = 0; $i < count($keys) - 1; $i++) {
            $k = $keys[$i];
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }

        // Set the final value
        $finalKey = end($keys);
        $current[$finalKey] = $value;
    }

    /**
     * Determine setting group based on key
     */
    protected function determineGroup(string $key): string
    {
        if (str_starts_with($key, 'api.')) {
            return 'api';
        }

        if (str_starts_with($key, 'sync.')) {
            return 'sync';
        }

        if (str_starts_with($key, 'cache.')) {
            return 'cache';
        }

        if (str_starts_with($key, 'logging.')) {
            return 'logging';
        }

        if (str_starts_with($key, 'ai_scoring.')) {
            return 'ai_scoring';
        }

        return 'general';
    }

    /**
     * Determine if setting should be encrypted
     */
    protected function shouldEncrypt(string $key): bool
    {
        $sensitiveKeys = [
            'api.access_token',
            'api.secret',
            'webhook.secret',
            'ai_scoring.api_key',
        ];

        return in_array($key, $sensitiveKeys);
    }

    /**
     * Determine if setting is public (can be shown in UI)
     */
    protected function isPublicSetting(string $key): bool
    {
        $privateKeys = [
            'api.access_token',
            'api.secret',
            'webhook.secret',
            'ai_scoring.api_key',
        ];

        return !in_array($key, $privateKeys);
    }

    /**
     * Determine data type of value
     */
    protected function determineType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        }

        if (is_int($value)) {
            return 'integer';
        }

        if (is_float($value)) {
            return 'float';
        }

        if (is_array($value)) {
            return 'array';
        }

        return 'string';
    }

    /**
     * Get description for setting key
     */
    protected function getDescription(string $key): string
    {
        $descriptions = [
            'api.access_token' => 'TikTok Business API Access Token',
            'api.app_id' => 'TikTok App ID',
            'api.secret' => 'TikTok App Secret',
            'api.base_url' => 'TikTok Business API Base URL',
            'sync.enabled' => 'Enable automatic synchronization',
            'sync.auto_sync_interval' => 'Auto sync interval in seconds',
            'sync.batch_size' => 'Batch size for sync operations',
            'cache.default_ttl' => 'Default cache TTL in seconds',
            'logging.enabled' => 'Enable TikTok API logging',
            'logging.level' => 'Logging level',
            'ai_scoring.enabled' => 'Enable AI-powered campaign scoring',
            'ai_scoring.provider' => 'AI provider (gemini, openai, claude)',
            'ai_scoring.model' => 'AI model for analysis',
            'ai_scoring.api_key' => 'AI provider API key',
            'ai_scoring.features' => 'Enabled AI features',
            'ai_scoring.analysis_frequency' => 'AI analysis frequency',
            'ai_scoring.confidence_threshold' => 'AI confidence threshold',
            'ai_scoring.max_requests_per_hour' => 'AI API rate limit',
        ];

        return $descriptions[$key] ?? "Configuration for {$key}";
    }

    /**
     * Test API connection với current config
     */
    public function testConnection(): array
    {
        $apiService = new TikTokApiService();
        $result = $apiService->testConnection();

        if (ErrorHandler::isSuccess($result)) {
            return ErrorHandler::createSuccessResponse(
                ['data' => $result['data'] ?? []],
                'TikTok API connection successful'
            );
        }

        return $result;
    }

    /**
     * Migrate settings từ .env sang database
     */
    public function migrateFromEnv(): array
    {
        $envMappings = [
            'TIKTOK_ACCESS_TOKEN' => 'api.access_token',
            'TIKTOK_APP_ID' => 'api.app_id',
            'TIKTOK_SECRET' => 'api.secret',
            'TIKTOK_API_BASE_URL' => 'api.base_url',
            'TIKTOK_SYNC_ENABLED' => 'sync.enabled',
            'TIKTOK_AUTO_SYNC_INTERVAL' => 'sync.auto_sync_interval',
            'TIKTOK_SYNC_BATCH_SIZE' => 'sync.batch_size',
            'TIKTOK_CACHE_TTL' => 'cache.default_ttl',
            'TIKTOK_LOGGING_ENABLED' => 'logging.enabled',
            'TIKTOK_LOG_LEVEL' => 'logging.level',
            'GEMINI_API_KEY' => 'ai_scoring.api_key',
        ];

        $migrated = [];
        $skipped = [];

        foreach ($envMappings as $envKey => $configKey) {
            $envValue = env($envKey);

            if ($envValue !== null) {
                // Check if setting already exists
                $existing = TikTokSettings::where('key', $configKey)->first();

                if (!$existing) {
                    $this->set($configKey, $envValue);
                    $migrated[] = $configKey;
                } else {
                    $skipped[] = $configKey;
                }
            }
        }

        return [
            'migrated' => $migrated,
            'skipped' => $skipped,
            'total' => count($migrated),
        ];
    }

    /**
     * Clear all config cache
     */
    public function clearCache(): void
    {
        // Clear merged config cache
        Cache::forget('tiktok_config_merged');

        // Clear individual setting caches
        $settings = TikTokSettings::all();
        foreach ($settings as $setting) {
            Cache::forget("tiktok_setting_{$setting->key}");
        }

        // Clear model cache
        TikTokSettings::clearCache();

        // Reset loaded flag
        $this->configLoaded = false;

        // Force rebuild cache
        $this->rebuildConfigCache();
    }

    /**
     * Get all settings grouped by category
     */
    public function getAllSettings(): array
    {
        return TikTokSettings::all()
            ->groupBy('group')
            ->map(function ($settings) {
                return $settings->map(function ($setting) {
                    return [
                        'key' => $setting->key,
                        'value' => $setting->is_public ? $setting->getTypedValue() : '***',
                        'type' => $setting->type,
                        'description' => $setting->description,
                        'is_encrypted' => $setting->is_encrypted,
                        'is_public' => $setting->is_public,
                        'last_tested_at' => $setting->last_tested_at,
                        'test_result' => $setting->test_result,
                    ];
                });
            })
            ->toArray();
    }
}
