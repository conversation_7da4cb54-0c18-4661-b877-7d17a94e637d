<?php

namespace App\Services\TikTok;

use App\Models\AdvertiserAccount;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * Service để đồng bộ Advertiser Accounts từ TikTok API
 */
class AdvertiserAccountSyncService
{
    protected TikTokApiService $apiService;
    protected array $syncStats = [
        'total_fetched' => 0,
        'created' => 0,
        'updated' => 0,
        'deactivated' => 0,
        'errors' => 0,
        'start_time' => null,
        'end_time' => null,
    ];

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Đồng bộ tất cả advertiser accounts từ TikTok API
     */
    public function syncAdvertiserAccounts(): array
    {
        $this->syncStats['start_time'] = now();

        Log::info('Starting advertiser accounts sync from TikTok API');

        try {
            // L<PERSON>y dữ liệu từ TikTok API
            $apiResponse = $this->apiService->getAdvertiserAccounts();

            // Debug logging để xem actual response format
            Log::debug('Advertiser accounts API response', [
                'response_structure' => array_keys($apiResponse),
                'response_sample' => $apiResponse
            ]);

            if (!$this->isValidApiResponse($apiResponse)) {
                Log::error('Invalid API response format', [
                    'expected_keys' => ['success', 'data'],
                    'actual_keys' => array_keys($apiResponse),
                    'response' => $apiResponse
                ]);
                throw new RuntimeException('Invalid API response format');
            }

            // Extract advertiser accounts từ response với flexible handling
            $advertiserAccountsData = $this->extractAdvertiserAccountsData($apiResponse);

            // Convert object to array if needed (TikTok API returns object with numeric keys)
            if (is_object($advertiserAccountsData)) {
                $advertiserAccounts = array_values((array)$advertiserAccountsData);
            } else {
                $advertiserAccounts = array_values($advertiserAccountsData);
            }

            // Validate extracted data
            if (empty($advertiserAccounts)) {
                Log::warning('No advertiser accounts found in API response', [
                    'response_structure' => array_keys($apiResponse),
                    'data_structure' => isset($apiResponse['data']) ? array_keys($apiResponse['data']) : 'no_data'
                ]);
            }

            $this->syncStats['total_fetched'] = count($advertiserAccounts);

            Log::info("Fetched {$this->syncStats['total_fetched']} advertiser accounts from TikTok API");

            // Đồng bộ từng account
            DB::transaction(function () use ($advertiserAccounts) {
                $this->processAdvertiserAccounts($advertiserAccounts);
                $this->deactivateRemovedAccounts($advertiserAccounts);
            });

            $this->syncStats['end_time'] = now();

            Log::info('Advertiser accounts sync completed', $this->syncStats);

            return [
                'success' => true,
                'stats' => $this->syncStats,
                'message' => 'Sync completed successfully'
            ];

        } catch (Exception $e) {
            $this->syncStats['errors']++;
            $this->syncStats['end_time'] = now();

            Log::error('Advertiser accounts sync failed', [
                'error' => $e->getMessage(),
                'stats' => $this->syncStats
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->syncStats
            ];
        }
    }

    /**
     * Xử lý danh sách advertiser accounts từ API
     */
    protected function processAdvertiserAccounts(array $advertiserAccounts): void
    {
        foreach ($advertiserAccounts as $accountData) {
            try {
                $this->syncSingleAccount($accountData);
            } catch (Exception $e) {
                $this->syncStats['errors']++;
                Log::error('Failed to sync advertiser account', [
                    'advertiser_id' => $accountData['advertiser_id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Đồng bộ một advertiser account
     */
    protected function syncSingleAccount(array $accountData): void
    {
        $advertiserId = $accountData['advertiser_id'];

        // Tìm account hiện có hoặc tạo mới
        $account = AdvertiserAccount::where('advertiser_id', $advertiserId)->first();

        if ($account) {
            // Cập nhật account hiện có
            $this->updateExistingAccount($account, $accountData);
        } else {
            // Tạo account mới
            $this->createNewAccount($accountData);
        }
    }

    /**
     * Cập nhật advertiser account hiện có
     */
    protected function updateExistingAccount(AdvertiserAccount $account, array $accountData): void
    {
        $updateData = [
            'advertiser_name' => $accountData['advertiser_name'],
            'status' => 'active', // Nếu có trong API response thì đang active
            'last_sync_at' => now(),
        ];

        // Chỉ cập nhật nếu có thay đổi
        $hasChanges = false;
        foreach ($updateData as $key => $value) {
            if ($account->$key !== $value) {
                $hasChanges = true;
                break;
            }
        }

        if ($hasChanges) {
            $account->update($updateData);
            $this->syncStats['updated']++;

            Log::info("Updated advertiser account: {$account->advertiser_id}");
        }
    }

    /**
     * Tạo advertiser account mới
     */
    protected function createNewAccount(array $accountData): void
    {
        $newAccountData = [
            'advertiser_id' => $accountData['advertiser_id'],
            'advertiser_name' => $accountData['advertiser_name'],
            'status' => 'active',
            'currency' => 'VND', // Default, có thể cập nhật sau
            'timezone' => 'Asia/Ho_Chi_Minh', // Default
            'permissions' => ['campaign_management', 'reporting'], // Default permissions
            'balance' => 0,
            'authorized_at' => now(),
            'last_sync_at' => now(),
        ];

        AdvertiserAccount::create($newAccountData);
        $this->syncStats['created']++;

        Log::info("Created new advertiser account: {$accountData['advertiser_id']}");
    }

    /**
     * Đánh dấu inactive các accounts không còn trong API response
     */
    protected function deactivateRemovedAccounts(array $apiAccounts): void
    {
        $apiAdvertiserIds = collect($apiAccounts)->pluck('advertiser_id')->toArray();

        $removedAccounts = AdvertiserAccount::where('status', 'active')
            ->whereNotIn('advertiser_id', $apiAdvertiserIds)
            ->get();

        foreach ($removedAccounts as $account) {
            $account->update([
                'status' => 'inactive',
                'last_sync_at' => now(),
            ]);

            $this->syncStats['deactivated']++;
            Log::info("Deactivated removed advertiser account: {$account->advertiser_id}");
        }
    }

    /**
     * Kiểm tra tính hợp lệ của API response
     */
    protected function isValidApiResponse(array $response): bool
    {
        // Log detailed response structure for debugging
        Log::debug('Validating API response structure', [
            'response_keys' => array_keys($response),
            'has_success_key' => isset($response['success']),
            'has_code_key' => isset($response['code']),
            'has_data_key' => isset($response['data']),
            'response_sample' => array_slice($response, 0, 3, true) // First 3 keys for debugging
        ]);

        // Check if response is wrapped by ErrorHandler
        if (isset($response['success'])) {
            // ErrorHandler wrapped response
            if (!$response['success']) {
                Log::error('API request failed', [
                    'error' => $response['error'] ?? $response['message'] ?? 'Unknown error',
                    'error_type' => $response['error_type'] ?? 'unknown',
                    'context' => $response['context'] ?? []
                ]);
                return false;
            }

            // Check if data contains the expected structure
            $data = $response['data'] ?? [];

            // More flexible validation - check for different possible structures
            $hasValidStructure = false;

            if (isset($data['list']) && (is_array($data['list']) || is_object($data['list']))) {
                $hasValidStructure = true;
            } elseif (isset($data['data']['list']) && (is_array($data['data']['list']) || is_object($data['data']['list']))) {
                // Some APIs might have nested data structure
                $hasValidStructure = true;
            } elseif (is_array($data) && !empty($data)) {
                // Fallback: if data is a non-empty array, consider it valid
                Log::warning('API response has unexpected structure but contains data', [
                    'data_keys' => array_keys($data),
                    'data_sample' => array_slice($data, 0, 2, true)
                ]);
                $hasValidStructure = true;
            }

            if (!$hasValidStructure) {
                Log::error('API response missing expected list structure', [
                    'data_keys' => is_array($data) ? array_keys($data) : 'not_array',
                    'data_type' => gettype($data),
                    'data_sample' => $data
                ]);
            }

            return $hasValidStructure;
        }

        // Raw TikTok API response format
        $isValidRaw = isset($response['code'], $response['data']['list']) &&
                      $response['code'] === 0 &&
                      (is_array($response['data']['list']) || is_object($response['data']['list']));

        if (!$isValidRaw) {
            Log::error('Raw TikTok API response validation failed', [
                'has_code' => isset($response['code']),
                'code_value' => $response['code'] ?? 'missing',
                'has_data_list' => isset($response['data']['list']),
                'data_structure' => isset($response['data']) ? array_keys($response['data']) : 'missing_data'
            ]);
        }

        return $isValidRaw;
    }

    /**
     * Lấy thống kê sync
     */
    public function getSyncStats(): array
    {
        return $this->syncStats;
    }

    /**
     * Reset thống kê sync
     */
    public function resetSyncStats(): void
    {
        $this->syncStats = [
            'total_fetched' => 0,
            'created' => 0,
            'updated' => 0,
            'deactivated' => 0,
            'errors' => 0,
            'start_time' => null,
            'end_time' => null,
        ];
    }

    /**
     * Kiểm tra xem có cần sync không (dựa trên thời gian sync cuối)
     */
    public function shouldSync(): bool
    {
        $lastSync = AdvertiserAccount::max('last_sync_at');

        if (!$lastSync) {
            return true; // Chưa sync lần nào
        }

        $syncInterval = config('tiktok.sync.auto_sync_interval', 3600); // 1 hour default
        return Carbon::parse($lastSync)->addSeconds($syncInterval)->isPast();
    }

    /**
     * Extract advertiser accounts data từ API response với flexible handling
     */
    protected function extractAdvertiserAccountsData(array $apiResponse): array
    {
        // Try different possible response structures

        // Case 1: ErrorHandler wrapped response
        if (isset($apiResponse['success']) && $apiResponse['success']) {
            $data = $apiResponse['data'] ?? [];

            // Standard structure: data.list
            if (isset($data['list'])) {
                Log::debug('Found advertiser accounts in data.list structure');
                return $data['list'];
            }

            // Nested structure: data.data.list
            if (isset($data['data']['list'])) {
                Log::debug('Found advertiser accounts in data.data.list structure');
                return $data['data']['list'];
            }

            // Direct data array
            if (is_array($data) && !empty($data)) {
                Log::debug('Using direct data array as advertiser accounts');
                return $data;
            }
        }

        // Case 2: Raw TikTok API response
        if (isset($apiResponse['code']) && $apiResponse['code'] === 0) {
            if (isset($apiResponse['data']['list'])) {
                Log::debug('Found advertiser accounts in raw TikTok API response');
                return $apiResponse['data']['list'];
            }
        }

        // Case 3: Fallback - log structure and return empty
        Log::warning('Could not extract advertiser accounts from response', [
            'response_keys' => array_keys($apiResponse),
            'has_success' => isset($apiResponse['success']),
            'has_code' => isset($apiResponse['code']),
            'has_data' => isset($apiResponse['data']),
            'data_keys' => isset($apiResponse['data']) ? array_keys($apiResponse['data']) : null
        ]);

        return [];
    }
}
