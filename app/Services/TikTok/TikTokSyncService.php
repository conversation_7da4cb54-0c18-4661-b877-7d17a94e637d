<?php

namespace App\Services\TikTok;

use App\Helpers\ErrorHandler;
use App\Models\AdvertiserAccount;
use App\Models\Campaign;
use App\Models\CampaignReport;
use App\Models\ExclusiveAuthorization;
use App\Models\Identity;
use App\Models\Session;
use App\Models\Shop;
use App\Services\Image\ImageDownloadService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * TikTok Data Synchronization Service
 * Handles bidirectional sync between local database and TikTok API
 */
class TikTokSyncService
{
    protected TikTokApiService $apiService;
    protected ImageDownloadService $imageService;

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
        $this->imageService = new ImageDownloadService();
    }

    /**
     * Sync all data from TikTok API
     */
    public function syncAll(): array
    {
        $results = [];

        try {
            DB::beginTransaction();

            $results['shops'] = $this->syncShops();
            $results['identities'] = $this->syncIdentities();
            $results['campaigns'] = $this->syncCampaigns();
            $results['exclusive_authorizations'] = $this->syncExclusiveAuthorizations();
            $results['reports'] = $this->syncReports();

            DB::commit();

            Log::info('TikTok Full Sync Completed', $results);

        } catch (Exception $e) {
            DB::rollBack();
            return ErrorHandler::handleNetworkError(
                $e,
                'TikTok Full Sync',
                ['operation' => 'full_sync']
            );
        }

        return $results;
    }

    /**
     * Sync shops from TikTok API
     */
    public function syncShops(): array
    {
        try {
            $storesResponse = $this->apiService->getStores();

            // Handle ErrorHandler wrapped response
            if (!isset($storesResponse['success']) || !$storesResponse['success']) {
                throw new RuntimeException('Failed to fetch stores: ' . ($storesResponse['message'] ?? 'Unknown error'));
            }

            $storesData = $storesResponse['data'] ?? [];
            $tikTokShops = $storesData['store_list'] ?? $storesData['list'] ?? [];

            $synced = 0;
            $created = 0;
            $updated = 0;

            foreach ($tikTokShops as $tikTokShop) {
                try {
                    // Validate required fields
                    if (!isset($tikTokShop['store_id'])) {
                        Log::warning('Shop data missing store_id', [
                            'shop_keys' => array_keys($tikTokShop),
                            'shop_data' => $tikTokShop
                        ]);
                        continue;
                    }

                    // Debug logging
                    Log::debug('Processing shop data', [
                        'shop_keys' => array_keys($tikTokShop),
                        'store_id' => $tikTokShop['store_id'],
                        'store_name' => $tikTokShop['store_name'] ?? 'MISSING'
                    ]);

                    $shop = Shop::updateOrCreate(
                        ['shop_id' => $tikTokShop['store_id']],
                        [
                            // Basic shop info
                            'name' => $tikTokShop['store_name'] ?? $tikTokShop['name'],
                            'status' => $this->mapShopStatus($tikTokShop['store_status'] ?? 'ACTIVE'),
                            'is_eligible_gmv_max' => $tikTokShop['is_gmv_max_available'] ?? false,
                            'region' => !empty($tikTokShop['targeting_region_codes'])
                                ? $tikTokShop['targeting_region_codes'][0]
                                : 'VN',
                            'currency' => $tikTokShop['currency'] ?? 'VND',
                            'targeting_region_codes' => $tikTokShop['targeting_region_codes'] ?? [],

                            // Shop details
                            'store_code' => $tikTokShop['store_code'] ?? null,
                            'thumbnail_url' => $tikTokShop['thumbnail_url'] ?? null,
                            'store_role' => $tikTokShop['store_role'] ?? null,

                            // Business Center info
                            'store_authorized_bc_id' => $tikTokShop['store_authorized_bc_id'] ?? null,
                            'is_owner_bc' => $tikTokShop['is_owner_bc'] ?? false,
                            'bc_id' => isset($tikTokShop['store_authorized_bc_info']) ? ($tikTokShop['store_authorized_bc_info']['bc_id'] ?? null) : null,
                            'bc_name' => isset($tikTokShop['store_authorized_bc_info']) ? ($tikTokShop['store_authorized_bc_info']['bc_name'] ?? null) : null,
                            'bc_profile_image' => isset($tikTokShop['store_authorized_bc_info']) ? ($tikTokShop['store_authorized_bc_info']['bc_profile_image'] ?? null) : null,
                            'user_role' => isset($tikTokShop['store_authorized_bc_info']) ? ($tikTokShop['store_authorized_bc_info']['user_role'] ?? null) : null,

                            // Advertiser info
                            'advertiser_id' => isset($tikTokShop['exclusive_authorized_advertiser_info']) ? ($tikTokShop['exclusive_authorized_advertiser_info']['advertiser_id'] ?? null) : null,
                            'advertiser_name' => isset($tikTokShop['exclusive_authorized_advertiser_info']) ? ($tikTokShop['exclusive_authorized_advertiser_info']['advertiser_name'] ?? null) : null,
                            'exclusive_authorization_status' => isset($tikTokShop['exclusive_authorized_advertiser_info'])
                                ? $this->mapAuthStatus($tikTokShop['exclusive_authorized_advertiser_info']['advertiser_status'] ?? 'none')
                                : 'none',

                            // Map advertiser_id to advertiser_account_id
                            'advertiser_account_id' => $this->getAdvertiserAccountId(
                                isset($tikTokShop['exclusive_authorized_advertiser_info'])
                                    ? ($tikTokShop['exclusive_authorized_advertiser_info']['advertiser_id'] ?? null)
                                    : null
                            ),
                        ]
                    );
                } catch (Exception $e) {
                    Log::error('Shop sync error for individual shop', [
                        'error' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'shop_keys' => array_keys($tikTokShop),
                        'store_id' => $tikTokShop['store_id'] ?? 'MISSING',
                        'store_name' => $tikTokShop['store_name'] ?? 'MISSING'
                    ]);
                    continue;
                }

                if ($shop->wasRecentlyCreated) {
                    $created++;
                } else {
                    $updated++;
                }
                $synced++;

                // Download images if needed
                $this->downloadShopImages($shop, $tikTokShop);
            }

            return [
                'success' => true,
                'synced' => $synced,
                'created' => $created,
                'updated' => $updated
            ];

        } catch (Exception $e) {
            Log::error('Shop Sync Failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Sync campaigns for all advertiser accounts with progress tracking
     */
    public function syncCampaignsForAllAdvertisers($advertiserAccounts, $progressCallback = null): array
    {
        $startTime = microtime(true);
        $totalSynced = 0;
        $totalCreated = 0;
        $totalUpdated = 0;
        $totalErrors = 0;
        $results = [];
        $totalAdvertisers = count($advertiserAccounts);
        $totalShops = $advertiserAccounts->sum(function ($account) {
            // Count shops from both relationships to get accurate total
            $shopsViaAccountId = $account->shops->count();
            $shopsViaAdvertiserId = $account->shopsByAdvertiserId->count();
            // Use max to avoid double counting if shops are linked via both methods
            return max($shopsViaAccountId, $shopsViaAdvertiserId);
        });

        foreach ($advertiserAccounts as $index => $advertiserAccount) {
            $currentIndex = $index + 1;

            // Call progress callback at start of each advertiser sync
            if ($progressCallback) {
                // Count shops from both relationships to get accurate count
                $shopsViaAccountId = $advertiserAccount->shops->count();
                $shopsViaAdvertiserId = $advertiserAccount->shopsByAdvertiserId->count();
                $shopsCount = max($shopsViaAccountId, $shopsViaAdvertiserId);

                $progressCallback([
                    'advertiser_id' => $advertiserAccount->advertiser_id,
                    'advertiser_name' => $advertiserAccount->advertiser_name,
                    'shops_count' => $shopsCount
                ], $currentIndex, $totalAdvertisers);
            }
            try {
                Log::info('Syncing campaigns for advertiser', [
                    'advertiser_id' => $advertiserAccount->advertiser_id,
                    'advertiser_name' => $advertiserAccount->advertiser_name
                ]);

                // Set advertiser ID for this sync
                $this->apiService->setAdvertiserId($advertiserAccount->advertiser_id);

                // Sync campaigns for this advertiser
                $result = $this->syncCampaigns();

                if ($result['success']) {
                    $totalSynced += $result['synced'] ?? 0;
                    $totalCreated += $result['created'] ?? 0;
                    $totalUpdated += $result['updated'] ?? 0;
                    $results[$advertiserAccount->advertiser_id] = $result;

                    // Call progress callback with success result
                    if ($progressCallback) {
                        $shopsViaAccountId = $advertiserAccount->shops->count();
                        $shopsViaAdvertiserId = $advertiserAccount->shopsByAdvertiserId->count();
                        $shopsCount = max($shopsViaAccountId, $shopsViaAdvertiserId);

                        $progressCallback([
                            'advertiser_id' => $advertiserAccount->advertiser_id,
                            'advertiser_name' => $advertiserAccount->advertiser_name,
                            'shops_count' => $shopsCount
                        ], $currentIndex, $totalAdvertisers, $result);
                    }
                } else {
                    $totalErrors++;
                    $results[$advertiserAccount->advertiser_id] = $result;
                    Log::warning('Failed to sync campaigns for advertiser', [
                        'advertiser_id' => $advertiserAccount->advertiser_id,
                        'error' => $result['error'] ?? 'Unknown error'
                    ]);

                    // Call progress callback with error result
                    if ($progressCallback) {
                        $shopsViaAccountId = $advertiserAccount->shops->count();
                        $shopsViaAdvertiserId = $advertiserAccount->shopsByAdvertiserId->count();
                        $shopsCount = max($shopsViaAccountId, $shopsViaAdvertiserId);

                        $errorResult = array_merge($result, ['synced' => 0, 'created' => 0, 'updated' => 0]);
                        $progressCallback([
                            'advertiser_id' => $advertiserAccount->advertiser_id,
                            'advertiser_name' => $advertiserAccount->advertiser_name,
                            'shops_count' => $shopsCount
                        ], $currentIndex, $totalAdvertisers, $errorResult);
                    }
                }

            } catch (Exception $e) {
                $totalErrors++;
                $results[$advertiserAccount->advertiser_id] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                Log::error('Exception during campaign sync for advertiser', [
                    'advertiser_id' => $advertiserAccount->advertiser_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        return [
            'success' => $totalErrors < count($advertiserAccounts), // Success if at least one advertiser worked
            'synced' => $totalSynced,
            'created' => $totalCreated,
            'updated' => $totalUpdated,
            'errors' => $totalErrors,
            'advertiser_results' => $results,
            'total_advertisers' => count($advertiserAccounts),
            'total_shops' => $totalShops,
            'execution_time' => $executionTime,
            'performance_metrics' => [
                'campaigns_per_second' => $executionTime > 0 ? round($totalSynced / $executionTime, 2) : 0,
                'advertisers_per_minute' => $executionTime > 0 ? round((count($advertiserAccounts) * 60) / $executionTime, 2) : 0,
            ]
        ];
    }

    /**
     * Sync campaigns from TikTok API (for single advertiser)
     */
    public function syncCampaigns(): array
    {
        try {
            // Validate advertiser ID is set
            if (!$this->apiService->getAdvertiserId()) {
                throw new RuntimeException('Advertiser ID is required for campaign retrieval');
            }

            // Get both Product and Live GMV Max campaigns
            $productCampaigns = $this->apiService->getProductGmvMaxCampaigns(['page_size' => 100]);
            $liveCampaigns = $this->apiService->getLiveGmvMaxCampaigns(['page_size' => 100]);

            $synced = 0;
            $created = 0;
            $updated = 0;

            // Process Product GMV Max campaigns
            if (isset($productCampaigns['data']['list'])) {
                Log::info('Processing Product GMV Max campaigns', [
                    'count' => count($productCampaigns['data']['list'])
                ]);
                foreach ($productCampaigns['data']['list'] as $tikTokCampaign) {
                    $result = $this->processCampaignData($tikTokCampaign, 'PRODUCT_GMV_MAX');
                    if ($result['success']) {
                        $synced++;
                        if ($result['created']) {
                            $created++;
                        } else {
                            $updated++;
                        }
                    }
                }
            } else {
                Log::warning('No Product GMV Max campaigns found in API response', [
                    'response_keys' => array_keys($productCampaigns),
                    'has_data' => isset($productCampaigns['data']),
                    'data_keys' => isset($productCampaigns['data']) ? array_keys($productCampaigns['data']) : null
                ]);
            }

            // Process Live GMV Max campaigns
            if (isset($liveCampaigns['data']['list'])) {
                Log::info('Processing Live GMV Max campaigns', [
                    'count' => count($liveCampaigns['data']['list'])
                ]);
                foreach ($liveCampaigns['data']['list'] as $tikTokCampaign) {
                    $result = $this->processCampaignData($tikTokCampaign, 'LIVE_GMV_MAX');
                    if ($result['success']) {
                        $synced++;
                        if ($result['created']) {
                            $created++;
                        } else {
                            $updated++;
                        }
                    }
                }
            } else {
                Log::warning('No Live GMV Max campaigns found in API response', [
                    'response_keys' => array_keys($liveCampaigns),
                    'has_data' => isset($liveCampaigns['data']),
                    'data_keys' => isset($liveCampaigns['data']) ? array_keys($liveCampaigns['data']) : null
                ]);
            }

            return [
                'success' => true,
                'synced' => $synced,
                'created' => $created,
                'updated' => $updated
            ];

        } catch (Exception $e) {
            Log::error('Campaign Sync Failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Process individual campaign data from TikTok API
     */
    protected function processCampaignData(array $tikTokCampaign, string $campaignType): array
    {
        try {
            Log::info('Processing campaign data', [
                'campaign_id' => $tikTokCampaign['campaign_id'],
                'campaign_name' => $tikTokCampaign['campaign_name'],
                'advertiser_id' => $tikTokCampaign['advertiser_id'],
                'campaign_type' => $campaignType
            ]);

            // For GMV Max campaigns, we need to find shop by advertiser_id
            // since shop_id might not be directly available in campaign data
            $shop = Shop::where('advertiser_id', $tikTokCampaign['advertiser_id'])->first();
            if (!$shop) {
                Log::warning('Shop not found for campaign', [
                    'campaign_id' => $tikTokCampaign['campaign_id'],
                    'advertiser_id' => $tikTokCampaign['advertiser_id']
                ]);
                return ['success' => false, 'created' => false];
            }

            Log::info('Found shop for campaign', [
                'campaign_id' => $tikTokCampaign['campaign_id'],
                'shop_id' => $shop->id,
                'shop_name' => $shop->name
            ]);

            $campaign = Campaign::updateOrCreate(
                ['campaign_id' => $tikTokCampaign['campaign_id']],
                [
                    'name' => $tikTokCampaign['campaign_name'],
                    'status' => $this->mapCampaignOperationStatus($tikTokCampaign['operation_status']),
                    'target_roi' => null, // Will be fetched from campaign details if needed
                    'budget' => null, // Will be fetched from campaign details if needed
                    'daily_budget' => null, // Will be fetched from campaign details if needed
                    'start_date' => Carbon::parse($tikTokCampaign['create_time']),
                    'end_date' => null, // GMV Max campaigns typically don't have end dates
                    'advertiser_id' => $tikTokCampaign['advertiser_id'],
                    'shop_id' => $shop->id,
                ]
            );

            Log::info('Campaign saved successfully', [
                'campaign_id' => $campaign->campaign_id,
                'campaign_name' => $campaign->name,
                'was_recently_created' => $campaign->wasRecentlyCreated,
                'shop_id' => $campaign->shop_id
            ]);

            // Sync sessions for this campaign (disabled temporarily to avoid errors)
            // $this->syncSessionsForCampaign($campaign);

            return [
                'success' => true,
                'created' => $campaign->wasRecentlyCreated
            ];

        } catch (Exception $e) {
            Log::error('Failed to process campaign data', [
                'campaign_id' => $tikTokCampaign['campaign_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'created' => false];
        }
    }

    /**
     * Sync sessions for all campaigns of an advertiser
     */
    public function syncSessionsForAdvertiser(string $advertiserId): array
    {
        try {
            Log::info('Starting sessions sync for advertiser', ['advertiser_id' => $advertiserId]);

            $totalSynced = 0;
            $totalErrors = 0;
            $campaignResults = [];

            // Get all campaigns for this advertiser
            $campaigns = Campaign::where('advertiser_id', $advertiserId)->get();

            if ($campaigns->isEmpty()) {
                return [
                    'success' => true,
                    'message' => 'No campaigns found for advertiser',
                    'total_synced' => 0,
                    'total_errors' => 0,
                    'campaigns' => []
                ];
            }

            foreach ($campaigns as $campaign) {
                $result = $this->syncSessionsForCampaign($campaign);

                $campaignResults[] = [
                    'campaign_id' => $campaign->campaign_id,
                    'campaign_name' => $campaign->name,
                    'success' => $result['success'],
                    'synced' => $result['synced'] ?? 0,
                    'error' => $result['error'] ?? null
                ];

                if ($result['success']) {
                    $totalSynced += $result['synced'] ?? 0;
                } else {
                    $totalErrors++;
                }
            }

            Log::info('Sessions sync completed for advertiser', [
                'advertiser_id' => $advertiserId,
                'total_campaigns' => $campaigns->count(),
                'total_synced' => $totalSynced,
                'total_errors' => $totalErrors
            ]);

            return [
                'success' => true,
                'total_campaigns' => $campaigns->count(),
                'total_synced' => $totalSynced,
                'total_errors' => $totalErrors,
                'campaigns' => $campaignResults
            ];

        } catch (Exception $e) {
            Log::error('Sessions sync failed for advertiser', [
                'advertiser_id' => $advertiserId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'total_synced' => 0,
                'total_errors' => 1
            ];
        }
    }

    /**
     * Sync sessions for a specific campaign
     */
    public function syncSessionsForCampaign(Campaign $campaign): array
    {
        try {
            $tikTokSessions = $this->apiService->listSessions($campaign->campaign_id);
            $synced = 0;

            // Check if sessions exist in the response
            $sessionList = $tikTokSessions['data']['session_list'] ?? [];

            foreach ($sessionList as $tikTokSession) {
                Session::updateOrCreate(
                    ['session_id' => $tikTokSession['session_id']],
                    [
                        'campaign_id' => $campaign->id,
                        'name' => $tikTokSession['session_name'] ?? $tikTokSession['name'],
                        'status' => $this->mapSessionStatus($tikTokSession['status']),
                        'delivery_type' => $tikTokSession['delivery_type'] ?? 'standard',
                        'budget' => $tikTokSession['budget'] ?? null,
                        'start_time' => isset($tikTokSession['start_time']) ? Carbon::parse($tikTokSession['start_time']) : now(),
                        'end_time' => isset($tikTokSession['end_time']) ? Carbon::parse($tikTokSession['end_time']) : null,
                    ]
                );
                $synced++;
            }

            return ['success' => true, 'synced' => $synced];

        } catch (Exception $e) {
            Log::error('Session Sync Failed', ['campaign_id' => $campaign->campaign_id, 'error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Sync identities from TikTok API
     */
    public function syncIdentities(): array
    {
        try {
            $tikTokIdentities = $this->apiService->getIdentities();
            $synced = 0;

            foreach ($tikTokIdentities as $tikTokIdentity) {
                Identity::updateOrCreate(
                    ['identity_id' => $tikTokIdentity['identity_id']],
                    [
                        'name' => $tikTokIdentity['identity_name'] ?? $tikTokIdentity['name'],
                        'type' => $this->mapIdentityType($tikTokIdentity['type'] ?? 'other'),
                        'status' => $this->mapIdentityStatus($tikTokIdentity['status'] ?? 'active'),
                        'advertiser_id' => $tikTokIdentity['advertiser_id'] ?? config('tiktok.api.advertiser_id'),
                    ]
                );
                $synced++;
            }

            return ['success' => true, 'synced' => $synced];

        } catch (Exception $e) {
            Log::error('Identity Sync Failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Sync exclusive authorizations
     */
    public function syncExclusiveAuthorizations(): array
    {
        try {
            $shops = Shop::all();
            $synced = 0;

            if ($shops->isEmpty()) {
                Log::info('No shops found, skipping authorization sync');
                return ['success' => true, 'synced' => 0];
            }

            foreach ($shops as $shop) {
                try {
                    // Skip shops without required BC ID
                    if (!$shop->store_authorized_bc_id) {
                        Log::warning('Shop missing store_authorized_bc_id, skipping authorization sync', [
                            'shop_id' => $shop->shop_id,
                            'shop_name' => $shop->name
                        ]);
                        continue;
                    }

                    $authData = $this->apiService->getExclusiveAuthorization($shop->shop_id, $shop->store_authorized_bc_id);

                    if (!empty($authData)) {
                        ExclusiveAuthorization::updateOrCreate(
                            ['shop_id' => $shop->id],
                            [
                                'authorization_id' => $authData['authorization_id'] ?? 'auth_' . $shop->shop_id,
                                'advertiser_id' => $authData['advertiser_id'] ?? config('tiktok.api.advertiser_id'),
                                'status' => $this->mapAuthStatus($authData['status'] ?? 'none'),
                                'granted_at' => isset($authData['granted_at']) ? Carbon::parse($authData['granted_at']) : null,
                                'expires_at' => isset($authData['expires_at']) ? Carbon::parse($authData['expires_at']) : null,
                            ]
                        );
                        $synced++;
                    }
                } catch (Exception $e) {
                    // Continue with other shops if one fails
                    Log::warning('Failed to sync authorization for shop', ['shop_id' => $shop->shop_id, 'error' => $e->getMessage()]);
                }
            }

            return ['success' => true, 'synced' => $synced];

        } catch (Exception $e) {
            Log::error('Authorization Sync Failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Sync campaign reports
     */
    public function syncReports(int $days = 30): array
    {
        try {
            $campaigns = Campaign::where('status', 'active')->get();
            $synced = 0;

            foreach ($campaigns as $campaign) {
                $reportData = $this->apiService->getCampaignReports([
                    'campaign_id' => $campaign->campaign_id,
                    'start_date' => now()->subDays($days)->format('Y-m-d'),
                    'end_date' => now()->format('Y-m-d'),
                ]);

                foreach ($reportData as $report) {
                    CampaignReport::updateOrCreate(
                        [
                            'campaign_id' => $campaign->id,
                            'report_date' => Carbon::parse($report['date'])->format('Y-m-d')
                        ],
                        [
                            'report_id' => $report['report_id'] ?? 'report_' . uniqid('', true),
                            'total_cost' => $report['total_cost'] ?? 0,
                            'orders_count' => $report['orders_count'] ?? 0,
                            'gross_revenue' => $report['gross_revenue'] ?? 0,
                            'cost_per_order' => $report['cost_per_order'] ?? 0,
                            'roi' => $report['roi'] ?? 0,
                            'impressions' => $report['impressions'] ?? 0,
                            'clicks' => $report['clicks'] ?? 0,
                            'ctr' => $report['ctr'] ?? 0,
                            'conversion_rate' => $report['conversion_rate'] ?? 0,
                        ]
                    );
                    $synced++;
                }
            }

            return ['success' => true, 'synced' => $synced];

        } catch (Exception $e) {
            Log::error('Reports Sync Failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // ==================== MAPPING METHODS ====================

    protected function mapShopStatus(string $tikTokStatus): string
    {
        return match (strtoupper($tikTokStatus)) {
            'ACTIVE', 'ENABLE', 'ENABLED' => 'active',
            'INACTIVE', 'DISABLE', 'DISABLED' => 'inactive',
            'SUSPEND', 'SUSPENDED' => 'suspended',
            default => 'pending'
        };
    }

    protected function mapCampaignStatus(string $tikTokStatus): string
    {
        return match (strtolower($tikTokStatus)) {
            'enable', 'enabled', 'active' => 'active',
            'disable', 'disabled', 'paused' => 'paused',
            'completed', 'done' => 'completed',
            'deleted', 'cancelled' => 'cancelled',
            default => 'draft'
        };
    }

    /**
     * Map TikTok campaign operation_status to local status
     */
    protected function mapCampaignOperationStatus(string $operationStatus): string
    {
        return match (strtoupper($operationStatus)) {
            'ENABLE' => 'active',
            'DISABLE' => 'paused',
            default => 'draft'
        };
    }

    protected function mapSessionStatus(string $tikTokStatus): string
    {
        return match (strtolower($tikTokStatus)) {
            'enable', 'enabled', 'active' => 'active',
            'disable', 'disabled', 'paused' => 'paused',
            'completed', 'done' => 'completed',
            'deleted', 'cancelled' => 'cancelled',
            default => 'draft'
        };
    }

    protected function mapIdentityType(string $tikTokType): string
    {
        return match (strtolower($tikTokType)) {
            'creator', 'influencer' => 'creator',
            'brand', 'business' => 'brand',
            'agency', 'partner' => 'agency',
            default => 'other'
        };
    }

    protected function mapIdentityStatus(string $tikTokStatus): string
    {
        return match (strtolower($tikTokStatus)) {
            'enable', 'enabled', 'active' => 'active',
            'disable', 'disabled', 'inactive' => 'inactive',
            'suspend', 'suspended' => 'suspended',
            default => 'pending'
        };
    }

    protected function mapAuthStatus(string $tikTokStatus): string
    {
        return match (strtoupper($tikTokStatus)) {
            'STATUS_ENABLE', 'GRANTED', 'APPROVED', 'ACTIVE' => 'granted',
            'STATUS_CONFIRM_FAIL', 'STATUS_DISABLE', 'STATUS_CONFIRM_FAIL_END', 'STATUS_CONFIRM_MODIFY_FAIL', 'REVOKED', 'CANCELLED' => 'revoked',
            'STATUS_PENDING_CONFIRM', 'STATUS_PENDING_CONFIRM_MODIFY', 'STATUS_PENDING_VERIFIED', 'STATUS_CONTRACT_PENDING', 'STATUS_SELF_SERVICE_UNAUDITED', 'STATUS_WAIT_FOR_BPM_AUDIT', 'PENDING', 'WAITING' => 'pending',
            'STATUS_LIMIT' => 'suspended',
            'EXPIRED', 'TIMEOUT' => 'expired',
            default => 'none'
        };
    }

    /**
     * Download images cho shop
     */
    protected function downloadShopImages(Shop $shop, array $tikTokShop): void
    {
        try {
            // Skip if images were recently synced (within 24 hours)
            if ($shop->images_last_synced_at && $shop->images_last_synced_at->gt(now()->subHours(24))) {
                return;
            }

            $imageMetadata = [];
            $hasNewImages = false;

            // Download thumbnail image
            if (!empty($tikTokShop['thumbnail_url'])) {
                $thumbnailPath = $this->imageService->downloadAndStore(
                    $tikTokShop['thumbnail_url'],
                    'shop_thumbnails',
                    'shop_' . $shop->shop_id
                );

                if ($thumbnailPath) {
                    $shop->thumbnail_local_path = $thumbnailPath;
                    $imageMetadata['thumbnail'] = [
                        'downloaded_at' => now()->toISOString(),
                        'original_url' => $tikTokShop['thumbnail_url'],
                        'local_path' => $thumbnailPath,
                        'status' => 'success'
                    ];
                    $hasNewImages = true;
                } else {
                    $imageMetadata['thumbnail'] = [
                        'attempted_at' => now()->toISOString(),
                        'original_url' => $tikTokShop['thumbnail_url'],
                        'status' => 'failed'
                    ];
                }
            }

            // Download BC profile image
            if (!empty($tikTokShop['store_authorized_bc_info']['bc_profile_image'])) {
                $bcImagePath = $this->imageService->downloadAndStore(
                    $tikTokShop['store_authorized_bc_info']['bc_profile_image'],
                    'bc_profiles',
                    'bc_' . ($tikTokShop['store_authorized_bc_info']['bc_id'] ?? 'unknown')
                );

                if ($bcImagePath) {
                    $shop->bc_profile_image_local_path = $bcImagePath;
                    $imageMetadata['bc_profile'] = [
                        'downloaded_at' => now()->toISOString(),
                        'original_url' => $tikTokShop['store_authorized_bc_info']['bc_profile_image'],
                        'local_path' => $bcImagePath,
                        'status' => 'success'
                    ];
                    $hasNewImages = true;
                } else {
                    $imageMetadata['bc_profile'] = [
                        'attempted_at' => now()->toISOString(),
                        'original_url' => $tikTokShop['store_authorized_bc_info']['bc_profile_image'],
                        'status' => 'failed'
                    ];
                }
            }

            // Update shop với image metadata
            if ($hasNewImages || !empty($imageMetadata)) {
                $shop->images_last_synced_at = now();
                $shop->image_sync_metadata = $imageMetadata;
                $shop->save();

                Log::info('Shop images synced', [
                    'shop_id' => $shop->shop_id,
                    'shop_name' => $shop->name,
                    'images_downloaded' => $hasNewImages ? 'yes' : 'no',
                    'metadata' => $imageMetadata
                ]);
            }

        } catch (Exception $e) {
            Log::error('Failed to download shop images', [
                'shop_id' => $shop->shop_id,
                'shop_name' => $shop->name,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * Map advertiser_id từ TikTok API sang advertiser_account_id trong database
     */
    protected function getAdvertiserAccountId(?string $advertiserIdFromApi): ?int
    {
        if (!$advertiserIdFromApi) {
            return null;
        }

        // Tìm advertiser account dựa trên advertiser_id
        $advertiserAccount = AdvertiserAccount::where('advertiser_id', $advertiserIdFromApi)->first();

        if ($advertiserAccount) {
            return $advertiserAccount->id;
        }

        // Log warning nếu không tìm thấy advertiser account
        Log::warning('Advertiser account not found for advertiser_id from API', [
            'advertiser_id_from_api' => $advertiserIdFromApi,
            'suggestion' => 'Run advertiser account sync first or check if advertiser_id is correct'
        ]);

        return null;
    }
}
