<?php

namespace App\Services;

use App\Models\User;
use Exception;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Support\Facades\Log;

class JobNotificationService
{
    /**
     * G<PERSON>i thông báo thành công cho job
     *
     * @param string $jobName Tên job (VD: "Đồng bộ TikTok", "Tạo nội dung ChatGPT")
     * @param string $message Nội dung thông báo chi tiết
     * @param array $stats Thống kê (optional)
     * @return void
     */
    public static function sendSuccessNotification(string $jobName, string $message, array $stats = []): void
    {
        try {
            $adminUsers = self::getAdminUsers();

            foreach ($adminUsers as $user) {
                FilamentNotification::make()
                    ->title("{$jobName} thành công")
                    ->body($message)
                    ->success()
                    ->sendToDatabase($user);
            }

            Log::info("Job success notification sent", [
                'job_name' => $jobName,
                'message' => $message,
                'stats' => $stats,
                'admin_count' => $adminUsers->count()
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send job success notification", [
                'job_name' => $jobName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Gửi thông báo lỗi cho job
     *
     * @param string $jobName Tên job
     * @param string $error Thông tin lỗi
     * @param array $context Thông tin bổ sung (optional)
     * @return void
     */
    public static function sendErrorNotification(string $jobName, string $error, array $context = []): void
    {
        try {
            $adminUsers = self::getAdminUsers();

            // Format error message for better readability
            $formattedError = self::formatErrorMessage($error);

            foreach ($adminUsers as $user) {
                FilamentNotification::make()
                    ->title("❌ Lỗi {$jobName}")
                    ->body($formattedError)
                    ->danger()
                    ->sendToDatabase($user);
            }

            Log::error("Job error notification sent", [
                'job_name' => $jobName,
                'error' => $error,
                'context' => $context,
                'admin_count' => $adminUsers->count()
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send job error notification", [
                'job_name' => $jobName,
                'original_error' => $error,
                'notification_error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Gửi thông báo cảnh báo cho job
     *
     * @param string $jobName Tên job
     * @param string $message Nội dung cảnh báo
     * @param array $context Thông tin bổ sung (optional)
     * @return void
     */
    public static function sendWarningNotification(string $jobName, string $message, array $context = []): void
    {
        try {
            $adminUsers = self::getAdminUsers();

            foreach ($adminUsers as $user) {
                FilamentNotification::make()
                    ->title("Cảnh báo {$jobName}")
                    ->body($message)
                    ->warning()
                    ->sendToDatabase($user);
            }

            Log::warning("Job warning notification sent", [
                'job_name' => $jobName,
                'message' => $message,
                'context' => $context,
                'admin_count' => $adminUsers->count()
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send job warning notification", [
                'job_name' => $jobName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Gửi thông báo thông tin cho job
     *
     * @param string $jobName Tên job
     * @param string $message Nội dung thông tin
     * @param array $context Thông tin bổ sung (optional)
     * @return void
     */
    public static function sendInfoNotification(string $jobName, string $message, array $context = []): void
    {
        try {
            $adminUsers = self::getAdminUsers();

            foreach ($adminUsers as $user) {
                FilamentNotification::make()
                    ->title($jobName)
                    ->body($message)
                    ->info()
                    ->sendToDatabase($user);
            }

            Log::info("Job info notification sent", [
                'job_name' => $jobName,
                'message' => $message,
                'context' => $context,
                'admin_count' => $adminUsers->count()
            ]);

        } catch (Exception $e) {
            Log::error("Failed to send job info notification", [
                'job_name' => $jobName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Lấy danh sách admin users
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private static function getAdminUsers(): \Illuminate\Database\Eloquent\Collection
    {
        // Kiểm tra xem có role system không
        if (method_exists(User::class, 'hasRole')) {
            return User::whereHas('roles', static function ($query) {
                $query->where('name', 'admin');
            })->get();
        }

        // Fallback: lấy user có email admin
        return User::where('email', '<EMAIL>')->get();
    }

    /**
     * Format error message for better readability
     *
     * @param string $error
     * @return string
     */
    private static function formatErrorMessage(string $error): string
    {
        // If error already contains formatting (emojis, line breaks), return as is
        if (str_contains($error, '❌') || str_contains($error, '🔧') || str_contains($error, "\n")) {
            return $error;
        }

        // Check for common TikTok API configuration errors
        if (str_contains($error, 'TikTok API configuration incomplete')) {
            return "🔧 **Cấu hình TikTok API chưa đầy đủ**\n\n" .
                "❌ **Lỗi**: {$error}\n\n" .
                "📋 **Hướng dẫn khắc phục**:\n" .
                "1. Truy cập Admin Panel: `/admin/tik-tok-settings`\n" .
                "2. Cấu hình đầy đủ: Access Token, App ID, App Secret\n" .
                "3. Chạy lệnh kiểm tra: `php artisan tiktok:debug-config`\n" .
                "4. Test kết nối: `php artisan tiktok:test-connection`";
        }

        if (str_contains($error, 'Access token is incorrect') || str_contains($error, 'Access token is null')) {
            return "🔑 **Access Token không hợp lệ**\n\n" .
                "❌ **Lỗi**: {$error}\n\n" .
                "📋 **Hướng dẫn khắc phục**:\n" .
                "1. Lấy Access Token mới từ TikTok Business API Dashboard\n" .
                "2. Cập nhật trong Admin Panel: `/admin/tik-tok-settings`\n" .
                "3. Đảm bảo token có quyền truy cập Advertiser Account\n" .
                "4. Kiểm tra lại: `php artisan tiktok:debug-config`";
        }

        if (str_contains($error, 'rate limit') || str_contains($error, 'too many requests')) {
            return "⚠️ **Vượt quá giới hạn API calls**\n\n" .
                "❌ **Lỗi**: {$error}\n\n" .
                "📋 **Hướng dẫn khắc phục**:\n" .
                "1. Chờ một lúc rồi thử lại\n" .
                "2. Giảm tần suất sync trong cấu hình\n" .
                "3. Kiểm tra rate limit settings";
        }

        // Generic error formatting
        return "❌ **Lỗi**: {$error}\n\n" .
            "📋 **Hướng dẫn chung**:\n" .
            "1. Kiểm tra log chi tiết để biết thêm thông tin\n" .
            "2. Xem cấu hình TikTok: `/admin/tik-tok-settings`\n" .
            "3. Chạy: `php artisan tiktok:debug-config`";
    }

    /**
     * Format thống kê thành message dễ đọc
     *
     * @param array $stats
     * @return string
     */
    public static function formatStatsMessage(array $stats): string
    {
        $parts = [];

        if (isset($stats['created'])) {
            $parts[] = "{$stats['created']} tạo mới";
        }

        if (isset($stats['updated'])) {
            $parts[] = "{$stats['updated']} cập nhật";
        }

        if (isset($stats['deleted']) || isset($stats['deactivated'])) {
            $count = $stats['deleted'] ?? $stats['deactivated'];
            $parts[] = "{$count} vô hiệu hóa";
        }

        if (isset($stats['processed'])) {
            $parts[] = "{$stats['processed']} xử lý";
        }

        if (isset($stats['errors'])) {
            $parts[] = "{$stats['errors']} lỗi";
        }

        // Handle sessions data if present
        if (isset($stats['sessions']) && is_array($stats['sessions'])) {
            $sessionsData = $stats['sessions'];
            if (isset($sessionsData['total_synced']) && $sessionsData['total_synced'] > 0) {
                $parts[] = "{$sessionsData['total_synced']} sessions đồng bộ";
            }
            if (isset($sessionsData['total_errors']) && $sessionsData['total_errors'] > 0) {
                $parts[] = "{$sessionsData['total_errors']} sessions lỗi";
            }
        }

        return implode(', ', $parts);
    }
}
