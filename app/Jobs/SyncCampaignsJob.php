<?php

namespace App\Jobs;

use App\Models\AdvertiserAccount;
use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use App\Traits\JobNotificationTrait;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncCampaignsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    public $timeout = 600; // 10 minutes
    public $tries = 3;

    protected ?string $advertiserId;
    protected bool $syncSessions;

    /**
     * Create a new job instance.
     */
    public function __construct(?string $advertiserId = null, bool $syncSessions = false, bool $notifyUsers = true)
    {
        $this->jobDisplayName = 'Đồng bộ Campaigns';
        $this->advertiserId = $advertiserId;
        $this->syncSessions = $syncSessions;
        $this->notifyUsers = $notifyUsers;
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping($this->advertiserId ?? 'all')];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting campaigns sync from TikTok API', [
                'advertiser_id' => $this->advertiserId,
                'sync_sessions' => $this->syncSessions
            ]);

            $apiService = new TikTokApiService();
            $syncService = new TikTokSyncService($apiService);

            if ($this->advertiserId) {
                // Sync for specific advertiser
                $result = $this->syncForSpecificAdvertiser($apiService, $syncService);
            } else {
                // Sync for all advertisers
                $result = $this->syncForAllAdvertisers($syncService);
            }

            if ($result['success']) {
                Log::info('Campaigns sync job completed successfully', $result);
                $this->sendStatsNotification($result);

                // Dispatch detail sync jobs for newly synced campaigns
                $this->dispatchDetailSyncJobs($result);
            } else {
                Log::error('Campaigns sync job failed', ['error' => $result['error']]);
                $this->sendErrorNotification($result['error']);
                $this->fail(new Exception($result['error']));
            }

        } catch (Exception $e) {
            Log::error('SyncCampaignsJob exception', [
                'error' => $e->getMessage(),
                'advertiser_id' => $this->advertiserId,
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendErrorNotification($e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync campaigns for specific advertiser
     */
    private function syncForSpecificAdvertiser(TikTokApiService $apiService, TikTokSyncService $syncService): array
    {
        $apiService->setAdvertiserId($this->advertiserId);
        $result = $syncService->syncCampaigns();

        if ($result['success'] && $this->syncSessions) {
            // Also sync sessions for campaigns if requested
            $sessionsResult = $syncService->syncSessionsForAdvertiser($this->advertiserId);
            $result['sessions'] = $sessionsResult;
        }

        return $result;
    }

    /**
     * Sync campaigns for all advertisers
     */
    private function syncForAllAdvertisers(TikTokSyncService $syncService): array
    {
        // Get eligible advertiser accounts
        $advertiserAccounts = AdvertiserAccount::active()
            ->where(function ($query) {
                $query->whereHas('shops')
                    ->orWhereHas('shopsByAdvertiserId');
            })
            ->with(['shops', 'shopsByAdvertiserId'])
            ->get();

        if ($advertiserAccounts->isEmpty()) {
            return [
                'success' => false,
                'error' => 'Không tìm thấy advertiser accounts có shops để đồng bộ'
            ];
        }

        // Sync campaigns for all advertiser accounts
        return $syncService->syncCampaignsForAllAdvertisers($advertiserAccounts);
    }

    /**
     * Dispatch detail sync jobs for campaigns that need details
     */
    private function dispatchDetailSyncJobs(array $result): void
    {
        try {
            // Get campaigns that were created or updated and need details sync
            $campaignsToSync = $this->getCampaignsNeedingDetailSync();

            if ($campaignsToSync->isEmpty()) {
                Log::info('No campaigns need detail sync');
                return;
            }

            $dispatchedCount = 0;
            foreach ($campaignsToSync as $campaign) {
                // Dispatch detail sync job with delay to avoid rate limiting
                SyncCampaignDetailsJob::dispatch(
                    $campaign->campaign_id,
                    $campaign->advertiser_id,
                    false // Don't notify for individual detail syncs
                )->delay(now()->addSeconds($dispatchedCount * 2)); // 2 second delay between jobs

                $dispatchedCount++;
            }

            Log::info('Dispatched campaign detail sync jobs', [
                'count' => $dispatchedCount,
                'campaigns' => $campaignsToSync->pluck('campaign_id')->toArray()
            ]);

            if ($this->notifyUsers && $dispatchedCount > 0) {
                $this->sendInfoNotification(
                    "Đã tạo {$dispatchedCount} jobs để đồng bộ chi tiết campaigns"
                );
            }

        } catch (Exception $e) {
            Log::error('Failed to dispatch campaign detail sync jobs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get campaigns that need detail sync
     */
    private function getCampaignsNeedingDetailSync()
    {
        $query = Campaign::whereNull('details_synced_at')
            ->orWhere('details_synced_at', '<', now()->subHours(24)); // Re-sync if older than 24 hours

        if ($this->advertiserId) {
            $query->where('advertiser_id', $this->advertiserId);
        }

        return $query->select('campaign_id', 'advertiser_id', 'name')
            ->limit(50) // Limit to avoid too many jobs at once
            ->get();
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('SyncCampaignsJob failed permanently', [
            'error' => $exception->getMessage(),
            'advertiser_id' => $this->advertiserId,
            'attempts' => $this->attempts()
        ]);

        $this->sendFailedNotification($exception, $this->attempts());
    }
}
