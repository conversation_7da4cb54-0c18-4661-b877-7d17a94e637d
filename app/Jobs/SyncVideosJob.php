<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\VideoSyncService;
use App\Helpers\ErrorHandler;
use App\Traits\JobNotificationTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;

class SyncVideosJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    public $timeout = 900; // 15 minutes
    public $tries = 3;

    protected ?string $campaignId;
    protected array $campaignIds;
    protected array $options;
    protected string $syncType;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $syncType = 'single',
        ?string $campaignId = null,
        array $campaignIds = [],
        array $options = [],
        bool $notifyUsers = true
    ) {
        $this->jobDisplayName = 'Đồng bộ Videos TikTok';
        $this->syncType = $syncType;
        $this->campaignId = $campaignId;
        $this->campaignIds = $campaignIds;
        $this->options = $options;
        $this->notifyUsers = $notifyUsers;
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        $key = $this->syncType === 'single' ? $this->campaignId : 'bulk_videos';
        return [new WithoutOverlapping($key)];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting videos sync job', [
                'sync_type' => $this->syncType,
                'campaign_id' => $this->campaignId,
                'campaign_ids' => $this->campaignIds,
                'options' => $this->options
            ]);

            $apiService = new TikTokApiService();
            $videoSyncService = new VideoSyncService($apiService);

            $result = match($this->syncType) {
                'single' => $this->syncSingleCampaign($videoSyncService),
                'multiple' => $this->syncMultipleCampaigns($videoSyncService),
                'all_active' => $this->syncAllActiveCampaigns($videoSyncService),
                default => throw new \Exception("Unknown sync type: {$this->syncType}")
            };

            $this->handleSyncResult($result);

            Log::info('Videos sync job completed successfully', [
                'sync_type' => $this->syncType,
                'result' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Videos sync job failed', [
                'sync_type' => $this->syncType,
                'campaign_id' => $this->campaignId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendFailureNotificationForVideos($e->getMessage());
            throw $e;
        }
    }

    /**
     * Sync videos for a single campaign
     */
    private function syncSingleCampaign(VideoSyncService $videoSyncService): array
    {
        if (!$this->campaignId) {
            throw new \Exception('Campaign ID is required for single campaign sync');
        }

        $campaign = Campaign::where('campaign_id', $this->campaignId)->first();
        if (!$campaign) {
            throw new \Exception("Campaign not found: {$this->campaignId}");
        }

        return $videoSyncService->syncVideosForCampaign($campaign, $this->options);
    }

    /**
     * Sync videos for multiple campaigns
     */
    private function syncMultipleCampaigns(VideoSyncService $videoSyncService): array
    {
        if (empty($this->campaignIds)) {
            throw new \Exception('Campaign IDs are required for multiple campaigns sync');
        }

        return $videoSyncService->syncVideosForMultipleCampaigns($this->campaignIds, $this->options);
    }

    /**
     * Sync videos for all active campaigns
     */
    private function syncAllActiveCampaigns(VideoSyncService $videoSyncService): array
    {
        $activeCampaigns = Campaign::where('status', 'active')
            ->whereNotNull('store_id')
            ->whereNotNull('store_authorized_bc_id')
            ->pluck('campaign_id')
            ->toArray();

        if (empty($activeCampaigns)) {
            return [
                'success' => true,
                'message' => 'No active campaigns found to sync',
                'overall_stats' => [
                    'campaigns_processed' => 0,
                    'campaigns_success' => 0,
                    'campaigns_failed' => 0,
                    'total_videos' => 0,
                ]
            ];
        }

        return $videoSyncService->syncVideosForMultipleCampaigns($activeCampaigns, $this->options);
    }

    /**
     * Handle sync result and send notifications
     */
    private function handleSyncResult(array $result): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        if (ErrorHandler::isSuccess($result)) {
            $this->sendSuccessNotificationForVideos($result);
        } else {
            $this->sendFailureNotificationForVideos($result['message'] ?? 'Unknown error');
        }
    }

    /**
     * Send success notification using JobNotificationTrait
     */
    private function sendSuccessNotificationForVideos(array $result): void
    {
        $message = $this->formatSuccessMessage($result);

        // Use the trait's sendSuccessNotification method
        $this->sendSuccessNotification($message, $result['stats'] ?? []);
    }

    /**
     * Send failure notification using JobNotificationTrait
     */
    private function sendFailureNotificationForVideos(string $error): void
    {
        // Use the trait's sendErrorNotification method
        $this->sendErrorNotification(
            "Video synchronization failed: {$error}"
        );
    }

    /**
     * Format success message based on sync type
     */
    private function formatSuccessMessage(array $result): string
    {
        if ($this->syncType === 'single') {
            $stats = $result['data']['stats'] ?? [];
            return sprintf(
                'Successfully synced %d videos (%d created, %d updated)',
                $stats['total'] ?? 0,
                $stats['created'] ?? 0,
                $stats['updated'] ?? 0
            );
        } else {
            $overallStats = $result['overall_stats'] ?? [];
            return sprintf(
                'Successfully processed %d campaigns with %d total videos (%d created, %d updated)',
                $overallStats['campaigns_success'] ?? 0,
                $overallStats['total_videos'] ?? 0,
                $overallStats['total_created'] ?? 0,
                $overallStats['total_updated'] ?? 0
            );
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Videos sync job failed permanently', [
            'sync_type' => $this->syncType,
            'campaign_id' => $this->campaignId,
            'campaign_ids' => $this->campaignIds,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        if ($this->notifyUsers) {
            $this->sendFailureNotificationForVideos($exception->getMessage());
        }
    }

    /**
     * Get unique job ID for queue deduplication
     */
    public function uniqueId(): string
    {
        if ($this->syncType === 'single') {
            return "sync_videos_single_{$this->campaignId}";
        } elseif ($this->syncType === 'multiple') {
            return "sync_videos_multiple_" . md5(implode(',', $this->campaignIds));
        } else {
            return "sync_videos_all_active";
        }
    }
}
