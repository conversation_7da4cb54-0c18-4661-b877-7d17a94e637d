<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Traits\JobNotificationTrait;
use App\Helpers\ErrorHandler;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SyncCampaignDetailsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected string $campaignId;
    protected string $advertiserId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $campaignId, string $advertiserId, bool $notifyUsers = false)
    {
        $this->jobDisplayName = 'Đồng bộ Chi tiết Campaign';
        $this->campaignId = $campaignId;
        $this->advertiserId = $advertiserId;
        $this->notifyUsers = $notifyUsers;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting campaign details sync', [
                'campaign_id' => $this->campaignId,
                'advertiser_id' => $this->advertiserId
            ]);

            // Find the campaign
            $campaign = Campaign::where('campaign_id', $this->campaignId)
                ->where('advertiser_id', $this->advertiserId)
                ->first();

            if (!$campaign) {
                throw new Exception("Campaign not found: {$this->campaignId}");
            }

            // Initialize API service
            $apiService = new TikTokApiService();
            $apiService->setAdvertiserId($this->advertiserId);

            // Get campaign details from API
            $response = $apiService->getCampaignDetails($this->campaignId);

            // Check if response is successful (TikTok API returns success=true or data with code=0)
            $isSuccess = ($response['success'] ?? false) === true ||
                        (isset($response['data']['code']) && $response['data']['code'] === 0);

            if (!$isSuccess) {
                $errorMessage = $response['error'] ?? $response['data']['message'] ?? 'Unknown error occurred';
                throw new Exception("Failed to get campaign details: {$errorMessage}");
            }

            // Update campaign with detailed information
            $this->updateCampaignDetails($campaign, $response['data']);

            Log::info('Campaign details sync completed successfully', [
                'campaign_id' => $this->campaignId,
                'campaign_name' => $campaign->name
            ]);

            if ($this->notifyUsers) {
                $this->sendSuccessNotification(
                    "Chi tiết campaign '{$campaign->name}' đã được cập nhật thành công"
                );
            }

        } catch (Exception $e) {
            Log::error('SyncCampaignDetailsJob failed', [
                'campaign_id' => $this->campaignId,
                'advertiser_id' => $this->advertiserId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($this->notifyUsers) {
                $this->sendErrorNotification(
                    "Lỗi khi đồng bộ chi tiết campaign {$this->campaignId}: " . $e->getMessage()
                );
            }

            throw $e;
        }
    }

    /**
     * Update campaign with detailed information
     */
    private function updateCampaignDetails(Campaign $campaign, array $data): void
    {
        $updateData = [
            'store_id' => $data['store_id'] ?? null,
            'store_authorized_bc_id' => $data['store_authorized_bc_id'] ?? null,
            'shopping_ads_type' => $data['shopping_ads_type'] ?? null,
            'product_specific_type' => $data['product_specific_type'] ?? null,
            'optimization_goal' => $data['optimization_goal'] ?? null,
            'roi_protection_enabled' => $data['roi_protection_enabled'] ?? false,
            'deep_bid_type' => $data['deep_bid_type'] ?? null,
            'roas_bid' => $data['roas_bid'] ?? null,
            'budget_details' => $data['budget'] ?? null,
            'schedule_type' => $data['schedule_type'] ?? null,
            'schedule_start_time' => $this->parseDateTime($data['schedule_start_time'] ?? null),
            'schedule_end_time' => $this->parseDateTime($data['schedule_end_time'] ?? null),
            'placements' => isset($data['placements']) ? json_encode($data['placements']) : null,
            'location_ids' => isset($data['location_ids']) ? json_encode($data['location_ids']) : null,
            'age_groups' => isset($data['age_groups']) ? json_encode($data['age_groups']) : null,
            'product_video_specific_type' => $data['product_video_specific_type'] ?? null,
            'affiliate_posts_enabled' => $data['affiliate_posts_enabled'] ?? false,
            'item_group_ids' => isset($data['item_group_ids']) ? json_encode($data['item_group_ids']) : null,
            'identity_list' => isset($data['identity_list']) ? json_encode($data['identity_list']) : null,
            'item_list' => isset($data['item_list']) ? json_encode($data['item_list']) : null,
            'custom_anchor_video_list' => isset($data['custom_anchor_video_list']) ? json_encode($data['custom_anchor_video_list']) : null,
            'campaign_custom_anchor_video_id' => $data['campaign_custom_anchor_video_id'] ?? null,
            'details_synced_at' => now(),
        ];

        // Remove null values
        $updateData = array_filter($updateData, function ($value) {
            return $value !== null;
        });

        $campaign->update($updateData);

        Log::info('Campaign details updated', [
            'campaign_id' => $campaign->campaign_id,
            'updated_fields' => array_keys($updateData)
        ]);
    }

    /**
     * Parse datetime string to Carbon instance
     */
    private function parseDateTime(?string $datetime): ?\Carbon\Carbon
    {
        if (empty($datetime)) {
            return null;
        }

        try {
            return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $datetime, 'UTC');
        } catch (Exception $e) {
            Log::warning('Failed to parse datetime', [
                'datetime' => $datetime,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncCampaignDetailsJob failed permanently', [
            'campaign_id' => $this->campaignId,
            'advertiser_id' => $this->advertiserId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        if ($this->notifyUsers) {
            $this->sendFailedNotification($exception, $this->attempts());
        }
    }
}
