<?php

namespace App\Jobs;

use App\Models\AdvertiserAccount;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use App\Traits\JobNotificationTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SyncShopsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected ?string $advertiserId;

    /**
     * Create a new job instance.
     */
    public function __construct(?string $advertiserId = null)
    {
        $this->jobDisplayName = 'Đồng bộ TikTok Shops';
        $this->advertiserId = $advertiserId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting shops sync from TikTok API', [
                'advertiser_id' => $this->advertiserId
            ]);

            $apiService = new TikTokApiService();

            // Nếu không có advertiser ID cụ thể, thử với tất cả advertiser accounts
            if ($this->advertiserId) {
                $advertiserIds = [$this->advertiserId];
            } else {
                // Lấy một số advertiser accounts để test
                $advertiserIds = AdvertiserAccount::take(10)->pluck('advertiser_id')->toArray();
            }

            $totalSynced = 0;
            $totalCreated = 0;
            $totalUpdated = 0;
            $errors = 0;

            foreach ($advertiserIds as $advertiserId) {
                try {
                    $apiService->setAdvertiserId($advertiserId);
                    $syncService = new TikTokSyncService($apiService);

                    $result = $syncService->syncShops();

                    if ($result['success']) {
                        $totalSynced += $result['synced'] ?? 0;
                        $totalCreated += $result['created'] ?? 0;
                        $totalUpdated += $result['updated'] ?? 0;

                        // Chỉ log nếu có shops được sync
                        if (($result['synced'] ?? 0) > 0) {
                            Log::info('Shops sync completed for advertiser', [
                                'advertiser_id' => $advertiserId,
                                'synced' => $result['synced'] ?? 0,
                                'created' => $result['created'] ?? 0,
                                'updated' => $result['updated'] ?? 0
                            ]);
                        }
                    } else {
                        $errors++;
                        Log::warning('Shops sync failed for advertiser', [
                            'advertiser_id' => $advertiserId,
                            'error' => $result['error'] ?? 'Unknown error'
                        ]);
                    }
                } catch (Exception $e) {
                    $errors++;
                    Log::error('Exception during shops sync for advertiser', [
                        'advertiser_id' => $advertiserId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('Shops sync job completed', [
                'total_synced' => $totalSynced,
                'total_created' => $totalCreated,
                'total_updated' => $totalUpdated,
                'errors' => $errors,
                'advertiser_accounts_processed' => count($advertiserIds)
            ]);

            // Send notification
            $this->sendStatsNotification([
                'processed' => $totalSynced,
                'created' => $totalCreated,
                'updated' => $totalUpdated,
                'errors' => $errors
            ]);

        } catch (Exception $e) {
            Log::error('SyncShopsJob failed', [
                'error' => $e->getMessage(),
                'advertiser_id' => $this->advertiserId
            ]);

            $this->sendErrorNotification($e->getMessage());
            $this->fail($e);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('SyncShopsJob failed permanently', [
            'error' => $exception->getMessage(),
            'advertiser_id' => $this->advertiserId,
            'attempts' => $this->attempts()
        ]);

        $this->sendFailedNotification($exception, $this->attempts());
    }
}
