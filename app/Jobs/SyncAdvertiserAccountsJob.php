<?php

namespace App\Jobs;

use App\Services\TikTok\AdvertiserAccountSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Traits\JobNotificationTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncAdvertiserAccountsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Indicate if the job should be marked as failed on timeout.
     */
    public bool $failOnTimeout = true;



    /**
     * Create a new job instance.
     */
    public function __construct(bool $notifyUsers = true)
    {
        $this->jobDisplayName = 'Đồng bộ Advertiser Accounts';
        $this->notifyUsers = $notifyUsers;
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
            new WithoutOverlapping('sync-advertiser-accounts'),
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('SyncAdvertiserAccountsJob started');

        try {
            // Khởi tạo services
            $apiService = new TikTokApiService();
            $syncService = new AdvertiserAccountSyncService($apiService);

            // Thực hiện sync
            $result = $syncService->syncAdvertiserAccounts();

            if ($result['success']) {
                Log::info('SyncAdvertiserAccountsJob completed successfully', $result['stats']);
                $this->sendStatsNotification($result['stats']);
            } else {
                $errorMessage = $result['error'] ?? 'Unknown sync error';
                Log::error('SyncAdvertiserAccountsJob failed', [
                    'error' => $errorMessage,
                    'error_type' => $result['error_type'] ?? 'unknown',
                    'context' => $result['context'] ?? []
                ]);
                $this->sendErrorNotification($errorMessage);
                $this->fail(new \Exception($errorMessage));
            }

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();

            // Provide more user-friendly error messages
            $friendlyMessage = $this->getFriendlyErrorMessage($errorMessage);

            Log::error('SyncAdvertiserAccountsJob exception', [
                'error' => $errorMessage,
                'friendly_error' => $friendlyMessage,
                'exception_class' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendErrorNotification($friendlyMessage);
            throw new \Exception($friendlyMessage, $e->getCode(), $e);
        }
    }



    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncAdvertiserAccountsJob failed permanently', [
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        $this->sendFailedNotification($exception, $this->attempts());
    }

    /**
     * Convert technical error messages to user-friendly messages
     */
    protected function getFriendlyErrorMessage(string $errorMessage): string
    {
        // Configuration errors
        if (str_contains($errorMessage, 'TikTok API configuration error')) {
            return "❌ Lỗi cấu hình TikTok API\n\n" .
                   "🔧 Cần kiểm tra và cập nhật thông tin API credentials:\n" .
                   "• Access Token\n" .
                   "• App ID\n" .
                   "• App Secret\n\n" .
                   "📋 Hướng dẫn khắc phục:\n" .
                   "1. Truy cập Admin Panel: /admin/tik-tok-settings\n" .
                   "2. Hoặc chạy: php artisan tiktok:setup-credentials\n" .
                   "3. Kiểm tra: php artisan tiktok:debug-config";
        }

        // Authentication errors
        if (str_contains($errorMessage, 'authentication') || str_contains($errorMessage, 'access denied')) {
            return "❌ Lỗi xác thực TikTok API\n\n" .
                   "🔐 Access Token có thể đã hết hạn hoặc không hợp lệ\n\n" .
                   "📋 Hướng dẫn khắc phục:\n" .
                   "1. Kiểm tra Access Token trong Admin Panel\n" .
                   "2. Tạo Access Token mới từ TikTok Business API Dashboard\n" .
                   "3. Cập nhật trong hệ thống";
        }

        // Network errors
        if (str_contains($errorMessage, 'network') || str_contains($errorMessage, 'connection')) {
            return "❌ Lỗi kết nối mạng\n\n" .
                   "🌐 Không thể kết nối đến TikTok API\n\n" .
                   "📋 Hướng dẫn khắc phục:\n" .
                   "1. Kiểm tra kết nối internet\n" .
                   "2. Thử lại sau vài phút\n" .
                   "3. Kiểm tra firewall/proxy settings";
        }

        // TikTok API specific errors
        if (str_contains($errorMessage, 'TikTok API returned error code')) {
            return "❌ Lỗi từ TikTok API\n\n" .
                   "🔧 " . $errorMessage . "\n\n" .
                   "📋 Hướng dẫn khắc phục:\n" .
                   "1. Kiểm tra TikTok API status\n" .
                   "2. Xem TikTok API documentation\n" .
                   "3. Liên hệ TikTok support nếu cần";
        }

        // Generic fallback
        return "❌ Lỗi đồng bộ Advertiser Accounts\n\n" .
               "🔧 Chi tiết: " . $errorMessage . "\n\n" .
               "📋 Hướng dẫn khắc phục:\n" .
               "1. Kiểm tra logs để biết thêm chi tiết\n" .
               "2. Chạy: php artisan tiktok:debug-config\n" .
               "3. Liên hệ admin nếu vấn đề tiếp tục";
    }
}
