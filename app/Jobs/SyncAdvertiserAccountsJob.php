<?php

namespace App\Jobs;

use App\Services\TikTok\AdvertiserAccountSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Traits\JobNotificationTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncAdvertiserAccountsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Indicate if the job should be marked as failed on timeout.
     */
    public bool $failOnTimeout = true;



    /**
     * Create a new job instance.
     */
    public function __construct(bool $notifyUsers = true)
    {
        $this->jobDisplayName = 'Đồng bộ Advertiser Accounts';
        $this->notifyUsers = $notifyUsers;
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
            new WithoutOverlapping('sync-advertiser-accounts'),
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('SyncAdvertiserAccountsJob started');

        try {
            // Khởi tạo services
            $apiService = new TikTokApiService();
            $syncService = new AdvertiserAccountSyncService($apiService);

            // Thực hiện sync
            $result = $syncService->syncAdvertiserAccounts();

            if ($result['success']) {
                Log::info('SyncAdvertiserAccountsJob completed successfully', $result['stats']);
                $this->sendStatsNotification($result['stats']);
            } else {
                Log::error('SyncAdvertiserAccountsJob failed', ['error' => $result['error']]);
                $this->sendErrorNotification($result['error']);
                $this->fail(new \Exception($result['error']));
            }

        } catch (\Exception $e) {
            Log::error('SyncAdvertiserAccountsJob exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendErrorNotification($e->getMessage());
            throw $e;
        }
    }



    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncAdvertiserAccountsJob failed permanently', [
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        $this->sendFailedNotification($exception, $this->attempts());
    }
}
