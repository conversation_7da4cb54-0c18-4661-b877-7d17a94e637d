<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\TikTok\TikTokSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Traits\JobNotificationTrait;
use Illuminate\Support\Facades\Log;
use Exception;

class SyncTikTokData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, JobNotificationTrait;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected string $syncType;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(string $syncType = 'all', array $options = [])
    {
        $this->jobDisplayName = 'Đồng bộ TikTok Data';
        $this->syncType = $syncType;
        $this->options = $options;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $apiService = new TikTokApiService();
            $syncService = new TikTokSyncService($apiService);

            Log::info('TikTok Data Sync Job Started', [
                'type' => $this->syncType,
                'options' => $this->options
            ]);

            $result = match($this->syncType) {
                'all' => $syncService->syncAll(),
                'shops' => $syncService->syncShops(),
                'campaigns' => $syncService->syncCampaigns(),
                'identities' => $syncService->syncIdentities(),
                'authorizations' => $syncService->syncExclusiveAuthorizations(),
                'reports' => $syncService->syncReports($this->options['days'] ?? 30),
                default => throw new Exception("Unknown sync type: {$this->syncType}")
            };

            Log::info('TikTok Data Sync Job Completed', [
                'type' => $this->syncType,
                'result' => $result
            ]);

            // Send success notification
            $this->sendSuccessNotification("Đồng bộ {$this->syncType} hoàn thành thành công", $result);

        } catch (Exception $e) {
            Log::error('TikTok Data Sync Job Failed', [
                'type' => $this->syncType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendErrorNotification($e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('TikTok Data Sync Job Failed Permanently', [
            'type' => $this->syncType,
            'error' => $exception->getMessage()
        ]);

        $this->sendFailedNotification($exception, $this->attempts());
    }
}
