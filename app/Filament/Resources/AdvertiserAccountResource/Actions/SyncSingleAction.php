<?php

namespace App\Filament\Resources\AdvertiserAccountResource\Actions;

use App\Models\AdvertiserAccount;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncSingleAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_single';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Đồng bộ')
            ->icon('heroicon-o-arrow-path')
            ->color('warning')
            ->requiresConfirmation()
            ->modalHeading('Đồng bộ advertiser account')
            ->modalDescription('Đánh dấu account này đã được sync. <PERSON><PERSON> sync thực sự từ API, sử dụng nút "Đồng bộ tất cả" ở trên.')
            ->action(function (AdvertiserAccount $record) {
                $this->syncSingle($record);
            });
    }

    protected function syncSingle(AdvertiserAccount $record): void
    {
        try {
            $record->markAsSynced();

            Notification::make()
                ->title('Đã cập nhật thời gian sync')
                ->body("Account {$record->advertiser_name} đã được đánh dấu sync.")
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi cập nhật')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
