<?php

namespace App\Filament\Resources\AdvertiserAccountResource\Actions;

use App\Jobs\SyncAdvertiserAccountsJob;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncAllFromApiAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_all';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Đồng bộ tất cả từ TikTok API')
            ->icon('heroicon-o-cloud-arrow-down')
            ->color('success')
            ->requiresConfirmation()
            ->modalHeading('Đồng bộ từ TikTok API')
            ->modalDescription('Thao tác này sẽ lấy danh sách advertiser accounts mới nhất từ TikTok Business API và cập nhật vào hệ thống. Quá trình có thể mất vài phút.')
            ->modalSubmitActionLabel('Bắt đầu đồng bộ')
            ->action(function () {
                $this->syncAllFromApi();
            });
    }

    protected function syncAllFromApi(): void
    {
        try {
            // Dispatch job để sync tất cả advertiser accounts
            SyncAdvertiserAccountsJob::dispatch(true);

            Notification::make()
                ->title('Đã bắt đầu đồng bộ')
                ->body('Đang đồng bộ advertiser accounts từ TikTok API. Bạn sẽ nhận được thông báo khi hoàn thành.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi bắt đầu đồng bộ')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
