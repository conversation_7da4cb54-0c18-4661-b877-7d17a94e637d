<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CampaignReportResource\Actions\AiPerformanceAnalysisAction;
use App\Filament\Resources\CampaignReportResource\Actions\SyncReportsAction;
use App\Filament\Resources\CampaignReportResource\Actions\SyncReportsWithDateRangeAction;
use App\Filament\Resources\CampaignReportResource\Pages;
use App\Helpers\DateHelper;
use App\Models\CampaignReport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CampaignReportResource extends Resource
{
    protected static ?string $model = CampaignReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = 'Báo cáo & Phân tích';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Báo cáo chiến dịch';

    protected static ?string $pluralModelLabel = 'Báo cáo chiến dịch';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('report_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DatePicker::make('report_date')
                    ->label('Ngày báo cáo')
                    ->required()
                    ->displayFormat('d/m/Y')
                    ->format('Y-m-d'),
                Forms\Components\TextInput::make('total_cost')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('orders_count')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('gross_revenue')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('cost_per_order')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('roi')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('impressions')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('clicks')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('ctr')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\TextInput::make('conversion_rate')
                    ->required()
                    ->numeric()
                    ->default(0.00),
                Forms\Components\Select::make('campaign_id')
                    ->relationship('campaign', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('report_id')
                    ->label('Report ID')
                    ->searchable()
                    ->formatStateUsing(function ($state) {
                        // Extract meaningful parts: TYPE_CAMPAIGN_DATE
                        // From: gmv_max_PRODUCT_1838424336063745_20250724
                        // To: PRODUCT_...3745_20250724
                        if (preg_match('/^gmv_max_(\w+)_(\d+)_(\d{8})$/', $state, $matches)) {
                            $type = $matches[1];
                            $campaignId = $matches[2];
                            $date = $matches[3];

                            // Show type + last 4 digits of campaign + date
                            $shortCampaignId = '...' . substr($campaignId, -4);
                            return "{$type}_{$shortCampaignId}_{$date}";
                        }

                        // Fallback to simple truncation
                        return strlen($state) > 25 ? substr($state, 0, 22) . '...' : $state;
                    })
                    ->tooltip(fn($record) => $record->report_id)
                    ->copyable()
                    ->copyMessage('Report ID copied')
                    ->copyMessageDuration(1500),
                Tables\Columns\TextColumn::make('campaign.name')
                    ->label('Campaign')
                    ->formatStateUsing(function ($record) {
                        $campaign = $record->campaign;
                        if (!$campaign) return 'N/A';

                        // Show campaign name with short campaign ID
                        $shortId = '...' . substr($campaign->campaign_id, -4);
                        return "{$campaign->name} ({$shortId})";
                    })
                    ->tooltip(function ($record) {
                        $campaign = $record->campaign;
                        return $campaign ? "Campaign ID: {$campaign->campaign_id}\nName: {$campaign->name}" : 'No campaign';
                    })
                    ->searchable(['name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('report_date')
                    ->label('Ngày báo cáo')
                    ->date('d/m/Y')
                    ->sortable()
                    ->tooltip(DateHelper::tooltipClosure()),
                Tables\Columns\TextColumn::make('total_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('orders_count')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('gross_revenue')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_order')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('roi')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('impressions')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('clicks')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ctr')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('conversion_rate')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->headerActions([
                AiPerformanceAnalysisAction::make(),
                SyncReportsAction::make(),
                SyncReportsWithDateRangeAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaignReports::route('/'),
            'create' => Pages\CreateCampaignReport::route('/create'),
            'edit' => Pages\EditCampaignReport::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
