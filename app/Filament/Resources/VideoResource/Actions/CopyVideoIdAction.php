<?php

namespace App\Filament\Resources\VideoResource\Actions;

use App\Models\Video;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class CopyVideoIdAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'copy_video_id';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Copy Video ID')
            ->icon('heroicon-o-clipboard')
            ->color('gray')
            ->tooltip('Copy video ID to clipboard')
            ->action(function (Video $record) {
                $this->copyVideoId($record);
            });
    }

    protected function copyVideoId(Video $record): void
    {
        // Since we can't directly access clipboard from server-side,
        // we'll show a modal with the video ID that user can copy
        Notification::make()
            ->title('Video ID')
            ->body("Video ID: {$record->video_id}")
            ->info()
            ->duration(10000)
            ->actions([
                \Filament\Notifications\Actions\Action::make('copy')
                    ->label('Copy to Clipboard')
                    ->button()
                    ->close()
                    ->extraAttributes([
                        'onclick' => "navigator.clipboard.writeText('{$record->video_id}').then(() => {
                            new FilamentNotification()
                                .title('Copied!')
                                .body('Video ID copied to clipboard')
                                .success()
                                .send();
                        });"
                    ]),
            ])
            ->send();
    }
}
