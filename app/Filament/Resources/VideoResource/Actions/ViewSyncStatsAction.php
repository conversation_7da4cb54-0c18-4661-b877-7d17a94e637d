<?php

namespace App\Filament\Resources\VideoResource\Actions;

use App\Models\Video;
use App\Models\Campaign;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Illuminate\Support\HtmlString;

class ViewSyncStatsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'view_sync_stats';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Statistics')
            ->icon('heroicon-o-chart-bar')
            ->color('gray')
            ->modalHeading('Video Sync Statistics')
            ->modalDescription('Overview of video synchronization data')
            ->modalWidth(MaxWidth::FourExtraLarge)
            ->form($this->getStatsForm())
            ->modalSubmitAction(false)
            ->modalCancelActionLabel('Close');
    }

    protected function getStatsForm(): array
    {
        $stats = $this->calculateStats();

        return [
            Section::make('Overall Statistics')
                ->description('General video and campaign statistics')
                ->schema([
                    Grid::make(3)
                        ->schema([
                            Placeholder::make('total_videos')
                                ->label('Total Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-primary-600'>
                                        {$stats['total_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Videos in database
                                    </div>
                                ")),

                            Placeholder::make('active_videos')
                                ->label('Active Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-success-600'>
                                        {$stats['active_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        {$stats['active_percentage']}% of total
                                    </div>
                                ")),

                            Placeholder::make('recent_videos')
                                ->label('Recent Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-info-600'>
                                        {$stats['recent_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Last 7 days
                                    </div>
                                ")),
                        ]),
                ]),

            Section::make('Video Types')
                ->description('Breakdown by video capabilities')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            Placeholder::make('custom_anchor_videos')
                                ->label('Custom Anchor Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-warning-600'>
                                        {$stats['custom_anchor_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Can change product anchor
                                    </div>
                                ")),

                            Placeholder::make('regular_videos')
                                ->label('Regular Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-gray-600'>
                                        {$stats['regular_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Standard videos
                                    </div>
                                ")),
                        ]),
                ]),

            Section::make('Campaign Statistics')
                ->description('Campaign-related video statistics')
                ->schema([
                    Grid::make(3)
                        ->schema([
                            Placeholder::make('campaigns_with_videos')
                                ->label('Campaigns with Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-primary-600'>
                                        {$stats['campaigns_with_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Out of {$stats['total_campaigns']} total
                                    </div>
                                ")),

                            Placeholder::make('avg_videos_per_campaign')
                                ->label('Avg Videos per Campaign')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-info-600'>
                                        {$stats['avg_videos_per_campaign']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Average count
                                    </div>
                                ")),

                            Placeholder::make('last_sync')
                                ->label('Last Sync')
                                ->content(new HtmlString("
                                    <div class='text-lg font-bold text-gray-600'>
                                        {$stats['last_sync']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Most recent video
                                    </div>
                                ")),
                        ]),
                ]),

            Section::make('Sync Trends')
                ->description('Recent synchronization activity')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            Placeholder::make('today_videos')
                                ->label('Today\'s Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-success-600'>
                                        {$stats['today_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Synced today
                                    </div>
                                ")),

                            Placeholder::make('this_week_videos')
                                ->label('This Week\'s Videos')
                                ->content(new HtmlString("
                                    <div class='text-2xl font-bold text-info-600'>
                                        {$stats['this_week_videos']}
                                    </div>
                                    <div class='text-sm text-gray-500'>
                                        Synced this week
                                    </div>
                                ")),
                        ]),
                ]),
        ];
    }

    protected function calculateStats(): array
    {
        $totalVideos = Video::count();
        $activeVideos = Video::where('status', 'active')->count();
        $recentVideos = Video::where('created_at', '>=', now()->subDays(7))->count();
        $customAnchorVideos = Video::where('can_change_anchor', true)->count();
        $campaignsWithVideos = Campaign::has('videos')->count();
        $totalCampaigns = Campaign::count();
        $todayVideos = Video::where('created_at', '>=', now()->startOfDay())->count();
        $thisWeekVideos = Video::where('created_at', '>=', now()->startOfWeek())->count();
        
        $lastSync = Video::latest('created_at')->value('created_at');
        $lastSyncFormatted = $lastSync ? $lastSync->diffForHumans() : 'Never';
        
        $activePercentage = $totalVideos > 0 ? round(($activeVideos / $totalVideos) * 100, 1) : 0;
        $regularVideos = $totalVideos - $customAnchorVideos;
        $avgVideosPerCampaign = $campaignsWithVideos > 0 ? round($totalVideos / $campaignsWithVideos, 1) : 0;

        return [
            'total_videos' => number_format($totalVideos),
            'active_videos' => number_format($activeVideos),
            'active_percentage' => $activePercentage,
            'recent_videos' => number_format($recentVideos),
            'custom_anchor_videos' => number_format($customAnchorVideos),
            'regular_videos' => number_format($regularVideos),
            'campaigns_with_videos' => number_format($campaignsWithVideos),
            'total_campaigns' => number_format($totalCampaigns),
            'avg_videos_per_campaign' => $avgVideosPerCampaign,
            'last_sync' => $lastSyncFormatted,
            'today_videos' => number_format($todayVideos),
            'this_week_videos' => number_format($thisWeekVideos),
        ];
    }
}
