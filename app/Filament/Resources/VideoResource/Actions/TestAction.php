<?php

namespace App\Filament\Resources\VideoResource\Actions;

use App\Models\Video;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class TestAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'test_action';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Test Action')
            ->icon('heroicon-o-beaker')
            ->color('warning')
            ->tooltip('Test action to verify actions are working')
            ->action(function (Video $record) {
                $this->testAction($record);
            });
    }

    protected function testAction(Video $record): void
    {
        Notification::make()
            ->title('Test Action Works!')
            ->body("Action triggered for video: {$record->video_id}")
            ->success()
            ->send();
    }
}
