<?php

namespace App\Filament\Resources\VideoResource\Actions;

use App\Helpers\ErrorHandler;
use App\Models\Video;
use App\Services\TikTok\TikTokApiService;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class RefreshVideoDataAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'refresh_video_data';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Refresh Data')
            ->icon('heroicon-o-arrow-path')
            ->color('info')
            ->modalHeading('Refresh Video Data from TikTok API')
            ->modalDescription('Update this video\'s information with the latest data from TikTok API.')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->form($this->getFormSchema())
            ->visible(fn(Video $record) => $record->video_id && $record->campaign_id)
            ->action(function (Video $record, array $data) {
                $this->handleRefreshVideoData($record, $data);
            });
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Refresh Options')
                ->description('Configure what data to refresh from TikTok API')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            Toggle::make('update_basic_info')
                                ->label('Update Basic Info')
                                ->default(true)
                                ->helperText('Title, duration, status'),

                            Toggle::make('update_identity_info')
                                ->label('Update Identity Info')
                                ->default(true)
                                ->helperText('Creator information'),

                            Toggle::make('update_video_details')
                                ->label('Update Video Details')
                                ->default(true)
                                ->helperText('Resolution, format, file size'),

                            Toggle::make('update_product_links')
                                ->label('Update Product Links')
                                ->default(true)
                                ->helperText('SPU IDs and product associations'),
                        ]),

                    Textarea::make('notes')
                        ->label('Notes (Optional)')
                        ->placeholder('Add any notes about this refresh...')
                        ->rows(3),
                ]),
        ];
    }

    protected function handleRefreshVideoData(Video $record, array $data): void
    {
        try {
            // Validate campaign exists and has required data
            if (!$record->campaign) {
                Notification::make()
                    ->title('Campaign Not Found')
                    ->body('Cannot refresh video data: Campaign not found.')
                    ->danger()
                    ->send();
                return;
            }

            if (!$record->campaign->store_id || !$record->campaign->store_authorized_bc_id) {
                Notification::make()
                    ->title('Campaign Configuration Error')
                    ->body('Campaign is missing required store information.')
                    ->warning()
                    ->send();
                return;
            }

            // Initialize API service
            $apiService = new TikTokApiService();
            if (!$apiService->isConfigured()) {
                $error = $apiService->getLastError();
                Notification::make()
                    ->title('TikTok API Configuration Error')
                    ->body($error['message'] ?? 'TikTok API is not properly configured')
                    ->danger()
                    ->send();
                return;
            }

            // Get video data from API
            $apiOptions = [
                'store_id' => $record->campaign->store_id,
                'store_authorized_bc_id' => $record->campaign->store_authorized_bc_id,
                'keyword' => $record->video_id, // Search by video ID
                'page_size' => 1,
            ];

            $response = $apiService->getVideos($apiOptions);

            if (!ErrorHandler::isSuccess($response)) {
                Notification::make()
                    ->title('API Error')
                    ->body($response['message'] ?? 'Failed to retrieve video data from TikTok API')
                    ->danger()
                    ->send();
                return;
            }

            $responseData = $response['data']['data'] ?? [];
            $videos = $responseData['item_list'] ?? [];

            // Find the specific video
            $videoData = null;
            foreach ($videos as $video) {
                if ($video['item_id'] === $record->video_id) {
                    $videoData = $video;
                    break;
                }
            }

            if (!$videoData) {
                Notification::make()
                    ->title('Video Not Found')
                    ->body('Video not found in TikTok API response. It may have been deleted or is no longer available.')
                    ->warning()
                    ->send();
                return;
            }

            // Update video data based on selected options
            $updateData = [];
            $updatedFields = [];

            if ($data['update_basic_info'] ?? true) {
                $updateData['title'] = $videoData['text'] ?? $record->title;
                $updateData['status'] = 'active'; // Assume active if found in API

                $videoInfo = $videoData['video_info'] ?? [];
                if (!empty($videoInfo['duration'])) {
                    $updateData['duration'] = (int)$videoInfo['duration'];
                }

                $updatedFields[] = 'basic info';
            }

            if ($data['update_identity_info'] ?? true) {
                $identityInfo = $videoData['identity_info'] ?? [];
                if (!empty($identityInfo)) {
                    $updateData['identity_info'] = $identityInfo;
                    $updatedFields[] = 'identity info';
                }
            }

            if ($data['update_video_details'] ?? true) {
                $videoInfo = $videoData['video_info'] ?? [];
                if (!empty($videoInfo)) {
                    $updateData['video_info'] = $videoInfo;

                    // Update URLs if available
                    if (!empty($videoInfo['preview_url'])) {
                        $updateData['url'] = $videoInfo['preview_url'];
                    }
                    if (!empty($videoInfo['video_cover_url'])) {
                        $updateData['thumbnail'] = $videoInfo['video_cover_url'];
                    }

                    $updatedFields[] = 'video details';
                }
            }

            if ($data['update_product_links'] ?? true) {
                $spuIds = $videoData['spu_id_list'] ?? [];
                $updateData['spu_id_list'] = $spuIds;
                $updatedFields[] = 'product links';
            }

            // Update can_change_anchor if available
            if (isset($videoData['can_change_anchor'])) {
                $updateData['can_change_anchor'] = (bool)$videoData['can_change_anchor'];
            }

            // Perform the update
            $record->update($updateData);

            // Create success message
            $message = 'Video data refreshed successfully';
            if (!empty($updatedFields)) {
                $message .= ' (' . implode(', ', $updatedFields) . ')';
            }

            if (!empty($data['notes'])) {
                $message .= '. Notes: ' . $data['notes'];
            }

            Notification::make()
                ->title('Video Data Refreshed')
                ->body($message)
                ->success()
                ->duration(8000)
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Refresh Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
