<?php

namespace App\Filament\Resources\VideoResource\Pages;

use App\Filament\Resources\VideoResource;
use App\Jobs\SyncVideosJob;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListVideos extends ListRecords
{
    protected static string $resource = VideoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            Actions\Action::make('sync_all_active_campaigns')
                ->label('Sync All Active Campaigns')
                ->icon('heroicon-o-arrow-path')
                ->color('info')
                ->requiresConfirmation()
                ->modalHeading('Sync Videos for All Active Campaigns')
                ->modalDescription('This will sync videos for all active campaigns that have store information configured. This process may take several minutes.')
                ->modalSubmitActionLabel('Start Sync')
                ->action(function () {
                    SyncVideosJob::dispatch(
                        'all_active',
                        null,
                        [],
                        ['page_size' => 20, 'max_pages' => 5],
                        true
                    );

                    Notification::make()
                        ->title('Video Sync Queued')
                        ->body('Video sync for all active campaigns has been queued for background processing.')
                        ->success()
                        ->duration(8000)
                        ->send();
                }),

            Actions\Action::make('view_sync_stats')
                ->label('Sync Statistics')
                ->icon('heroicon-o-chart-bar')
                ->color('gray')
                ->modalHeading('Video Sync Statistics')
                ->modalDescription(function () {
                    $totalVideos = \App\Models\Video::count();
                    $activeVideos = \App\Models\Video::where('status', 'active')->count();
                    $recentVideos = \App\Models\Video::where('created_at', '>=', now()->subDays(7))->count();
                    $customAnchorVideos = \App\Models\Video::where('can_change_anchor', true)->count();
                    $campaignsWithVideos = \App\Models\Campaign::has('videos')->count();

                    $stats = [
                        "📹 Total Videos: {$totalVideos}",
                        "✅ Active Videos: {$activeVideos}",
                        "🆕 Recent Videos (7 days): {$recentVideos}",
                        "🔗 Custom Anchor Videos: {$customAnchorVideos}",
                        "📊 Campaigns with Videos: {$campaignsWithVideos}",
                    ];

                    return implode("\n", $stats);
                })
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close'),
        ];
    }
}
