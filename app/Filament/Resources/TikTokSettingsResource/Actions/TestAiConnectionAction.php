<?php

namespace App\Filament\Resources\TikTokSettingsResource\Actions;

use App\Services\AI\AIScoringService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class TestAiConnectionAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'test_ai_connection';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Test AI Connection')
            ->icon('heroicon-o-cpu-chip')
            ->color('purple')
            ->tooltip('Kiểm tra kết nối với AI service')
            ->action(function () {
                $this->testAiConnection();
            });
    }

    protected function testAiConnection(): void
    {
        try {
            $aiService = new AIScoringService();
            $result = $aiService->testConnection();

            if ($result['success']) {
                $provider = $result['provider'] ?? 'Unknown';
                $model = $result['model'] ?? 'Unknown';
                $responseTime = isset($result['response_time']) ? round($result['response_time'], 2) : null;
                $version = $result['version'] ?? '';

                $bodyMessage = "Connected to {$provider} using {$model}";
                if ($responseTime) {
                    $bodyMessage .= " (Response time: {$responseTime}ms)";
                }
                if ($version) {
                    $bodyMessage .= " - Version: {$version}";
                }

                Notification::make()
                    ->title('AI connection successful')
                    ->body($bodyMessage)
                    ->success()
                    ->duration(6000)
                    ->send();

            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $message = $result['message'] ?? 'AI connection failed';
                $suggestions = $result['suggestions'] ?? [];

                $title = match ($errorType) {
                    'authentication_error' => 'AI Authentication Failed',
                    'network_error' => 'Network Connection Error',
                    'configuration_error' => 'AI Configuration Error',
                    'service_unavailable' => 'AI Service Unavailable',
                    default => 'AI Connection Failed'
                };

                $bodyMessage = $message;
                if (!empty($suggestions)) {
                    $bodyMessage .= ". Suggestions: " . implode(', ', array_slice($suggestions, 0, 2));
                }

                Notification::make()
                    ->title($title)
                    ->body($bodyMessage)
                    ->danger()
                    ->duration(8000)
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('AI Connection Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
