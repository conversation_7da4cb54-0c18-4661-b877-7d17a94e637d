<?php

namespace App\Filament\Resources\TikTokSettingsResource\Actions;

use App\Models\TikTokSettings;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class TestSettingAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'test_setting';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Test')
            ->icon('heroicon-o-beaker')
            ->color('warning')
            ->tooltip('Test cài đặt này')
            ->visible(fn(TikTokSettings $record) => in_array($record->group, ['api']))
            ->action(function (TikTokSettings $record) {
                $this->testSetting($record);
            });
    }

    protected function testSetting(TikTokSettings $record): void
    {
        try {
            // Test the setting
            $result = $record->test();

            if ($result['success']) {
                $message = $result['message'] ?? 'Test completed successfully';
                $details = $result['details'] ?? '';

                $bodyMessage = $message;
                if ($details) {
                    $bodyMessage .= ". Details: {$details}";
                }

                Notification::make()
                    ->title('Test thành công')
                    ->body($bodyMessage)
                    ->success()
                    ->duration(6000)
                    ->send();

            } else {
                $message = $result['message'] ?? 'Test failed';
                $errorCode = $result['error_code'] ?? '';
                $suggestions = $result['suggestions'] ?? [];

                $bodyMessage = $message;
                if ($errorCode) {
                    $bodyMessage .= " (Error: {$errorCode})";
                }
                if (!empty($suggestions)) {
                    $bodyMessage .= ". Suggestions: " . implode(', ', array_slice($suggestions, 0, 2));
                }

                Notification::make()
                    ->title('Test thất bại')
                    ->body($bodyMessage)
                    ->danger()
                    ->duration(8000)
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Test Error')
                ->body('An unexpected error occurred during test: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
