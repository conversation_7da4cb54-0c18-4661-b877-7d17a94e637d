<?php

namespace App\Filament\Resources\TikTokSettingsResource\Actions;

use App\Services\TikTok\TikTokConfigService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class ClearCacheAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'clear_cache';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Xóa cache')
            ->icon('heroicon-o-trash')
            ->color('warning')
            ->requiresConfirmation()
            ->modalHeading('Xóa cache cấu hình')
            ->modalDescription('Bạn có chắc chắn muốn xóa tất cả cache cấu hình TikTok? Hành động này sẽ làm chậm các request tiếp theo cho đến khi cache được tái tạo.')
            ->modalSubmitActionLabel('Xóa cache')
            ->tooltip('Xóa tất cả cache cấu hình TikTok')
            ->action(function () {
                $this->clearCache();
            });
    }

    protected function clearCache(): void
    {
        try {
            $configService = new TikTokConfigService();
            
            // Clear cache and get statistics
            $result = $configService->clearCache();
            
            if (is_array($result) && isset($result['cleared_items'])) {
                $clearedCount = $result['cleared_items'];
                $cacheSize = isset($result['cache_size']) ? $result['cache_size'] : null;
                
                $bodyMessage = "Đã xóa {$clearedCount} items từ cache";
                if ($cacheSize) {
                    $bodyMessage .= " (Tiết kiệm: {$cacheSize})";
                }
                
                Notification::make()
                    ->title('Cache đã được xóa')
                    ->body($bodyMessage)
                    ->success()
                    ->send();
            } else {
                // Simple success case
                Notification::make()
                    ->title('Cache đã được xóa')
                    ->body('Tất cả cache cấu hình TikTok đã được xóa thành công')
                    ->success()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi xóa cache')
                ->body('Không thể xóa cache: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
