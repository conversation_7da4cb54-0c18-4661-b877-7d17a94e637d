<?php

namespace App\Filament\Resources\TikTokSettingsResource\Actions;

use App\Helpers\ErrorHandler;
use App\Services\TikTok\TikTokConfigService;
use Filament\Tables\Actions\Action;

class TestConnectionAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'test_connection';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Test kết nối API')
            ->icon('heroicon-o-signal')
            ->color('success')
            ->tooltip('Kiểm tra kết nối với TikTok API')
            ->action(function () {
                $this->testConnection();
            });
    }

    protected function testConnection(): void
    {
        try {
            $configService = new TikTokConfigService();
            $result = $configService->testConnection();

            // Use ErrorHandler to send notification with proper formatting
            ErrorHandler::sendNotification($result);

        } catch (\Exception $e) {
            ErrorHandler::sendNotification([
                'success' => false,
                'error' => 'Connection test failed: ' . $e->getMessage(),
                'error_type' => 'exception'
            ]);
        }
    }
}
