<?php

namespace App\Filament\Resources\TikTokSettingsResource\Pages;

use App\Filament\Resources\TikTokSettingsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTikTokSettings extends ListRecords
{
    protected static string $resource = TikTokSettingsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
