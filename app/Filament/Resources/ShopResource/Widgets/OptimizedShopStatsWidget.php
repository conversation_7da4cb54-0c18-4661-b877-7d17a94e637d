<?php

namespace App\Filament\Resources\ShopResource\Widgets;

use App\Models\Shop;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class OptimizedShopStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $totalShops = Shop::count();
        $activeShops = Shop::where('status', 'active')->count();
        $gmvMaxEligible = Shop::where('is_eligible_gmv_max', true)->count();
        $grantedAuth = Shop::where('exclusive_authorization_status', 'granted')->count();

        $activePercentage = $totalShops > 0 ? round(($activeShops / $totalShops) * 100) : 0;
        $gmvPercentage = $totalShops > 0 ? round(($gmvMaxEligible / $totalShops) * 100) : 0;
        $authPercentage = $totalShops > 0 ? round(($grantedAuth / $totalShops) * 100) : 0;

        return [
            Stat::make('Total Shops', $totalShops)
                ->description('Shops in system')
                ->descriptionIcon('heroicon-m-building-storefront')
                ->color('primary'),

            Stat::make('Active Shops', $activeShops)
                ->description("{$activePercentage}% active")
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('GMV Max Ready', $gmvMaxEligible)
                ->description("{$gmvPercentage}% eligible")
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),

            Stat::make('Authorized', $grantedAuth)
                ->description("{$authPercentage}% granted")
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('info'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }
}
