<?php

namespace App\Filament\Resources\ShopResource\Widgets;

use App\Models\Shop;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AuthorizationBreakdownWidget extends BaseWidget
{
    protected static ?int $sort = 2;

    protected function getStats(): array
    {
        $totalShops = Shop::count();
        $grantedAuth = Shop::where('exclusive_authorization_status', 'granted')->count();
        $pendingAuth = Shop::where('exclusive_authorization_status', 'pending')->count();
        $noneAuth = Shop::where('exclusive_authorization_status', 'none')->count();
        $revokedAuth = Shop::where('exclusive_authorization_status', 'revoked')->count();

        $grantedPercentage = $totalShops > 0 ? round(($grantedAuth / $totalShops) * 100) : 0;
        $pendingPercentage = $totalShops > 0 ? round(($pendingAuth / $totalShops) * 100) : 0;
        $nonePercentage = $totalShops > 0 ? round(($noneAuth / $totalShops) * 100) : 0;

        return [
            Stat::make('Granted', $grantedAuth)
                ->description("{$grantedPercentage}% authorized")
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('success'),

            Stat::make('Pending', $pendingAuth)
                ->description("{$pendingPercentage}% waiting")
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('No Authorization', $noneAuth)
                ->description("{$nonePercentage}% unset")
                ->descriptionIcon('heroicon-m-minus-circle')
                ->color('gray'),

            Stat::make('Issues', $revokedAuth)
                ->description('Revoked/Expired')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }
}
