<?php

namespace App\Filament\Resources\ShopResource\RelationManagers;

use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'products';

    protected static ?string $title = 'Sản phẩm';

    protected static ?string $modelLabel = 'sản phẩm';

    protected static ?string $pluralModelLabel = 'sản phẩm';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên sản phẩm')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('product_id')
                    ->label('Product ID')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('sku')
                    ->label('SKU')
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'out_of_stock' => 'Hết hàng',
                        'discontinued' => 'Ngừng kinh doanh',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('price')
                    ->label('Giá')
                    ->numeric()
                    ->step(0.01),
                Forms\Components\TextInput::make('sale_price')
                    ->label('Giá khuyến mãi')
                    ->numeric()
                    ->step(0.01),
                Forms\Components\TextInput::make('stock_quantity')
                    ->label('Số lượng tồn kho')
                    ->numeric(),
                Forms\Components\Textarea::make('description')
                    ->label('Mô tả')
                    ->maxLength(1000),
                Forms\Components\TextInput::make('image_url')
                    ->label('URL hình ảnh')
                    ->url()
                    ->maxLength(500),
                Forms\Components\Select::make('category')
                    ->label('Danh mục')
                    ->options([
                        'fashion' => 'Thời trang',
                        'beauty' => 'Làm đẹp',
                        'electronics' => 'Điện tử',
                        'home' => 'Gia dụng',
                        'sports' => 'Thể thao',
                        'books' => 'Sách',
                        'toys' => 'Đồ chơi',
                        'food' => 'Thực phẩm',
                        'other' => 'Khác',
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\ImageColumn::make('image_url')
                    ->label('Hình ảnh')
                    ->circular()
                    ->defaultImageUrl(url('/images/placeholder-product.png')),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên sản phẩm')
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('product_id')
                    ->label('Product ID')
                    ->searchable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'active',
                        'danger' => 'inactive',
                        'warning' => 'out_of_stock',
                        'secondary' => 'discontinued',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'out_of_stock' => 'Hết hàng',
                        'discontinued' => 'Ngừng kinh doanh',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('price')
                    ->label('Giá')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sale_price')
                    ->label('Giá KM')
                    ->money('VND')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('stock_quantity')
                    ->label('Tồn kho')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('category')
                    ->label('Danh mục')
                    ->colors([
                        'primary' => 'fashion',
                        'success' => 'beauty',
                        'warning' => 'electronics',
                        'info' => 'home',
                        'secondary' => 'other',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'fashion' => 'Thời trang',
                        'beauty' => 'Làm đẹp',
                        'electronics' => 'Điện tử',
                        'home' => 'Gia dụng',
                        'sports' => 'Thể thao',
                        'books' => 'Sách',
                        'toys' => 'Đồ chơi',
                        'food' => 'Thực phẩm',
                        'other' => 'Khác',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'out_of_stock' => 'Hết hàng',
                        'discontinued' => 'Ngừng kinh doanh',
                    ]),
                Tables\Filters\SelectFilter::make('category')
                    ->label('Danh mục')
                    ->options([
                        'fashion' => 'Thời trang',
                        'beauty' => 'Làm đẹp',
                        'electronics' => 'Điện tử',
                        'home' => 'Gia dụng',
                        'sports' => 'Thể thao',
                        'books' => 'Sách',
                        'toys' => 'Đồ chơi',
                        'food' => 'Thực phẩm',
                        'other' => 'Khác',
                    ]),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]));
    }
}
