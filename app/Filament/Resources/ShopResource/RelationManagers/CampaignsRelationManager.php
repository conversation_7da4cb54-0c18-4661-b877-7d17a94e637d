<?php

namespace App\Filament\Resources\ShopResource\RelationManagers;

use App\Models\Campaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CampaignsRelationManager extends RelationManager
{
    protected static string $relationship = 'campaigns';

    protected static ?string $title = 'Chiến dịch';

    protected static ?string $modelLabel = 'chiến dịch';

    protected static ?string $pluralModelLabel = 'chiến dịch';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên chiến dịch')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('campaign_id')
                    ->label('Campaign ID')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'paused' => 'Tạm dừng',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('target_roi')
                    ->label('Target ROI')
                    ->numeric()
                    ->step(0.01),
                Forms\Components\TextInput::make('budget')
                    ->label('Ngân sách')
                    ->numeric()
                    ->step(0.01),
                Forms\Components\TextInput::make('daily_budget')
                    ->label('Ngân sách hàng ngày')
                    ->numeric()
                    ->step(0.01),
                Forms\Components\DateTimePicker::make('start_date')
                    ->label('Ngày bắt đầu'),
                Forms\Components\DateTimePicker::make('end_date')
                    ->label('Ngày kết thúc'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên chiến dịch')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('campaign_id')
                    ->label('Campaign ID')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'paused',
                        'info' => 'completed',
                        'danger' => 'cancelled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => 'Hoạt động',
                        'paused' => 'Tạm dừng',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('target_roi')
                    ->label('Target ROI')
                    ->numeric(decimalPlaces: 2)
                    ->sortable(),
                Tables\Columns\TextColumn::make('budget')
                    ->label('Ngân sách')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\TextColumn::make('daily_budget')
                    ->label('Ngân sách/ngày')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('Ngày bắt đầu')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('Ngày kết thúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'paused' => 'Tạm dừng',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ]),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]));
    }
}
