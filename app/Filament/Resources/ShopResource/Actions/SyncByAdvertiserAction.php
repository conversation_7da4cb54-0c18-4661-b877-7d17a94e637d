<?php

namespace App\Filament\Resources\ShopResource\Actions;

use App\Jobs\SyncShopsJob;
use App\Models\AdvertiserAccount;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncByAdvertiserAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_by_advertiser';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Đồng bộ theo Advertiser')
            ->icon('heroicon-o-building-office')
            ->color('info')
            ->modalHeading('Đồng bộ cửa hàng theo Advertiser')
            ->modalSubmitActionLabel('Đồng bộ')
            ->form([
                Forms\Components\Select::make('advertiser_id')
                    ->label('Chọn Advertiser Account')
                    ->options(function () {
                        return AdvertiserAccount::pluck('advertiser_name', 'advertiser_id');
                    })
                    ->searchable()
                    ->required()
                    ->helperText('Chọn advertiser account để đồng bộ cửa hàng'),
            ])
            ->action(function (array $data) {
                $this->syncByAdvertiser($data);
            });
    }

    protected function syncByAdvertiser(array $data): void
    {
        try {
            $advertiserId = $data['advertiser_id'];
            
            // Validate advertiser exists
            $advertiser = AdvertiserAccount::where('advertiser_id', $advertiserId)->first();
            if (!$advertiser) {
                throw new \Exception('Advertiser account không tồn tại');
            }

            SyncShopsJob::dispatch($advertiserId);

            $advertiserName = $advertiser->advertiser_name ?? $advertiserId;

            Notification::make()
                ->title('Đồng bộ đã được lên lịch')
                ->body("Quá trình đồng bộ cửa hàng cho advertiser '{$advertiserName}' đang chạy trong background")
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi lên lịch đồng bộ')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
