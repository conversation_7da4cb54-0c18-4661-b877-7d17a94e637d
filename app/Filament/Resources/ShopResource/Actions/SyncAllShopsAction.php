<?php

namespace App\Filament\Resources\ShopResource\Actions;

use App\Jobs\SyncShopsJob;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncAllShopsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_all_shops';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Đồng bộ tất cả từ TikTok')
            ->icon('heroicon-o-arrow-path')
            ->color('success')
            ->requiresConfirmation()
            ->modalHeading('Đồng bộ tất cả cửa hàng từ TikTok')
            ->modalDescription('Bạn có chắc chắn muốn đồng bộ tất cả cửa hàng từ TikTok API? Quá trình này sẽ kiểm tra tất cả advertiser accounts.')
            ->modalSubmitActionLabel('Đồng bộ')
            ->action(function () {
                $this->syncAllShops();
            });
    }

    protected function syncAllShops(): void
    {
        try {
            SyncShopsJob::dispatch();

            Notification::make()
                ->title('Đồng bộ đã được lên lịch')
                ->body('Quá trình đồng bộ tất cả cửa hàng từ TikTok API đang chạy trong background')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi lên lịch đồng bộ')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
