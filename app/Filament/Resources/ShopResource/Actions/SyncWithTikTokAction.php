<?php

namespace App\Filament\Resources\ShopResource\Actions;

use App\Jobs\SyncShopsJob;
use App\Models\Shop;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncWithTikTokAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_with_tiktok';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Đồng bộ')
            ->icon('heroicon-o-arrow-path')
            ->color('info')
            ->tooltip('Đồng bộ shop này từ TikTok API')
            ->requiresConfirmation()
            ->modalHeading('Đồng bộ cửa hàng từ TikTok')
            ->modalDescription('Bạn có chắc chắn muốn đồng bộ cửa hàng này từ TikTok API? Quá trình này sẽ cập nhật thông tin shop và tải xuống hình ảnh mới.')
            ->modalSubmitActionLabel('Đồng bộ ngay')
            ->action(function (Shop $record) {
                $this->syncWithTikTok($record);
            });
    }

    protected function syncWithTikTok(Shop $record): void
    {
        try {
            // Validate advertiser_id exists
            if (!$record->advertiser_id) {
                Notification::make()
                    ->title('Lỗi')
                    ->body('Shop không có Advertiser ID để đồng bộ')
                    ->danger()
                    ->send();
                return;
            }

            // Dispatch sync job for this advertiser
            SyncShopsJob::dispatch($record->advertiser_id);

            // Get advertiser name for better UX
            $advertiserName = $record->advertiserAccount?->advertiser_name ?? $record->advertiser_id;

            Notification::make()
                ->title('Đồng bộ đã được lên lịch')
                ->body("Quá trình đồng bộ đang chạy trong background cho advertiser: {$advertiserName}")
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi đồng bộ')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
