<?php

namespace App\Filament\Resources\ShopResource\Actions;

use App\Jobs\SyncShopsJob;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;

class SyncAllBulkAction extends BulkAction
{
    public static function getDefaultName(): ?string
    {
        return 'sync_all_with_tiktok';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Đồng bộ tất cả từ TikTok')
            ->icon('heroicon-o-arrow-path')
            ->color('info')
            ->requiresConfirmation()
            ->modalHeading('Đồng bộ tất cả cửa hàng với TikTok')
            ->modalDescription('Bạn có chắc chắn muốn đồng bộ tất cả cửa hàng với TikTok API? Quá trình này sẽ kiểm tra tất cả advertiser accounts.')
            ->modalSubmitActionLabel('Đồng bộ')
            ->action(function (Collection $records) {
                $this->syncAllShops($records);
            });
    }

    protected function syncAllShops(Collection $records): void
    {
        try {
            // Dispatch sync job for all shops
            SyncShopsJob::dispatch();

            $count = $records->count();

            Notification::make()
                ->title('Đồng bộ đã được lên lịch')
                ->body("Quá trình đồng bộ {$count} cửa hàng từ TikTok API đang chạy trong background")
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi lên lịch đồng bộ')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
