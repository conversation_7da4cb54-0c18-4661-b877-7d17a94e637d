<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Jobs\SyncCampaignDetailsJob;
use App\Services\JobNotificationService;
use Exception;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class BulkSyncCampaignDetailsAction
{
    public static function make(): BulkAction
    {
        return BulkAction::make('bulk_sync_campaign_details')
            ->label('Sync Campaign Details')
            ->icon('heroicon-o-document-magnifying-glass')
            ->color('info')
            ->requiresConfirmation()
            ->modalHeading('Bulk Sync Campaign Details')
            ->modalDescription('Đồng bộ chi tiết GMV Max Campaign cho tất cả campaigns đã chọn từ TikTok API endpoint /campaign/gmv_max/info/. Quá trình này sẽ chạy trong background queue.')
            ->form([
                Toggle::make('force_sync')
                    ->label('Force Sync')
                    ->helperText('Đồng bộ lại ngay cả khi đã có dữ liệu chi tiết')
                    ->default(false),

                Toggle::make('notify_users')
                    ->label('Send Notifications')
                    ->helperText('Gửi thông báo cho admin users khi hoàn thành')
                    ->default(true),

                TextInput::make('delay_seconds')
                    ->label('Delay Between Jobs (seconds)')
                    ->helperText('Thời gian delay giữa các jobs để tránh rate limit')
                    ->numeric()
                    ->default(2)
                    ->minValue(0)
                    ->maxValue(60),
            ])
            ->modalSubmitActionLabel('Start Bulk Sync')
            ->action(function (Collection $records, array $data) {
                self::bulkSyncCampaignDetails($records, $data);
            });
    }

    protected static function bulkSyncCampaignDetails(Collection $records, array $data): void
    {
        try {
            $totalCampaigns = $records->count();
            $dispatchedCount = 0;
            $skippedCount = 0;
            $delaySeconds = $data['delay_seconds'] ?? 2;
            $forceSync = $data['force_sync'] ?? false;
            $notifyUsers = $data['notify_users'] ?? true;

            foreach ($records as $index => $campaign) {
                // Check if already has details and not forcing
                if (!$forceSync && $campaign->details_synced_at &&
                    $campaign->details_synced_at->gt(now()->subHours(1))) {

                    $skippedCount++;
                    Log::info('Skipping campaign details sync - already synced recently', [
                        'campaign_id' => $campaign->campaign_id,
                        'campaign_name' => $campaign->name,
                        'last_synced' => $campaign->details_synced_at
                    ]);
                    continue;
                }

                // Dispatch job with delay to avoid rate limiting
                SyncCampaignDetailsJob::dispatch(
                    $campaign->campaign_id,
                    $campaign->advertiser_id,
                    false // Don't notify for individual jobs in bulk operation
                )->delay(now()->addSeconds($index * $delaySeconds));

                $dispatchedCount++;
            }

            // Log bulk operation
            Log::info('Bulk campaign details sync jobs dispatched', [
                'total_campaigns' => $totalCampaigns,
                'dispatched' => $dispatchedCount,
                'skipped' => $skippedCount,
                'delay_seconds' => $delaySeconds,
                'force_sync' => $forceSync
            ]);

            // Send summary notification to admin users
            if ($notifyUsers && $dispatchedCount > 0) {
                $adminMessage = "Đã tạo {$dispatchedCount} jobs đồng bộ chi tiết campaigns";
                if ($skippedCount > 0) {
                    $adminMessage .= "\n⏭️ Bỏ qua {$skippedCount} campaigns đã được đồng bộ gần đây";
                }
                $adminMessage .= "\n⏱️ Delay giữa các jobs: {$delaySeconds}s";
                $adminMessage .= "\n🔄 Chạy 'php artisan queue:work' để xử lý";

                JobNotificationService::sendSuccessNotification(
                    'Bulk Sync Campaign Details',
                    $adminMessage,
                    [
                        'total_campaigns' => $totalCampaigns,
                        'dispatched' => $dispatchedCount,
                        'skipped' => $skippedCount,
                        'delay_seconds' => $delaySeconds
                    ]
                );
            }

            // Show immediate feedback
            $localMessage = "✅ Đã tạo {$dispatchedCount} jobs đồng bộ chi tiết campaigns";
            if ($skippedCount > 0) {
                $localMessage .= "\n⏭️ Bỏ qua {$skippedCount} campaigns đã được đồng bộ gần đây";
            }
            $localMessage .= "\n🔄 Chạy 'php artisan queue:work' để xử lý";

            Notification::make()
                ->title('Bulk Campaign Details Sync Started')
                ->body($localMessage)
                ->success()
                ->duration(10000)
                ->send();

        } catch (Exception $e) {
            Log::error('Bulk campaign details sync failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Send error notification to admin users
            if ($data['notify_users'] ?? true) {
                JobNotificationService::sendErrorNotification(
                    'Bulk Sync Campaign Details',
                    "Lỗi khi tạo bulk sync jobs: " . $e->getMessage()
                );
            }

            Notification::make()
                ->title('❌ Bulk Campaign Details Sync Failed')
                ->body('Lỗi: ' . $e->getMessage())
                ->danger()
                ->duration(10000)
                ->send();
        }
    }
}
