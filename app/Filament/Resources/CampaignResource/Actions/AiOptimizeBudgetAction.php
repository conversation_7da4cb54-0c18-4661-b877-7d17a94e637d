<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\Campaign;
use App\Services\AI\AIScoringService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class AiOptimizeBudgetAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'ai_optimize_budget';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('AI Budget Optimization')
            ->icon('heroicon-o-currency-dollar')
            ->color('success')
            ->tooltip('Tối ưu hóa ngân sách chiến dịch bằng AI')
            ->action(function (Campaign $record) {
                $this->optimizeBudget($record);
            });
    }

    protected function optimizeBudget(Campaign $record): void
    {
        try {
            $aiService = new AIScoringService();
            $result = $aiService->optimizeBudget($record);

            if ($result['success']) {
                $current = isset($result['current_budget']) ? number_format($result['current_budget']) : 'N/A';
                $recommended = isset($result['recommended_budget']) ? number_format($result['recommended_budget']) : 'N/A';
                $improvement = isset($result['expected_improvement']) ? round($result['expected_improvement'] * 100) : 0;
                $reasoning = $result['reasoning'] ?? '';

                $bodyMessage = "Current: ${current} → Recommended: ${recommended} (Expected +{$improvement}% improvement)";
                if ($reasoning) {
                    $bodyMessage .= ". Reasoning: {$reasoning}";
                }

                Notification::make()
                    ->title('Budget Optimization Complete')
                    ->body($bodyMessage)
                    ->success()
                    ->duration(10000) // Show longer for important financial info
                    ->send();

            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $errorMessage = $result['error'] ?? 'Budget optimization failed. Please try again later.';

                $title = match ($errorType) {
                    'insufficient_data' => 'Insufficient Data for Optimization',
                    'configuration_error' => 'Configuration Error',
                    'network_error' => 'Network Connection Error',
                    'api_error' => 'AI Service Error',
                    default => 'Budget Optimization Unavailable'
                };

                $notificationColor = match ($errorType) {
                    'configuration_error' => 'warning',
                    'network_error' => 'info',
                    'insufficient_data' => 'warning',
                    default => 'danger'
                };

                Notification::make()
                    ->title($title)
                    ->body($errorMessage)
                    ->color($notificationColor)
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Budget Optimization Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
