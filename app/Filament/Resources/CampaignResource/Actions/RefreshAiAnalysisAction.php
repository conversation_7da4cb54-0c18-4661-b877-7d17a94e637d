<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\AiAnalysis;
use App\Services\AI\AIScoringService;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class RefreshAiAnalysisAction
{
    public static function make(): Action
    {
        return Action::make('refresh_ai_analysis')
            ->label('Refresh AI Analysis')
            ->icon('heroicon-o-arrow-path')
            ->color('info')
            ->requiresConfirmation()
            ->modalHeading('Refresh AI Analysis')
            ->modalDescription('This will generate new AI analysis for this campaign. This may take a few moments.')
            ->modalSubmitActionLabel('Refresh Analysis')
            ->action(function ($record) {
                try {
                    $aiService = new AIScoringService();
                    $analysisTypes = AiAnalysis::getAnalysisTypes();
                    $successCount = 0;
                    $errors = [];

                    foreach ($analysisTypes as $type) {
                        try {
                            // Perform AI analysis based on type
                            $result = match ($type) {
                                AiAnalysis::TYPE_RECOMMENDATIONS => $aiService->generateCampaignRecommendations($record),
                                AiAnalysis::TYPE_ROI_PREDICTION => $aiService->analyzeCampaign($record),
                                AiAnalysis::TYPE_BUDGET_OPTIMIZATION => $aiService->optimizeBudget($record),
                                default => ['success' => false, 'error' => 'Unknown analysis type']
                            };

                            if ($result['success']) {
                                // Store analysis result
                                $confidence = $result['confidence'] ?? null;
                                if ($confidence !== null && !is_float($confidence)) {
                                    $confidence = is_numeric($confidence) ? (float) $confidence : null;
                                }
                                
                                AiAnalysis::createOrUpdateAnalysis(
                                    $record->id,
                                    $type,
                                    $result,
                                    $confidence
                                );

                                $successCount++;
                            } else {
                                $errors[] = ucfirst(str_replace('_', ' ', $type)) . ': ' . ($result['error'] ?? 'Unknown error');
                            }

                        } catch (\Exception $e) {
                            $errors[] = ucfirst(str_replace('_', ' ', $type)) . ': ' . $e->getMessage();
                            
                            Log::error('Manual AI analysis failed', [
                                'campaign_id' => $record->id,
                                'analysis_type' => $type,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }

                    // Show results
                    if ($successCount > 0) {
                        $message = "✅ Successfully refreshed {$successCount} AI analysis types for campaign: {$record->name}";
                        
                        if (!empty($errors)) {
                            $message .= "\n\n⚠️ Some analyses failed:\n" . implode("\n", $errors);
                        }

                        Notification::make()
                            ->title('AI Analysis Refreshed')
                            ->body($message)
                            ->success()
                            ->duration(8000)
                            ->send();
                    } else {
                        Notification::make()
                            ->title('AI Analysis Failed')
                            ->body("❌ All AI analyses failed for campaign: {$record->name}\n\nErrors:\n" . implode("\n", $errors))
                            ->danger()
                            ->duration(10000)
                            ->send();
                    }

                } catch (\Exception $e) {
                    Notification::make()
                        ->title('AI Analysis Error')
                        ->body("💥 An unexpected error occurred: " . $e->getMessage())
                        ->danger()
                        ->duration(8000)
                        ->send();

                    Log::error('Manual AI analysis exception', [
                        'campaign_id' => $record->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            });
    }
}
