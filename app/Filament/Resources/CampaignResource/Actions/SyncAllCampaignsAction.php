<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\AdvertiserAccount;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use App\Services\JobNotificationService;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class SyncAllCampaignsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_all_campaigns';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync All Campaigns')
            ->icon('heroicon-o-cloud-arrow-down')
            ->color('success')
            ->modalHeading('Campaign Sync Progress')
            ->modalWidth(MaxWidth::FourExtraLarge)
            ->modalContent(fn() => view('filament.campaign-sync-progress'))
            ->modalSubmitActionLabel('Start Sync')
            ->modalCancelActionLabel('Close')
            ->action(function () {
                $this->syncAllCampaigns();
            });
    }

    protected function syncAllCampaigns(): void
    {
        try {
            // Get eligible advertiser accounts
            $advertiserAccounts = AdvertiserAccount::active()
                ->where(function ($query) {
                    $query->whereHas('shops')
                        ->orWhereHas('shopsByAdvertiserId');
                })
                ->with(['shops', 'shopsByAdvertiserId'])
                ->get();

            if ($advertiserAccounts->isEmpty()) {
                Notification::make()
                    ->title('No Eligible Advertiser Accounts')
                    ->body('No active advertiser accounts with associated shops were found.')
                    ->warning()
                    ->send();
                return;
            }

            $apiService = new TikTokApiService();
            $syncService = new TikTokSyncService($apiService);

            // Sync campaigns for all advertiser accounts
            $result = $syncService->syncCampaignsForAllAdvertisers($advertiserAccounts);

            if ($result['success']) {
                $syncedCount = $result['synced'] ?? 0;
                $createdCount = $result['created'] ?? 0;
                $updatedCount = $result['updated'] ?? 0;
                $totalAdvertisers = $result['total_advertisers'] ?? 0;
                $totalShops = $result['total_shops'] ?? 0;
                $executionTime = $result['execution_time'] ?? 0;

                // Send success notification to admin users
                $message = "Đồng bộ campaigns thành công!\n\n" .
                          "📊 Tổng campaigns: {$syncedCount}\n" .
                          "🆕 Tạo mới: {$createdCount}\n" .
                          "🔄 Cập nhật: {$updatedCount}\n" .
                          "👥 Advertiser accounts: {$totalAdvertisers}\n" .
                          "🏪 Shops: {$totalShops}\n" .
                          "⏱️ Thời gian: {$executionTime}s";

                JobNotificationService::sendSuccessNotification(
                    'Đồng bộ Campaigns',
                    $message,
                    [
                        'synced' => $syncedCount,
                        'created' => $createdCount,
                        'updated' => $updatedCount,
                        'advertisers' => $totalAdvertisers,
                        'shops' => $totalShops,
                        'execution_time' => $executionTime
                    ]
                );

                // Also show local notification for immediate feedback
                Notification::make()
                    ->title('🎉 Đồng bộ Campaigns thành công')
                    ->body("Đã đồng bộ {$syncedCount} campaigns ({$createdCount} mới, {$updatedCount} cập nhật)")
                    ->success()
                    ->duration(6000)
                    ->send();
            } else {
                $errorMessage = $result['error'] ?? 'Unknown sync error';

                // Send error notification to admin users
                JobNotificationService::sendErrorNotification(
                    'Đồng bộ Campaigns',
                    $errorMessage
                );

                // Also show local notification for immediate feedback
                Notification::make()
                    ->title('❌ Đồng bộ Campaigns thất bại')
                    ->body("Lỗi: {$errorMessage}")
                    ->danger()
                    ->duration(8000)
                    ->send();
            }

        } catch (\Exception $e) {
            // Send error notification to admin users
            JobNotificationService::sendErrorNotification(
                'Đồng bộ Campaigns',
                "Lỗi không mong muốn: " . $e->getMessage()
            );

            // Also show local notification for immediate feedback
            Notification::make()
                ->title('💥 Lỗi đồng bộ Campaigns')
                ->body("Lỗi không mong muốn: " . $e->getMessage())
                ->danger()
                ->duration(10000)
                ->send();
        }
    }
}

