<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\AiAnalysis;
use App\Models\Campaign;
use Artisan;
use Exception;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class ViewAiAnalysisSummaryAction
{
    public static function make(): Action
    {
        return Action::make('view_ai_analysis_summary')
            ->label('AI Summary')
            ->icon('heroicon-o-chart-bar')
            ->color('info')
            ->modalHeading('AI Analysis Summary')
            ->modalWidth(MaxWidth::SevenExtraLarge)
            ->modalContent(function (Campaign $record) {
                return view('filament.modals.ai-analysis-summary', [
                    'campaign' => $record,
                    'recommendationsAnalysis' => $record->getCurrentAiAnalysis(AiAnalysis::TYPE_RECOMMENDATIONS),
                    'roiAnalysis' => $record->getCurrentAiAnalysis(AiAnalysis::TYPE_ROI_PREDICTION),
                    'budgetAnalysis' => $record->getCurrentAiAnalysis(AiAnalysis::TYPE_BUDGET_OPTIMIZATION),
                ]);
            })
            ->modalFooterActions([
                Action::make('refresh_analysis')
                    ->label('Refresh Analysis')
                    ->icon('heroicon-o-arrow-path')
                    ->color('primary')
                    ->action(function (Campaign $record) {
                        try {
                            Artisan::call('ai:analyze-campaigns', [
                                '--campaign' => $record->id,
                                '--force' => true,
                                '--type' => 'all'
                            ]);

                            Notification::make()
                                ->title('AI Analysis Started')
                                ->body('🤖 New AI analysis has been started for this campaign.')
                                ->success()
                                ->duration(5000)
                                ->send();
                        } catch (Exception $e) {
                            Notification::make()
                                ->title('Analysis Failed')
                                ->body('❌ Failed to start AI analysis: ' . $e->getMessage())
                                ->danger()
                                ->duration(8000)
                                ->send();
                        }
                    }),

                Action::make('close')
                    ->label('Close')
                    ->color('gray')
                    ->modalCancelAction(),
            ])
            ->visible(function () {
                return true;
            })
            ->tooltip('View AI analysis summary and insights');
    }
}
