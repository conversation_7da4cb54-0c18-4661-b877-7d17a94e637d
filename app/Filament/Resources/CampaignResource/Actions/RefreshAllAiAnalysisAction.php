<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class RefreshAllAiAnalysisAction
{
    public static function make(): Action
    {
        return Action::make('refresh_all_ai_analysis')
            ->label('Refresh All AI Analysis')
            ->icon('heroicon-o-cpu-chip')
            ->color('info')
            ->requiresConfirmation()
            ->modalHeading('Refresh AI Analysis for All Active Campaigns')
            ->modalDescription('This will generate new AI analysis for all active campaigns. This process may take 10-15 minutes.')
            ->modalSubmitActionLabel('Start Analysis')
            ->action(function () {
                try {
                    Artisan::call('ai:analyze-campaigns', [
                        '--type' => 'all',
                        '--force' => true
                    ]);
                    
                    Notification::make()
                        ->title('AI Analysis Started')
                        ->body('🤖 AI analysis has been started for all active campaigns. Check back in 10-15 minutes for results.')
                        ->success()
                        ->duration(8000)
                        ->send();
                } catch (\Exception $e) {
                    Log::error('Refresh all AI analysis failed', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    Notification::make()
                        ->title('AI Analysis Failed')
                        ->body('❌ Failed to start AI analysis: ' . $e->getMessage())
                        ->danger()
                        ->duration(8000)
                        ->send();
                }
            });
    }
}
