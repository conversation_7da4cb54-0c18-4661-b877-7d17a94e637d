<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\Campaign;
use App\Services\AI\AIScoringService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class AiAnalyzeAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'ai_analyze';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('AI Analysis')
            ->icon('heroicon-o-cpu-chip')
            ->color('purple')
            ->tooltip('Phân tích chiến dịch bằng AI')
            ->action(function (Campaign $record) {
                $this->analyzeWithAI($record);
            });
    }

    protected function analyzeWithAI(Campaign $record): void
    {
        try {
            $aiService = new AIScoringService();
            $result = $aiService->analyzeCampaign($record);

            if ($result['success']) {
                $score = $result['ai_score'] ?? 'N/A';
                $confidence = isset($result['confidence']) ? round($result['confidence'] * 100) : 'N/A';
                $recommendations = !empty($result['recommendations'])
                    ? implode(', ', array_slice($result['recommendations'], 0, 2))
                    : 'No specific recommendations available';

                Notification::make()
                    ->title("AI Score: {$score}/100 ({$confidence}% confidence)")
                    ->body("Recommendations: {$recommendations}")
                    ->success()
                    ->send();
            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $errorMessage = $result['error'] ?? 'Unknown error occurred';

                $title = match ($errorType) {
                    'insufficient_data' => 'Insufficient Data for Analysis',
                    'api_error' => 'AI Service Error',
                    'configuration_error' => 'Configuration Error',
                    default => 'Analysis Failed'
                };

                Notification::make()
                    ->title($title)
                    ->body($errorMessage)
                    ->warning()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('AI Analysis Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
