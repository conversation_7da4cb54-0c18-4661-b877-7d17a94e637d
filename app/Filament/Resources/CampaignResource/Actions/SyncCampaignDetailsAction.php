<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\Campaign;
use App\Jobs\SyncCampaignDetailsJob;
use App\Services\TikTok\TikTokApiService;
use App\Services\JobNotificationService;
use App\Helpers\ErrorHandler;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;

class SyncCampaignDetailsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_campaign_details';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Details')
            ->icon('heroicon-o-document-magnifying-glass')
            ->color('info')
            ->tooltip('Đồng bộ chi tiết GMV Max Campaign từ TikTok API')
            ->modalHeading('Sync Campaign Details')
            ->modalDescription(fn (Campaign $record) =>
                "Đồng bộ chi tiết đầy đủ cho campaign: {$record->name}\n\n" .
                "Thông tin sẽ được lấy từ TikTok Business API endpoint /campaign/gmv_max/info/ bao gồm:\n" .
                "• Cấu hình campaign (shopping_ads_type, optimization_goal)\n" .
                "• Thông tin bidding (deep_bid_type, roas_bid, budget)\n" .
                "• Lịch trình (schedule_type, start/end time)\n" .
                "• Targeting (placements, locations, age_groups)\n" .
                "• Sản phẩm và nội dung (item_group_ids, identity_list)\n" .
                "• Video và affiliate settings"
            )
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->form([
                \Filament\Forms\Components\Select::make('sync_mode')
                    ->label('Sync Mode')
                    ->options([
                        'direct' => 'Đồng bộ trực tiếp (Immediate)',
                        'queue' => 'Đồng bộ qua queue (Background)'
                    ])
                    ->default('queue')
                    ->required()
                    ->helperText('Queue mode được khuyến nghị để tránh timeout'),

                \Filament\Forms\Components\Toggle::make('force_sync')
                    ->label('Force Sync')
                    ->helperText('Đồng bộ lại ngay cả khi đã có dữ liệu chi tiết')
                    ->default(false),

                \Filament\Forms\Components\Toggle::make('notify_users')
                    ->label('Send Notifications')
                    ->helperText('Gửi thông báo cho admin users khi hoàn thành')
                    ->default(true),
            ])
            ->modalSubmitActionLabel('Start Sync')
            ->modalCancelActionLabel('Cancel')
            ->action(function (Campaign $record, array $data) {
                $this->syncCampaignDetails($record, $data);
            });
    }

    protected function syncCampaignDetails(Campaign $record, array $data): void
    {
        try {
            // Check if already has details and not forcing
            if (!$data['force_sync'] && $record->details_synced_at &&
                $record->details_synced_at->gt(now()->subHours(1))) {

                Notification::make()
                    ->title('⚠️ Chi tiết đã được đồng bộ gần đây')
                    ->body("Campaign '{$record->name}' đã được đồng bộ chi tiết lúc {$record->details_synced_at->format('H:i d/m/Y')}. Sử dụng 'Force Sync' để đồng bộ lại.")
                    ->warning()
                    ->duration(8000)
                    ->send();
                return;
            }

            if ($data['sync_mode'] === 'queue') {
                $this->syncViaQueue($record, $data);
            } else {
                $this->syncDirectly($record, $data);
            }

        } catch (\Exception $e) {
            // Send error notification to admin users
            if ($data['notify_users']) {
                JobNotificationService::sendErrorNotification(
                    'Đồng bộ Chi tiết Campaign',
                    "Campaign: {$record->name}\nLỗi: " . $e->getMessage()
                );
            }

            // Also show local notification for immediate feedback
            Notification::make()
                ->title('💥 Lỗi đồng bộ chi tiết Campaign')
                ->body('Lỗi: ' . $e->getMessage())
                ->danger()
                ->duration(10000)
                ->send();
        }
    }

    protected function syncViaQueue(Campaign $record, array $data): void
    {
        // Dispatch job to queue
        SyncCampaignDetailsJob::dispatch(
            $record->campaign_id,
            $record->advertiser_id,
            $data['notify_users']
        );

        Notification::make()
            ->title('🚀 Job đã được thêm vào queue')
            ->body("Đồng bộ chi tiết cho campaign '{$record->name}' đã được thêm vào queue.")
            ->success()
            ->duration(8000)
            ->send();
    }

    protected function syncDirectly(Campaign $record, array $data): void
    {
        // Initialize API service
        $apiService = new TikTokApiService();
        $apiService->setAdvertiserId($record->advertiser_id);

        // Get campaign details from API
        $response = $apiService->getCampaignDetails($record->campaign_id);

        if (!ErrorHandler::isSuccessResponse($response)) {
            $errorMessage = ErrorHandler::getErrorMessage($response);
            throw new \Exception("Failed to get campaign details: {$errorMessage}");
        }

        // Update campaign with detailed information
        $this->updateCampaignDetails($record, $response['data']);

        // Send success notification
        if ($data['notify_users']) {
            JobNotificationService::sendSuccessNotification(
                'Đồng bộ Chi tiết Campaign',
                "Chi tiết campaign '{$record->name}' đã được cập nhật thành công với " . count($response['data']) . " fields từ TikTok API"
            );
        }

        Notification::make()
            ->title('✅ Đồng bộ chi tiết thành công')
            ->body("Chi tiết campaign '{$record->name}' đã được cập nhật từ TikTok API")
            ->success()
            ->duration(6000)
            ->send();
    }

    protected function updateCampaignDetails(Campaign $campaign, array $data): void
    {
        $updateData = [
            'store_id' => $data['store_id'] ?? null,
            'store_authorized_bc_id' => $data['store_authorized_bc_id'] ?? null,
            'shopping_ads_type' => $data['shopping_ads_type'] ?? null,
            'product_specific_type' => $data['product_specific_type'] ?? null,
            'optimization_goal' => $data['optimization_goal'] ?? null,
            'roi_protection_enabled' => $data['roi_protection_enabled'] ?? false,
            'deep_bid_type' => $data['deep_bid_type'] ?? null,
            'roas_bid' => $data['roas_bid'] ?? null,
            'budget_details' => $data['budget'] ?? null,
            'schedule_type' => $data['schedule_type'] ?? null,
            'schedule_start_time' => $this->parseDateTime($data['schedule_start_time'] ?? null),
            'schedule_end_time' => $this->parseDateTime($data['schedule_end_time'] ?? null),
            'placements' => isset($data['placements']) ? $data['placements'] : null,
            'location_ids' => isset($data['location_ids']) ? $data['location_ids'] : null,
            'age_groups' => isset($data['age_groups']) ? $data['age_groups'] : null,
            'product_video_specific_type' => $data['product_video_specific_type'] ?? null,
            'affiliate_posts_enabled' => $data['affiliate_posts_enabled'] ?? false,
            'item_group_ids' => isset($data['item_group_ids']) ? $data['item_group_ids'] : null,
            'identity_list' => isset($data['identity_list']) ? $data['identity_list'] : null,
            'item_list' => isset($data['item_list']) ? $data['item_list'] : null,
            'custom_anchor_video_list' => isset($data['custom_anchor_video_list']) ? $data['custom_anchor_video_list'] : null,
            'campaign_custom_anchor_video_id' => $data['campaign_custom_anchor_video_id'] ?? null,
            'details_synced_at' => now(),
        ];

        // Remove null values
        $updateData = array_filter($updateData, function ($value) {
            return $value !== null;
        });

        $campaign->update($updateData);
    }

    protected function parseDateTime(?string $datetime): ?\Carbon\Carbon
    {
        if (empty($datetime)) {
            return null;
        }

        try {
            return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $datetime, 'UTC');
        } catch (\Exception $e) {
            return null;
        }
    }
}
