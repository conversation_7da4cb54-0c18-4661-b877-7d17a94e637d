<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use Filament\Tables\Actions\BulkAction;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;

class BulkRefreshAiAnalysisAction
{
    public static function make(): BulkAction
    {
        return BulkAction::make('refresh_ai_analysis')
            ->label('Refresh AI Analysis')
            ->icon('heroicon-o-cpu-chip')
            ->color('info')
            ->requiresConfirmation()
            ->modalHeading('Refresh AI Analysis for Selected Campaigns')
            ->modalDescription('This will generate new AI analysis for all selected campaigns. This may take several minutes.')
            ->modalSubmitActionLabel('Start Analysis')
            ->action(function (Collection $records) {
                $successCount = 0;
                $errorCount = 0;
                
                foreach ($records as $record) {
                    try {
                        Artisan::call('ai:analyze-campaigns', [
                            '--campaign' => $record->id,
                            '--force' => true,
                            '--type' => 'all'
                        ]);
                        $successCount++;
                    } catch (\Exception $e) {
                        $errorCount++;
                        Log::error('Bulk AI analysis failed', [
                            'campaign_id' => $record->id,
                            'campaign_name' => $record->name,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
                
                $message = "✅ AI analysis completed for {$successCount} campaigns";
                if ($errorCount > 0) {
                    $message .= "\n⚠️ {$errorCount} campaigns failed";
                }
                
                Notification::make()
                    ->title('Bulk AI Analysis Completed')
                    ->body($message)
                    ->success()
                    ->duration(8000)
                    ->send();
            });
    }
}
