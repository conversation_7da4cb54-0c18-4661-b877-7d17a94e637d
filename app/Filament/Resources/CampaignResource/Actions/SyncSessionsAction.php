<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use App\Services\JobNotificationService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncSessionsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_sessions';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Sessions')
            ->icon('heroicon-o-arrow-path')
            ->color('warning')
            ->requiresConfirmation()
            ->modalHeading('Sync Sessions from TikTok API')
            ->modalDescription('This will fetch and sync all sessions for this campaign from TikTok API.')
            ->modalSubmitActionLabel('Start Sync')
            ->action(function (Campaign $record) {
                $this->syncSessions($record);
            });
    }

    protected function syncSessions(Campaign $record): void
    {
        try {
            $apiService = new TikTokApiService();
            $syncService = new TikTokSyncService($apiService);

            // Sync sessions for this campaign
            $result = $syncService->syncSessionsForCampaign($record);

            if ($result['success']) {
                $syncedCount = $result['synced'] ?? 0;
                $createdCount = $result['created'] ?? 0;
                $updatedCount = $result['updated'] ?? 0;

                $message = "Đồng bộ sessions thành công cho campaign: {$record->name}\n\n" .
                          "📊 Tổng sessions: {$syncedCount}\n" .
                          "🆕 Tạo mới: {$createdCount}\n" .
                          "🔄 Cập nhật: {$updatedCount}";

                // Send notification to admin users
                JobNotificationService::sendSuccessNotification(
                    'Đồng bộ Sessions',
                    $message,
                    [
                        'campaign_id' => $record->campaign_id,
                        'campaign_name' => $record->name,
                        'synced' => $syncedCount,
                        'created' => $createdCount,
                        'updated' => $updatedCount
                    ]
                );

                // Also show local notification for immediate feedback
                Notification::make()
                    ->title('🎉 Đồng bộ Sessions thành công')
                    ->body("Đã đồng bộ {$syncedCount} sessions cho {$record->name}")
                    ->success()
                    ->duration(6000)
                    ->send();

            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $errorMessage = $result['error'] ?? 'Unknown sync error';

                $title = match ($errorType) {
                    'api_error' => 'Lỗi TikTok API',
                    'authentication_error' => 'Lỗi xác thực',
                    'campaign_not_found' => 'Không tìm thấy Campaign',
                    'no_sessions_found' => 'Không có Sessions',
                    'rate_limit_error' => 'Vượt quá giới hạn API',
                    default => 'Lỗi đồng bộ Sessions'
                };

                // Send error notification to admin users
                JobNotificationService::sendErrorNotification(
                    'Đồng bộ Sessions',
                    "Campaign: {$record->name}\nLỗi: {$errorMessage}"
                );

                // Also show local notification for immediate feedback
                Notification::make()
                    ->title("❌ {$title}")
                    ->body($errorMessage)
                    ->danger()
                    ->send();
            }

        } catch (\Exception $e) {
            // Send error notification to admin users
            JobNotificationService::sendErrorNotification(
                'Đồng bộ Sessions',
                "Lỗi không mong muốn: " . $e->getMessage()
            );

            // Also show local notification for immediate feedback
            Notification::make()
                ->title('💥 Lỗi đồng bộ Sessions')
                ->body('Lỗi không mong muốn: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
