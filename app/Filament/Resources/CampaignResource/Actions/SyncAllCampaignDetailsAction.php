<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Jobs\SyncCampaignDetailsJob;
use App\Models\Campaign;
use App\Services\JobNotificationService;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Log;

class SyncAllCampaignDetailsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_all_campaign_details';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync All Campaign Details')
            ->icon('heroicon-o-document-magnifying-glass')
            ->color('info')
            ->modalHeading('Sync All Campaign Details')
            ->modalDescription('Đồng bộ chi tiết GMV Max Campaign cho tất cả campaigns từ TikTok API endpoint /campaign/gmv_max/info/. Quá trình này sẽ chạy trong background queue.')
            ->modalWidth(MaxWidth::TwoExtraLarge)
            ->form([
                \Filament\Forms\Components\Select::make('filter_campaigns')
                    ->label('Filter Campaigns')
                    ->options([
                        'all' => 'Tất cả campaigns',
                        'missing_details' => 'Chỉ campaigns chưa có chi tiết',
                        'outdated_details' => 'Chỉ campaigns có chi tiết cũ (>24h)',
                        'active_only' => 'Chỉ campaigns đang hoạt động'
                    ])
                    ->default('missing_details')
                    ->required()
                    ->helperText('Chọn loại campaigns cần đồng bộ'),

                \Filament\Forms\Components\Toggle::make('force_sync')
                    ->label('Force Sync')
                    ->helperText('Đồng bộ lại ngay cả khi đã có dữ liệu chi tiết gần đây')
                    ->default(false),

                \Filament\Forms\Components\Toggle::make('notify_users')
                    ->label('Send Notifications')
                    ->helperText('Gửi thông báo cho admin users khi hoàn thành')
                    ->default(true),

                \Filament\Forms\Components\TextInput::make('delay_seconds')
                    ->label('Delay Between Jobs (seconds)')
                    ->helperText('Thời gian delay giữa các jobs để tránh rate limit')
                    ->numeric()
                    ->default(3)
                    ->minValue(1)
                    ->maxValue(60),

                \Filament\Forms\Components\TextInput::make('batch_size')
                    ->label('Batch Size')
                    ->helperText('Số lượng campaigns tối đa trong một batch')
                    ->numeric()
                    ->default(50)
                    ->minValue(1)
                    ->maxValue(200),
            ])
            ->modalSubmitActionLabel('Start Sync All')
            ->modalCancelActionLabel('Cancel')
            ->action(function (array $data) {
                $this->syncAllCampaignDetails($data);
            });
    }

    protected function syncAllCampaignDetails(array $data): void
    {
        try {
            // Get campaigns based on filter
            $campaigns = $this->getCampaignsToSync($data);

            if ($campaigns->isEmpty()) {
                Notification::make()
                    ->title('ℹ️ Không có campaigns cần đồng bộ')
                    ->body('Không tìm thấy campaigns phù hợp với bộ lọc đã chọn.')
                    ->info()
                    ->duration(6000)
                    ->send();
                return;
            }

            $totalCampaigns = $campaigns->count();
            $batchSize = $data['batch_size'] ?? 50;
            $delaySeconds = $data['delay_seconds'] ?? 3;
            $notifyUsers = $data['notify_users'] ?? true;

            // Limit to batch size
            if ($totalCampaigns > $batchSize) {
                $campaigns = $campaigns->take($batchSize);

                Notification::make()
                    ->title('⚠️ Giới hạn batch size')
                    ->body("Tìm thấy {$totalCampaigns} campaigns nhưng chỉ xử lý {$batchSize} campaigns đầu tiên theo batch size.")
                    ->warning()
                    ->duration(8000)
                    ->send();
            }

            $dispatchedCount = 0;
            foreach ($campaigns as $index => $campaign) {
                // Dispatch job with delay to avoid rate limiting
                SyncCampaignDetailsJob::dispatch(
                    $campaign->campaign_id,
                    $campaign->advertiser_id,
                    false // Don't notify for individual jobs in bulk operation
                )->delay(now()->addSeconds($index * $delaySeconds));

                $dispatchedCount++;
            }

            // Log bulk operation
            Log::info('Sync all campaign details jobs dispatched', [
                'total_found' => $totalCampaigns,
                'dispatched' => $dispatchedCount,
                'filter' => $data['filter_campaigns'],
                'delay_seconds' => $delaySeconds,
                'batch_size' => $batchSize,
                'force_sync' => $data['force_sync'] ?? false
            ]);

            // Send summary notification to admin users
            if ($notifyUsers) {
                $adminMessage = "Đã tạo {$dispatchedCount} jobs đồng bộ chi tiết campaigns";
                if ($totalCampaigns > $dispatchedCount) {
                    $adminMessage .= "\n📊 Tổng tìm thấy: {$totalCampaigns}, xử lý: {$dispatchedCount}";
                }
                $adminMessage .= "\n🔍 Bộ lọc: " . $this->getFilterLabel($data['filter_campaigns']);
                $adminMessage .= "\n⏱️ Delay giữa các jobs: {$delaySeconds}s";
                $adminMessage .= "\n🔄 Chạy 'php artisan queue:work' để xử lý";

                JobNotificationService::sendSuccessNotification(
                    'Sync All Campaign Details',
                    $adminMessage,
                    [
                        'total_found' => $totalCampaigns,
                        'dispatched' => $dispatchedCount,
                        'filter' => $data['filter_campaigns'],
                        'delay_seconds' => $delaySeconds,
                        'batch_size' => $batchSize
                    ]
                );
            }

            // Show immediate feedback
            $localMessage = "✅ Đã tạo {$dispatchedCount} jobs đồng bộ chi tiết campaigns";
            if ($totalCampaigns > $dispatchedCount) {
                $localMessage .= "\n📊 Tổng tìm thấy: {$totalCampaigns}, xử lý: {$dispatchedCount}";
            }
            $localMessage .= "\n🔄 Chạy 'php artisan queue:work' để xử lý";

            Notification::make()
                ->title('Sync All Campaign Details Started')
                ->body($localMessage)
                ->success()
                ->duration(10000)
                ->send();

        } catch (\Exception $e) {
            Log::error('Sync all campaign details failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Send error notification to admin users
            if ($data['notify_users'] ?? true) {
                JobNotificationService::sendErrorNotification(
                    'Sync All Campaign Details',
                    "Lỗi khi tạo sync all jobs: " . $e->getMessage()
                );
            }

            Notification::make()
                ->title('❌ Sync All Campaign Details Failed')
                ->body('Lỗi: ' . $e->getMessage())
                ->danger()
                ->duration(10000)
                ->send();
        }
    }

    protected function getCampaignsToSync(array $data)
    {
        $query = Campaign::query();

        switch ($data['filter_campaigns']) {
            case 'missing_details':
                $query->whereNull('details_synced_at');
                break;

            case 'outdated_details':
                $query->where('details_synced_at', '<', now()->subHours(24));
                break;

            case 'active_only':
                $query->where('status', 'active');
                if (!($data['force_sync'] ?? false)) {
                    $query->where(function ($q) {
                        $q->whereNull('details_synced_at')
                            ->orWhere('details_synced_at', '<', now()->subHours(1));
                    });
                }
                break;

            case 'all':
            default:
                if (!($data['force_sync'] ?? false)) {
                    $query->where(function ($q) {
                        $q->whereNull('details_synced_at')
                            ->orWhere('details_synced_at', '<', now()->subHours(1));
                    });
                }
                break;
        }

        return $query->select('campaign_id', 'advertiser_id', 'name', 'status', 'details_synced_at')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    protected function getFilterLabel(string $filter): string
    {
        return match ($filter) {
            'all' => 'Tất cả campaigns',
            'missing_details' => 'Campaigns chưa có chi tiết',
            'outdated_details' => 'Campaigns có chi tiết cũ (>24h)',
            'active_only' => 'Campaigns đang hoạt động',
            default => 'Unknown filter'
        };
    }
}
