<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AdvertiserAccountResource\Actions\SyncAllFromApiAction;
use App\Filament\Resources\AdvertiserAccountResource\Actions\SyncSingleAction;
use App\Filament\Resources\AdvertiserAccountResource\Pages;
use App\Filament\Resources\AdvertiserAccountResource\RelationManagers;
use App\Helpers\DateHelper;
use App\Models\AdvertiserAccount;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AdvertiserAccountResource extends Resource
{
    protected static ?string $model = AdvertiserAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Quyền & Ủy quyền';

    protected static ?int $navigationSort = 2;

    protected static ?string $modelLabel = 'Tài khoản quảng cáo';

    protected static ?string $pluralModelLabel = 'Tài khoản quảng cáo';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        TextInput::make('advertiser_id')
                            ->label('Advertiser ID')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),

                        TextInput::make('advertiser_name')
                            ->label('Tên tài khoản quảng cáo')
                            ->required()
                            ->maxLength(255),

                        Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'active' => 'Hoạt động',
                                'inactive' => 'Không hoạt động',
                                'suspended' => 'Tạm ngưng',
                                'pending' => 'Chờ xử lý',
                            ])
                            ->default('active')
                            ->required(),

                        Select::make('currency')
                            ->label('Tiền tệ')
                            ->options([
                                'VND' => 'Việt Nam Đồng (VND)',
                                'USD' => 'US Dollar (USD)',
                                'THB' => 'Thai Baht (THB)',
                            ])
                            ->default('VND'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Thông tin công ty')
                    ->schema([
                        TextInput::make('company_name')
                            ->label('Tên công ty')
                            ->maxLength(255),

                        TextInput::make('industry')
                            ->label('Ngành nghề')
                            ->maxLength(255),

                        TagsInput::make('permissions')
                            ->label('Quyền hạn')
                            ->suggestions([
                                'campaign_management',
                                'reporting',
                                'billing',
                                'user_management',
                                'api_access'
                            ]),

                        TextInput::make('balance')
                            ->label('Số dư tài khoản')
                            ->numeric()
                            ->step(0.01)
                            ->default(0),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('advertiser_id')
                    ->label('Advertiser ID')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('advertiser_name')
                    ->label('Tên tài khoản')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'pending',
                        'danger' => 'suspended',
                        'secondary' => 'inactive',
                    ])
                    ->icons([
                        'heroicon-o-check-circle' => 'active',
                        'heroicon-o-clock' => 'pending',
                        'heroicon-o-x-circle' => 'suspended',
                        'heroicon-o-minus-circle' => 'inactive',
                    ]),

                TextColumn::make('currency')
                    ->label('Tiền tệ')
                    ->badge()
                    ->sortable(),

                TextColumn::make('formatted_balance')
                    ->label('Số dư')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy('balance', $direction);
                    }),

                TextColumn::make('shops_count')
                    ->label('Số shops')
                    ->counts('shops')
                    ->sortable(),

                TextColumn::make('company_name')
                    ->label('Công ty')
                    ->limit(25)
                    ->toggleable(),

                TextColumn::make('authorized_at')
                    ->label('Ngày ủy quyền')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),

                TextColumn::make('last_sync_at')
                    ->label('Sync cuối')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'suspended' => 'Tạm ngưng',
                        'pending' => 'Chờ xử lý',
                    ]),

                SelectFilter::make('currency')
                    ->label('Tiền tệ')
                    ->options([
                        'VND' => 'VND',
                        'USD' => 'USD',
                        'THB' => 'THB',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                SyncSingleAction::make(),
            ])
            ->headerActions([
                SyncAllFromApiAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdvertiserAccounts::route('/'),
            'create' => Pages\CreateAdvertiserAccount::route('/create'),
            'edit' => Pages\EditAdvertiserAccount::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
