<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Helpers\DateHelper;
use App\Models\Product;
use App\Models\Shop;
use App\Services\TikTok\ProductSyncService;
use App\Services\TikTok\TikTokApiService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Quản lý cửa hàng';

    protected static ?int $navigationSort = 2;

    protected static ?string $modelLabel = 'Sản phẩm';

    protected static ?string $pluralModelLabel = 'Sản phẩm';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Forms\Components\TextInput::make('item_group_id')
                            ->label('SPU ID')
                            ->required()
                            ->maxLength(255)
                            ->helperText('ID sản phẩm từ TikTok API'),
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề sản phẩm')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('store_id')
                            ->label('Store ID')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('shop_id')
                            ->label('Cửa hàng')
                            ->relationship('shop', 'name')
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Giá cả')
                    ->schema([
                        Forms\Components\TextInput::make('min_price')
                            ->label('Giá tối thiểu')
                            ->numeric()
                            ->step(0.01)
                            ->prefix('$'),
                        Forms\Components\TextInput::make('max_price')
                            ->label('Giá tối đa')
                            ->numeric()
                            ->step(0.01)
                            ->prefix('$'),
                        Forms\Components\TextInput::make('currency')
                            ->label('Tiền tệ')
                            ->maxLength(10)
                            ->default('USD'),
                    ])->columns(3),

                Forms\Components\Section::make('Thông tin sản phẩm')
                    ->schema([
                        Forms\Components\TextInput::make('category')
                            ->label('Danh mục')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('historical_sales')
                            ->label('Lượt bán lịch sử')
                            ->numeric()
                            ->default(0),
                        Forms\Components\TextInput::make('product_image_url')
                            ->label('URL hình ảnh')
                            ->url()
                            ->maxLength(500),
                    ])->columns(2),

                Forms\Components\Section::make('Trạng thái')
                    ->schema([
                        Forms\Components\Select::make('api_status')
                            ->label('Trạng thái API')
                            ->options([
                                'AVAILABLE' => 'Có sẵn',
                                'NOT_AVAILABLE' => 'Không có sẵn',
                            ])
                            ->default('AVAILABLE'),
                        Forms\Components\Select::make('gmv_max_ads_status')
                            ->label('Trạng thái GMV Max')
                            ->options([
                                'OCCUPIED' => 'Đang sử dụng',
                                'UNOCCUPIED' => 'Chưa sử dụng',
                            ])
                            ->nullable(),
                        Forms\Components\Toggle::make('is_running_custom_shop_ads')
                            ->label('Đang chạy Shopping Ads'),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('item_group_id')
                    ->label('SPU ID')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('formatted_price_range')
                    ->label('Giá')
                    ->sortable(['min_price']),
                Tables\Columns\BadgeColumn::make('api_status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'AVAILABLE',
                        'danger' => 'NOT_AVAILABLE',
                    ]),
                Tables\Columns\BadgeColumn::make('gmv_max_ads_status')
                    ->label('GMV Max')
                    ->colors([
                        'warning' => 'OCCUPIED',
                        'success' => 'UNOCCUPIED',
                    ])
                    ->placeholder('N/A'),
                Tables\Columns\IconColumn::make('is_running_custom_shop_ads')
                    ->label('Shopping Ads')
                    ->boolean(),
                Tables\Columns\TextColumn::make('category')
                    ->label('Danh mục')
                    ->searchable()
                    ->limit(30)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('historical_sales')
                    ->label('Lượt bán')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('shop.name')
                    ->label('Cửa hàng')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('api_status')
                    ->label('Trạng thái')
                    ->options([
                        'AVAILABLE' => 'Có sẵn',
                        'NOT_AVAILABLE' => 'Không có sẵn',
                    ]),
                Tables\Filters\SelectFilter::make('gmv_max_ads_status')
                    ->label('GMV Max Status')
                    ->options([
                        'OCCUPIED' => 'Đang sử dụng',
                        'UNOCCUPIED' => 'Chưa sử dụng',
                    ]),
                Tables\Filters\TernaryFilter::make('is_running_custom_shop_ads')
                    ->label('Shopping Ads')
                    ->placeholder('Tất cả')
                    ->trueLabel('Đang chạy')
                    ->falseLabel('Không chạy'),
                Tables\Filters\SelectFilter::make('shop')
                    ->label('Cửa hàng')
                    ->relationship('shop', 'name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('sync_product')
                    ->label('Đồng bộ')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function (Product $record) {
                        static::syncSingleProduct($record);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('sync_selected')
                        ->label('Đồng bộ đã chọn')
                        ->icon('heroicon-o-arrow-path')
                        ->action(function ($records) {
                            static::syncMultipleProducts($records);
                        }),
                ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('sync_all_products')
                    ->label('Đồng bộ tất cả sản phẩm')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function () {
                        static::syncAllProducts();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Đồng bộ tất cả sản phẩm')
                    ->modalDescription('Bạn có chắc chắn muốn đồng bộ tất cả sản phẩm từ TikTok API?'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }

    /**
     * Đồng bộ một sản phẩm từ TikTok API
     */
    protected static function syncSingleProduct(Product $product): void
    {
        try {
            $shop = $product->shop;
            if (!$shop) {
                Notification::make()
                    ->title('Lỗi đồng bộ')
                    ->body('Không tìm thấy thông tin cửa hàng')
                    ->danger()
                    ->send();
                return;
            }

            $apiService = app(TikTokApiService::class);
            $syncService = new ProductSyncService($apiService);

            $result = $syncService->syncShopProducts($shop, [
                'filtering' => [
                    'item_group_ids' => [$product->item_group_id]
                ]
            ]);

            if ($result['success'] ?? false) {
                $data = $result['data'];
                $message = 'Sản phẩm đã được đồng bộ từ TikTok API';

                // Add info about data source if using fallback
                if (isset($data['data_source']) && $data['data_source'] !== 'direct_api') {
                    $message .= ' (sử dụng dữ liệu fallback)';
                }

                Notification::make()
                    ->title('Đồng bộ thành công')
                    ->body($message)
                    ->success()
                    ->send();
            } else {
                $errorType = \App\Helpers\ErrorHandler::getErrorType($result);
                $errorMessage = \App\Helpers\ErrorHandler::getErrorMessage($result);

                if ($errorType === \App\Helpers\ErrorHandler::PERMISSION_ERROR) {
                    Notification::make()
                        ->title('Lỗi quyền truy cập')
                        ->body($errorMessage . ' Vui lòng liên hệ admin để cấp quyền store management.')
                        ->warning()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Đồng bộ thất bại')
                        ->body($errorMessage)
                        ->danger()
                        ->send();
                }
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi đồng bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Đồng bộ nhiều sản phẩm
     */
    protected static function syncMultipleProducts($products): void
    {
        try {
            $apiService = app(TikTokApiService::class);
            $syncService = new ProductSyncService($apiService);

            $shopGroups = $products->groupBy('shop_id');
            $totalSynced = 0;
            $totalErrors = 0;

            foreach ($shopGroups as $shopId => $shopProducts) {
                $shop = Shop::find($shopId);
                if (!$shop) continue;

                $itemGroupIds = $shopProducts->pluck('item_group_id')->toArray();

                $result = $syncService->syncShopProducts($shop, [
                    'filtering' => [
                        'item_group_ids' => $itemGroupIds
                    ]
                ]);

                if ($result['success'] ?? false) {
                    $totalSynced += ($result['data']['created'] ?? 0) + ($result['data']['updated'] ?? 0);
                } else {
                    $totalErrors++;
                }
            }

            if ($totalErrors === 0) {
                Notification::make()
                    ->title('Đồng bộ thành công')
                    ->body("Đã đồng bộ {$totalSynced} sản phẩm")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Đồng bộ hoàn tất với lỗi')
                    ->body("Đồng bộ {$totalSynced} sản phẩm, {$totalErrors} lỗi")
                    ->warning()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi đồng bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Đồng bộ tất cả sản phẩm từ tất cả shops
     */
    protected static function syncAllProducts(): void
    {
        try {
            $apiService = app(TikTokApiService::class);
            $syncService = new ProductSyncService($apiService);

            $result = $syncService->syncAllShopsProducts();

            if ($result['success'] ?? false) {
                $data = $result['data'];
                Notification::make()
                    ->title('Đồng bộ hoàn tất')
                    ->body("Đã xử lý {$data['total_shops']} cửa hàng. Thành công: {$data['successful_shops']}, Lỗi: {$data['failed_shops']}")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Đồng bộ thất bại')
                    ->body($result['message'] ?? 'Có lỗi xảy ra khi đồng bộ')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi đồng bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
