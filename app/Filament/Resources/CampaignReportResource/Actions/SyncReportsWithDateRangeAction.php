<?php

namespace App\Filament\Resources\CampaignReportResource\Actions;

use App\Models\Campaign;
use App\Services\TikTok\GmvMaxReportService;
use App\Services\TikTok\TikTokApiService;
use Carbon\Carbon;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class SyncReportsWithDateRangeAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_reports_with_daterange';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Reports (DateRange)')
            ->icon('heroicon-o-calendar-days')
            ->color('primary')
            ->form([
                Section::make('Select Date Range for Sync')
                    ->description('Choose the date range for syncing GMV Max campaign reports. Maximum 30 days allowed for daily breakdown.')
                    ->schema([
                        DateRangePicker::make('date_range')
                            ->label('Date Range')
                            ->placeholder('Select date range for sync')
                            ->default([
                                'start' => Carbon::now()->subDays(7)->format('Y-m-d'),
                                'end' => Carbon::now()->format('Y-m-d')
                            ])
                            ->required()
                            ->maxDate(Carbon::now())
                            ->helperText('Select the date range for report sync (max 30 days)')
                            ->displayFormat('DD/MM/YYYY')
                            ->format('Y-m-d')
                            ->alwaysShowCalendar()
                            ->autoApply()
                            ->showDropdowns()
                            ->maxSpan(['days' => 30])
                            ->ranges([
                                'Today' => [Carbon::now(), Carbon::now()],
                                'Yesterday' => [Carbon::yesterday(), Carbon::yesterday()],
                                'Last 7 Days' => [Carbon::now()->subDays(6), Carbon::now()],
                                'Last 14 Days' => [Carbon::now()->subDays(13), Carbon::now()],
                                'Last 30 Days' => [Carbon::now()->subDays(29), Carbon::now()],
                                'This Month' => [Carbon::now()->startOfMonth(), Carbon::now()],
                                'Last Month' => [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()],
                            ]),
                    ])
                    ->collapsible()
                    ->persistCollapsed(false),
            ])
            ->modalHeading('Sync GMV Max Campaign Reports - Advanced')
            ->modalDescription('Select your preferred date range using the advanced date picker and sync GMV Max campaign reports from TikTok API. This process may take a few minutes depending on the date range.')
            ->modalSubmitActionLabel('Start Sync')
            ->modalWidth('lg')
            ->action(function (array $data) {
                $this->syncGmvMaxReportsWithDateRange($data);
            });
    }

    /**
     * Parse date string with multiple format support
     */
    private function parseDate(string $dateStr): ?Carbon
    {
        $dateStr = trim($dateStr);

        // List of possible date formats
        $formats = [
            'Y-m-d',           // 2025-07-18
            'd/m/Y',           // 18/07/2025
            'm/d/Y',           // 07/18/2025 (US format)
            'Y/m/d',           // 2025/07/18
            'd-m-Y',           // 18-07-2025
            'm-d-Y',           // 07-18-2025
            'j/n/Y',           // 18/7/2025 (without leading zeros)
            'n/j/Y',           // 7/18/2025 (US format without leading zeros)
        ];

        // Try each format
        foreach ($formats as $format) {
            try {
                $date = Carbon::createFromFormat($format, $dateStr);
                if ($date && $date->format($format) === $dateStr) {
                    \Log::info("Successfully parsed date '{$dateStr}' using format '{$format}'");
                    return $date;
                }
            } catch (\Exception $e) {
                // Continue to next format
                continue;
            }
        }

        // Fallback: try Carbon::parse() for other formats
        try {
            return Carbon::parse($dateStr);
        } catch (\Exception $e) {
            \Log::error('Failed to parse date: ' . $dateStr, ['error' => $e->getMessage()]);
            return null;
        }
    }

    protected function syncGmvMaxReportsWithDateRange(array $data): void
    {
        try {
            // Extract date range
            $dateRange = $data['date_range'] ?? null;

            // Debug: Log the data structure
            \Log::info('DateRangePicker data received:', ['date_range' => $dateRange, 'full_data' => $data]);

            if (empty($dateRange)) {
                Notification::make()
                    ->title('Invalid Date Range')
                    ->body('Please select a date range.')
                    ->warning()
                    ->duration(6000)
                    ->send();
                return;
            }

            // Handle array format from DateRangePicker
            if (is_array($dateRange) && isset($dateRange['start']) && isset($dateRange['end'])) {
                $startDateStr = $dateRange['start'];
                $endDateStr = $dateRange['end'];
            } else {
                // Fallback: Parse string format if still received
                $parts = explode(' - ', $dateRange);
                if (count($parts) !== 2) {
                    Notification::make()
                        ->title('Invalid Date Range Format')
                        ->body('Date range format is invalid. Please select a valid date range.')
                        ->warning()
                        ->duration(8000)
                        ->send();
                    return;
                }
                $startDateStr = trim($parts[0]);
                $endDateStr = trim($parts[1]);
            }

            // Parse dates using the extracted values with multiple format support
            $startDate = $this->parseDate($startDateStr);
            $endDate = $this->parseDate($endDateStr);

            if (!$startDate || !$endDate) {
                Notification::make()
                    ->title('Invalid Date Format')
                    ->body('Could not parse the selected dates. Please try selecting the date range again.')
                    ->warning()
                    ->duration(8000)
                    ->send();
                return;
            }

            // Validate date range constraints for GMV Max API
            $daysDiff = $startDate->diffInDays($endDate);

            if ($daysDiff > 30) {
                Notification::make()
                    ->title('Date Range Too Large')
                    ->body('Date range cannot exceed 30 days for daily breakdown reports. Please select a shorter range.')
                    ->warning()
                    ->duration(8000)
                    ->send();
                return;
            }

            if ($startDate->isFuture() || $endDate->isFuture()) {
                Notification::make()
                    ->title('Invalid Date Range')
                    ->body('Start date and end date cannot be in the future.')
                    ->warning()
                    ->duration(6000)
                    ->send();
                return;
            }

            if ($startDate->isAfter($endDate)) {
                Notification::make()
                    ->title('Invalid Date Range')
                    ->body('Start date must be before or equal to end date.')
                    ->warning()
                    ->duration(6000)
                    ->send();
                return;
            }

            $apiService = new TikTokApiService();
            $reportService = new GmvMaxReportService($apiService);

            // Get store IDs from campaigns via shop relationship
            // Note: shop_id in gmv_max_campaigns table is foreign key to tiktok_shops.id
            // We need shop_id from tiktok_shops table which is the TikTok Store ID
            $storeIds = Campaign::join('tiktok_shops', 'gmv_max_campaigns.shop_id', '=', 'tiktok_shops.id')
                ->whereNotNull('tiktok_shops.shop_id')
                ->where('gmv_max_campaigns.deleted_at', null) // Only active campaigns
                ->distinct()
                ->pluck('tiktok_shops.shop_id')
                ->toArray();

            if (empty($storeIds)) {
                Notification::make()
                    ->title('No Store IDs Found')
                    ->body('No GMV Max campaigns with store IDs found. Please ensure campaigns are properly configured.')
                    ->warning()
                    ->send();
                return;
            }

            // Use selected date range (already in string format)
            $startDateForSync = $startDate->format('Y-m-d');
            $endDateForSync = $endDate->format('Y-m-d');

            // Show progress notification
            Notification::make()
                ->title('Sync Started')
                ->body("Starting sync for date range: {$startDateForSync} to {$endDateForSync} ({$daysDiff} days)")
                ->info()
                ->duration(5000)
                ->send();

            $result = $reportService->syncReports($storeIds, $startDateForSync, $endDateForSync);

            if ($result['success']) {
                $totalSynced = $result['total_synced'] ?? 0;
                $createdCount = $result['created'] ?? 0;
                $updatedCount = $result['updated'] ?? 0;

                $bodyMessage = "Successfully synced {$totalSynced} GMV Max campaign reports";
                $bodyMessage .= "\n📅 Date Range: {$startDateForSync} to {$endDateForSync} ({$daysDiff} days)";

                if ($createdCount > 0 || $updatedCount > 0) {
                    $bodyMessage .= "\n📊 Details: {$createdCount} created, {$updatedCount} updated";
                }

                Notification::make()
                    ->title('GMV Max Reports Synced Successfully')
                    ->body($bodyMessage)
                    ->success()
                    ->duration(12000)
                    ->send();

            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $errorMessage = $result['error'] ?? 'Unknown sync error';

                $title = match ($errorType) {
                    'api_error' => 'TikTok API Error',
                    'authentication_error' => 'Authentication Failed',
                    'rate_limit_error' => 'Rate Limit Exceeded',
                    'network_error' => 'Network Connection Error',
                    'sync_error' => 'Sync Process Error',
                    'validation_error' => 'Date Range Validation Error',
                    default => 'Sync Failed'
                };

                Notification::make()
                    ->title($title)
                    ->body($errorMessage . "\nDate Range: {$startDateForSync} to {$endDateForSync}")
                    ->danger()
                    ->duration(10000)
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('GMV Max Sync Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->duration(8000)
                ->send();
        }
    }
}
