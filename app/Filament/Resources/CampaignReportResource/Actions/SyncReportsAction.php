<?php

namespace App\Filament\Resources\CampaignReportResource\Actions;

use App\Models\Campaign;
use App\Services\TikTok\GmvMaxReportService;
use App\Services\TikTok\TikTokApiService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class SyncReportsAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_reports';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Reports (Basic)')
            ->icon('heroicon-o-cloud-arrow-down')
            ->color('success')
            ->form([
                Section::make('Select Date Range for Sync')
                    ->description('Choose the date range for syncing GMV Max campaign reports. Maximum 30 days allowed for daily breakdown.')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                DatePicker::make('start_date')
                                    ->label('Start Date')
                                    ->default(Carbon::now()->subDays(7)->format('Y-m-d'))
                                    ->required()
                                    ->maxDate(Carbon::now())
                                    ->helperText('Select the start date for report sync')
                                    ->validationAttribute('start date'),

                                DatePicker::make('end_date')
                                    ->label('End Date')
                                    ->default(Carbon::now()->format('Y-m-d'))
                                    ->required()
                                    ->maxDate(Carbon::now())
                                    ->helperText('Select the end date for report sync')
                                    ->validationAttribute('end date'),
                            ]),
                    ])
                    ->collapsible()
                    ->persistCollapsed(false),
            ])
            ->modalHeading('Sync GMV Max Campaign Reports')
            ->modalDescription('Select your preferred date range and sync GMV Max campaign reports from TikTok API. This process may take a few minutes depending on the date range.')
            ->modalSubmitActionLabel('Start Sync')
            ->modalWidth('lg')
            ->action(function (array $data) {
                $this->syncGmvMaxReports($data);
            });
    }

    protected function syncGmvMaxReports(array $data): void
    {
        try {
            // Validate date range
            $startDate = Carbon::parse($data['start_date']);
            $endDate = Carbon::parse($data['end_date']);

            // Validate date range constraints for GMV Max API
            $daysDiff = $startDate->diffInDays($endDate);

            if ($daysDiff > 30) {
                Notification::make()
                    ->title('Invalid Date Range')
                    ->body('Date range cannot exceed 30 days for daily breakdown reports. Please select a shorter range.')
                    ->warning()
                    ->duration(8000)
                    ->send();
                return;
            }

            if ($startDate->isFuture() || $endDate->isFuture()) {
                Notification::make()
                    ->title('Invalid Date Range')
                    ->body('Start date and end date cannot be in the future.')
                    ->warning()
                    ->duration(6000)
                    ->send();
                return;
            }

            if ($startDate->isAfter($endDate)) {
                Notification::make()
                    ->title('Invalid Date Range')
                    ->body('Start date must be before or equal to end date.')
                    ->warning()
                    ->duration(6000)
                    ->send();
                return;
            }

            $apiService = new TikTokApiService();
            $reportService = new GmvMaxReportService($apiService);

            // Get store IDs from campaigns via shop relationship
            // Note: shop_id in gmv_max_campaigns table is foreign key to tiktok_shops.id
            // We need shop_id from tiktok_shops table which is the TikTok Store ID
            $storeIds = Campaign::join('tiktok_shops', 'gmv_max_campaigns.shop_id', '=', 'tiktok_shops.id')
                ->whereNotNull('tiktok_shops.shop_id')
                ->where('gmv_max_campaigns.deleted_at', null) // Only active campaigns
                ->distinct()
                ->pluck('tiktok_shops.shop_id')
                ->toArray();

            if (empty($storeIds)) {
                Notification::make()
                    ->title('No Store IDs Found')
                    ->body('No GMV Max campaigns with store IDs found. Please ensure campaigns are properly configured.')
                    ->warning()
                    ->send();
                return;
            }

            // Use selected date range
            $startDateStr = $startDate->format('Y-m-d');
            $endDateStr = $endDate->format('Y-m-d');

            $result = $reportService->syncReports($storeIds, $startDateStr, $endDateStr);

            if ($result['success']) {
                $totalSynced = $result['total_synced'] ?? 0;
                $createdCount = $result['created'] ?? 0;
                $updatedCount = $result['updated'] ?? 0;

                $bodyMessage = "Synced {$totalSynced} GMV Max campaign reports from {$startDateStr} to {$endDateStr}";
                if ($createdCount > 0 || $updatedCount > 0) {
                    $bodyMessage .= " ({$createdCount} created, {$updatedCount} updated)";
                }

                Notification::make()
                    ->title('GMV Max Reports Synced Successfully')
                    ->body($bodyMessage)
                    ->success()
                    ->duration(10000)
                    ->send();

            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $errorMessage = $result['error'] ?? 'Unknown sync error';

                $title = match ($errorType) {
                    'api_error' => 'TikTok API Error',
                    'authentication_error' => 'Authentication Failed',
                    'rate_limit_error' => 'Rate Limit Exceeded',
                    'network_error' => 'Network Connection Error',
                    'sync_error' => 'Sync Process Error',
                    'validation_error' => 'Date Range Validation Error',
                    default => 'Sync Failed'
                };

                Notification::make()
                    ->title($title)
                    ->body($errorMessage)
                    ->danger()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('GMV Max Sync Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
