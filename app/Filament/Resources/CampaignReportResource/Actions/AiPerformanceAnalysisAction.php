<?php

namespace App\Filament\Resources\CampaignReportResource\Actions;

use App\Services\AI\AIScoringService;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class AiPerformanceAnalysisAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'ai_performance_analysis';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('AI Performance Analysis')
            ->icon('heroicon-o-cpu-chip')
            ->color('purple')
            ->tooltip('Phân tích hiệu suất chiến dịch bằng AI')
            ->action(function () {
                $this->performAiAnalysis();
            });
    }

    protected function performAiAnalysis(): void
    {
        try {
            $aiService = new AIScoringService();
            $result = $aiService->generatePerformanceAlerts();

            if ($result['success']) {
                $alertCount = $result['total_alerts'] ?? 0;
                $insights = $result['insights'] ?? [];
                $recommendations = $result['recommendations'] ?? [];

                $bodyMessage = "Generated {$alertCount} performance insights";
                if (!empty($recommendations)) {
                    $topRecommendations = array_slice($recommendations, 0, 2);
                    $bodyMessage .= " and recommendations: " . implode(', ', $topRecommendations);
                }

                Notification::make()
                    ->title('AI Performance Analysis Complete')
                    ->body($bodyMessage)
                    ->success()
                    ->duration(8000) // Show longer for important results
                    ->send();

            } else {
                $errorType = $result['error_type'] ?? 'unknown';
                $errorMessage = $result['error'] ?? 'Unknown error occurred';

                $title = match ($errorType) {
                    'insufficient_data' => 'Insufficient Data for Analysis',
                    'api_error' => 'AI Service Error',
                    'configuration_error' => 'Configuration Error',
                    default => 'AI Analysis Failed'
                };

                Notification::make()
                    ->title($title)
                    ->body($errorMessage)
                    ->warning()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('AI Analysis Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
