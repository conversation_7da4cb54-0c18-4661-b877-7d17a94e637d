<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ExclusiveAuthorizationResource\Pages;
use App\Filament\Resources\ExclusiveAuthorizationResource\RelationManagers;
use App\Helpers\DateHelper;
use App\Models\ExclusiveAuthorization;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;

class ExclusiveAuthorizationResource extends Resource
{
    protected static ?string $model = ExclusiveAuthorization::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Quyền & Ủy quyền';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Ủy quyền độc quyền';

    protected static ?string $pluralModelLabel = 'Ủy quyền độc quyền';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('authorization_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('advertiser_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\DateTimePicker::make('granted_at')
                    ->label('Ngày cấp quyền')
                    ->displayFormat('d/m/Y H:i')
                    ->format('Y-m-d H:i:s'),
                Forms\Components\DateTimePicker::make('expires_at')
                    ->label('Ngày hết hạn')
                    ->displayFormat('d/m/Y H:i')
                    ->format('Y-m-d H:i:s'),
                Forms\Components\Select::make('shop_id')
                    ->relationship('shop', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('authorization_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('advertiser_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('granted_at')
                    ->label('Ngày cấp quyền')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('expires_at')
                    ->label('Ngày hết hạn')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('shop.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->headerActions([
                Action::make('sync_authorizations')
                    ->label('Sync Authorizations')
                    ->icon('heroicon-o-cloud-arrow-down')
                    ->color('success')
                    ->action(function () {
                        $apiService = new TikTokApiService();
                        $syncService = new TikTokSyncService($apiService);

                        $result = $syncService->syncExclusiveAuthorizations();

                        if ($result['success']) {
                            Notification::make()
                                ->title('Authorizations synced successfully')
                                ->body("Synced {$result['synced']} exclusive authorizations from TikTok API")
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Sync failed')
                                ->body($result['error'])
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Sync Exclusive Authorizations from TikTok API')
                    ->modalDescription('This will fetch and sync all exclusive authorizations from TikTok API.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExclusiveAuthorizations::route('/'),
            'create' => Pages\CreateExclusiveAuthorization::route('/create'),
            'edit' => Pages\EditExclusiveAuthorization::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
