<?php

namespace App\Filament\Resources\ExclusiveAuthorizationResource\Pages;

use App\Filament\Resources\ExclusiveAuthorizationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListExclusiveAuthorizations extends ListRecords
{
    protected static string $resource = ExclusiveAuthorizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
