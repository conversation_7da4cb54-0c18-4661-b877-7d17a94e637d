<?php

namespace App\Filament\Resources\ExclusiveAuthorizationResource\Pages;

use App\Filament\Resources\ExclusiveAuthorizationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExclusiveAuthorization extends EditRecord
{
    protected static string $resource = ExclusiveAuthorizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
