<?php

namespace App\Filament\Widgets;

use App\Models\Campaign;
use App\Services\AI\AIScoringService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Cache;

class AICampaignInsights extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $aiService = new AIScoringService();

        // Check if AI is enabled
        if (!$aiService->getConfig()['enabled']) {
            return [
                Stat::make('AI Analysis', 'Disabled')
                    ->description('Enable AI scoring in settings')
                    ->descriptionIcon('heroicon-m-exclamation-triangle')
                    ->color('warning'),
            ];
        }

        $campaigns = Campaign::where('status', 'active')->get();

        if ($campaigns->isEmpty()) {
            return [
                Stat::make('Active Campaigns', '0')
                    ->description('No active campaigns to analyze')
                    ->descriptionIcon('heroicon-m-information-circle')
                    ->color('gray'),
            ];
        }

        // Calculate AI insights
        $totalCampaigns = $campaigns->count();
        $highPerformingCampaigns = 0;
        $averageAIScore = 0;
        $totalBudgetOptimization = 0;
        $criticalAlerts = 0;

        foreach ($campaigns as $campaign) {
            $cacheKey = "ai_analysis_widget_{$campaign->id}";

            $analysis = Cache::remember($cacheKey, 1800, function () use ($aiService, $campaign) {
                $result = $aiService->analyzeCampaign($campaign);
                return $result['success'] ? $result : null;
            });

            if ($analysis) {
                $score = $analysis['ai_score'] ?? 0;
                $averageAIScore += $score;

                if ($score >= 80) {
                    $highPerformingCampaigns++;
                }

                if ($score < 60) {
                    $criticalAlerts++;
                }
            }

            // Budget optimization potential
            $budgetCacheKey = "ai_budget_widget_{$campaign->id}";
            $budgetAnalysis = Cache::remember($budgetCacheKey, 1800, function () use ($aiService, $campaign) {
                $result = $aiService->optimizeBudget($campaign);
                return $result['success'] ? $result : null;
            });

            if ($budgetAnalysis && isset($budgetAnalysis['expected_improvement'])) {
                $totalBudgetOptimization += $budgetAnalysis['expected_improvement'];
            }
        }

        $averageAIScore = $totalCampaigns > 0 ? round($averageAIScore / $totalCampaigns) : 0;
        $averageBudgetOptimization = $totalCampaigns > 0 ? round(($totalBudgetOptimization / $totalCampaigns) * 100) : 0;

        return [
            Stat::make('Average AI Score', $averageAIScore . '/100')
                ->description($this->getScoreDescription($averageAIScore))
                ->descriptionIcon($this->getScoreIcon($averageAIScore))
                ->color($this->getScoreColor($averageAIScore))
                ->chart($this->getScoreChart($campaigns)),

            Stat::make('High Performing Campaigns', $highPerformingCampaigns . '/' . $totalCampaigns)
                ->description('Campaigns with AI score ≥ 80')
                ->descriptionIcon('heroicon-m-trophy')
                ->color($highPerformingCampaigns > 0 ? 'success' : 'gray'),

            Stat::make('Budget Optimization Potential', '+' . $averageBudgetOptimization . '%')
                ->description('Average improvement potential')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color($averageBudgetOptimization > 10 ? 'success' : 'warning'),

            Stat::make('Critical Alerts', $criticalAlerts)
                ->description('Campaigns needing attention (score < 60)')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($criticalAlerts > 0 ? 'danger' : 'success'),
        ];
    }

    protected function getScoreDescription(int $score): string
    {
        return match (true) {
            $score >= 80 => 'Excellent performance',
            $score >= 60 => 'Good performance',
            $score >= 40 => 'Needs optimization',
            default => 'Critical attention required',
        };
    }

    protected function getScoreIcon(int $score): string
    {
        return match (true) {
            $score >= 80 => 'heroicon-m-face-smile',
            $score >= 60 => 'heroicon-m-face-frown',
            default => 'heroicon-m-exclamation-triangle',
        };
    }

    protected function getScoreColor(int $score): string
    {
        return match (true) {
            $score >= 80 => 'success',
            $score >= 60 => 'warning',
            default => 'danger',
        };
    }

    /**
     * @throws RandomException
     */
    protected function getScoreChart(object $campaigns): array
    {
        // Generate simple chart data for the last 7 days
        $chartData = [];
        for ($i = 6; $i >= 0; $i--) {
            $chartData[] = random_int(60, 90); // Simulated historical data
        }
        return $chartData;
    }
}
