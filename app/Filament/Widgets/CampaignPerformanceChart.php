<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\CampaignReport;
use Carbon\Carbon;

class CampaignPerformanceChart extends ChartWidget
{
    protected static ?string $heading = '<PERSON><PERSON><PERSON> suất chiến dịch';

    protected function getData(): array
    {
        // Lấy dữ liệu 30 ngày gần đây
        $data = CampaignReport::selectRaw('DATE(report_date) as date, SUM(gross_revenue) as revenue, SUM(total_cost) as cost')
            ->where('report_date', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Doanh thu',
                    'data' => $data->pluck('revenue')->toArray(),
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Chi phí',
                    'data' => $data->pluck('cost')->toArray(),
                    'borderColor' => '#EF4444',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $data->pluck('date')->map(function ($date) {
                return Carbon::parse($date)->format('d/m');
            })->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
