<?php

namespace App\Filament\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\Campaign;

class TopPerformingCampaigns extends BaseWidget
{
    protected static ?string $heading = '<PERSON>ến dịch hiệu suất cao';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Campaign::query()
                    ->with(['reports', 'shop'])
                    ->whereHas('reports')
                    ->selectRaw('gmv_max_campaigns.*,
                        (SELECT AVG(roi) FROM campaign_reports WHERE campaign_reports.campaign_id = gmv_max_campaigns.id) as avg_roi,
                        (SELECT SUM(gross_revenue) FROM campaign_reports WHERE campaign_reports.campaign_id = gmv_max_campaigns.id) as total_revenue_calc,
                        (SELECT SUM(total_cost) FROM campaign_reports WHERE campaign_reports.campaign_id = gmv_max_campaigns.id) as total_cost_calc')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên chiến dịch')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('shop.name')
                    ->label('Cửa hàng')
                    ->sortable(),
                Tables\Columns\TextColumn::make('target_roi')
                    ->label('Target ROI')
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('avg_roi')
                    ->label('ROI thực tế')
                    ->getStateUsing(function (Campaign $record): string {
                        $roi = $record->avg_roi ?? 0;
                        return number_format($roi, 1) . '%';
                    })
                    ->color(fn (Campaign $record): string => ($record->avg_roi ?? 0) > 100 ? 'success' : 'danger')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_revenue_calc')
                    ->label('Doanh thu')
                    ->getStateUsing(function (Campaign $record): string {
                        $revenue = $record->total_revenue_calc ?? 0;
                        return '₫' . number_format($revenue, 0, ',', '.');
                    })
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'secondary' => 'draft',
                        'success' => 'active',
                        'warning' => 'paused',
                        'info' => 'completed',
                        'danger' => 'cancelled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'draft' => 'Bản nháp',
                        'active' => 'Đang chạy',
                        'paused' => 'Tạm dừng',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    }),
            ])
            ->defaultSort('avg_roi', 'desc');
    }
}
