<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Campaign;
use App\Models\CampaignReport;

class DashboardStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('total_campaigns', 'Tổng chiến dịch')
                ->value(Campaign::count())
                ->description('Chiến dịch đang hoạt động')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),

            Stat::make('total_revenue', 'Tổng doanh thu')
                ->value('₫' . number_format(CampaignReport::sum('gross_revenue'), 0, ',', '.'))
                ->description('Doanh thu tháng này')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('average_roi', 'ROI trung bình')
                ->value(number_format(CampaignReport::avg('roi') ?? 0, 1) . '%')
                ->description('ROI trung bình tháng này')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('warning'),

            Stat::make('total_orders', 'Tổng đơn hàng')
                ->value(number_format(CampaignReport::sum('orders_count')))
                ->description('Đơn hàng tháng này')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('info'),
        ];
    }
}
