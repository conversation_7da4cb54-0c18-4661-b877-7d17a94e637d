<?php

namespace App\Filament\Pages;

use App\Models\Shop;
use App\Models\Product;
use App\Services\TikTok\ProductSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Helpers\ErrorHandler;
use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;

class TikTokProductsManagement extends Page implements Forms\Contracts\HasForms, Tables\Contracts\HasTable
{
    use InteractsWithForms, InteractsWithTable, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';
    protected static ?string $navigationLabel = 'Quản Lý Sản Phẩm TikTok';
    protected static ?string $title = 'Quản Lý Sản Phẩm TikTok';
    protected static ?string $navigationGroup = 'TikTok API Management';
    protected static ?int $navigationSort = 3;

    protected static string $view = 'filament.pages.tiktok-products-management';

    public ?array $data = [];
    public ?string $selectedShop = null;
    public bool $syncInProgress = false;

    protected ?ProductSyncService $syncService = null;

    public function mount(): void
    {
        $apiService = app(TikTokApiService::class);
        $this->syncService = new ProductSyncService($apiService);

        $this->form->fill();
    }

    protected function getSyncService(): ProductSyncService
    {
        if (!$this->syncService) {
            $apiService = app(TikTokApiService::class);
            $this->syncService = new ProductSyncService($apiService);
        }

        return $this->syncService;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Đồng Bộ Sản Phẩm TikTok')
                    ->description('Đồng bộ sản phẩm từ TikTok API vào hệ thống')
                    ->schema([
                        Forms\Components\Select::make('shop_id')
                            ->label('Chọn Cửa Hàng')
                            ->options(Shop::where('status', 'active')->pluck('name', 'id'))
                            ->searchable()
                            ->placeholder('Chọn cửa hàng để đồng bộ sản phẩm')
                            ->helperText('Chọn cửa hàng cụ thể hoặc để trống để đồng bộ tất cả'),

                        Forms\Components\Select::make('sync_type')
                            ->label('Loại Đồng Bộ')
                            ->options([
                                'all' => 'Tất cả sản phẩm',
                                'gmv_max' => 'Chỉ sản phẩm GMV Max',
                                'shopping_ads' => 'Chỉ sản phẩm Shopping Ads',
                            ])
                            ->default('all')
                            ->required(),

                        Forms\Components\TextInput::make('page_size')
                            ->label('Số Sản Phẩm Mỗi Trang')
                            ->numeric()
                            ->default(100)
                            ->minValue(1)
                            ->maxValue(100)
                            ->helperText('Số lượng sản phẩm tối đa mỗi lần gọi API'),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Product::query())
            ->columns([
                Tables\Columns\TextColumn::make('item_group_id')
                    ->label('SPU ID')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu Đề')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('shop.name')
                    ->label('Cửa Hàng')
                    ->sortable(),

                Tables\Columns\TextColumn::make('formatted_price_range')
                    ->label('Giá')
                    ->sortable(['min_price']),

                Tables\Columns\BadgeColumn::make('api_status')
                    ->label('Trạng Thái API')
                    ->colors([
                        'success' => 'AVAILABLE',
                        'danger' => 'NOT_AVAILABLE',
                    ]),

                Tables\Columns\BadgeColumn::make('gmv_max_ads_status')
                    ->label('GMV Max')
                    ->colors([
                        'warning' => 'OCCUPIED',
                        'success' => 'UNOCCUPIED',
                    ])
                    ->placeholder('N/A'),

                Tables\Columns\IconColumn::make('is_running_custom_shop_ads')
                    ->label('Shopping Ads')
                    ->boolean(),

                Tables\Columns\TextColumn::make('historical_sales')
                    ->label('Lượt Bán')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập Nhật')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('shop')
                    ->relationship('shop', 'name')
                    ->label('Cửa Hàng'),

                Tables\Filters\SelectFilter::make('api_status')
                    ->label('Trạng Thái API')
                    ->options([
                        'AVAILABLE' => 'Có sẵn',
                        'NOT_AVAILABLE' => 'Không có sẵn',
                    ]),

                Tables\Filters\SelectFilter::make('gmv_max_ads_status')
                    ->label('GMV Max Status')
                    ->options([
                        'OCCUPIED' => 'Đang sử dụng',
                        'UNOCCUPIED' => 'Chưa sử dụng',
                    ]),

                Tables\Filters\TernaryFilter::make('is_running_custom_shop_ads')
                    ->label('Shopping Ads')
                    ->placeholder('Tất cả')
                    ->trueLabel('Đang chạy')
                    ->falseLabel('Không chạy'),
            ])
            ->actions([
                Tables\Actions\Action::make('sync_product')
                    ->label('Đồng Bộ')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function (Product $record) {
                        $this->syncSingleProduct($record);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('sync_selected')
                    ->label('Đồng Bộ Đã Chọn')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function ($records) {
                        $this->syncMultipleProducts($records);
                    })
                    ->requiresConfirmation(),
            ])
            ->defaultSort('updated_at', 'desc')
            ->poll('30s');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('sync_all_products')
                ->label('Đồng Bộ Tất Cả')
                ->icon('heroicon-o-arrow-path')
                ->color('primary')
                ->action('syncAllProducts')
                ->requiresConfirmation()
                ->modalHeading('Đồng Bộ Tất Cả Sản Phẩm')
                ->modalDescription('Bạn có chắc chắn muốn đồng bộ tất cả sản phẩm từ TikTok API?')
                ->modalSubmitActionLabel('Đồng Bộ'),

            Action::make('sync_by_form')
                ->label('Đồng Bộ Theo Cấu Hình')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('success')
                ->form([
                    Forms\Components\Select::make('shop_id')
                        ->label('Cửa Hàng')
                        ->options(Shop::where('status', 'active')->pluck('name', 'id'))
                        ->searchable()
                        ->placeholder('Chọn cửa hàng hoặc để trống cho tất cả'),

                    Forms\Components\Select::make('sync_type')
                        ->label('Loại Đồng Bộ')
                        ->options([
                            'all' => 'Tất cả sản phẩm',
                            'gmv_max' => 'Chỉ sản phẩm GMV Max',
                            'shopping_ads' => 'Chỉ sản phẩm Shopping Ads',
                        ])
                        ->default('all')
                        ->required(),
                ])
                ->action('syncByConfiguration')
                ->modalWidth(MaxWidth::Medium),
        ];
    }

    public function syncAllProducts(): void
    {
        try {
            $this->syncInProgress = true;

            $result = $this->getSyncService()->syncAllShopsProducts();

            if (ErrorHandler::isSuccess($result)) {
                $data = $result['data'];
                Notification::make()
                    ->title('Đồng Bộ Thành Công')
                    ->body("Đã xử lý {$data['total_shops']} cửa hàng. Thành công: {$data['successful_shops']}, Lỗi: {$data['failed_shops']}")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Đồng Bộ Thất Bại')
                    ->body(ErrorHandler::getErrorMessage($result))
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi Hệ Thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        } finally {
            $this->syncInProgress = false;
        }
    }

    public function syncByConfiguration(): void
    {
        try {
            $this->syncInProgress = true;

            $data = $this->form->getState();
            $options = [];

            // Set filtering based on sync type
            if ($data['sync_type'] === 'gmv_max') {
                $options['filtering'] = ['ad_creation_eligible' => 'GMV_MAX'];
            } elseif ($data['sync_type'] === 'shopping_ads') {
                $options['filtering'] = ['ad_creation_eligible' => 'CUSTOM_SHOP_ADS'];
            }

            // Set page size
            if (!empty($data['page_size'])) {
                $options['page_size'] = $data['page_size'];
            }

            if (!empty($data['shop_id'])) {
                // Sync specific shop
                $shop = Shop::find($data['shop_id']);
                if ($shop) {
                    $result = $this->getSyncService()->syncShopProducts($shop, $options);
                    $this->handleSyncResult($result, $shop->name);
                }
            } else {
                // Sync all shops
                $result = $this->getSyncService()->syncAllShopsProducts($options);
                $this->handleBatchSyncResult($result);
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi Đồng Bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        } finally {
            $this->syncInProgress = false;
        }
    }

    protected function syncSingleProduct(Product $product): void
    {
        try {
            $shop = $product->shop;
            if (!$shop) {
                Notification::make()
                    ->title('Lỗi')
                    ->body('Không tìm thấy thông tin cửa hàng')
                    ->danger()
                    ->send();
                return;
            }

            $result = $this->getSyncService()->syncShopProducts($shop, [
                'filtering' => [
                    'item_group_ids' => [$product->item_group_id]
                ]
            ]);

            $this->handleSyncResult($result, $product->title);
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi Đồng Bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function syncMultipleProducts($products): void
    {
        try {
            $shopGroups = $products->groupBy('shop_id');
            $totalSynced = 0;
            $totalErrors = 0;

            foreach ($shopGroups as $shopId => $shopProducts) {
                $shop = Shop::find($shopId);
                if (!$shop) continue;

                $itemGroupIds = $shopProducts->pluck('item_group_id')->toArray();

                $result = $this->getSyncService()->syncShopProducts($shop, [
                    'filtering' => [
                        'item_group_ids' => $itemGroupIds
                    ]
                ]);

                if (ErrorHandler::isSuccess($result)) {
                    $totalSynced += ($result['data']['created'] ?? 0) + ($result['data']['updated'] ?? 0);
                } else {
                    $totalErrors++;
                }
            }

            if ($totalErrors === 0) {
                Notification::make()
                    ->title('Đồng Bộ Thành Công')
                    ->body("Đã đồng bộ {$totalSynced} sản phẩm")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Đồng Bộ Hoàn Tất Với Lỗi')
                    ->body("Đồng bộ {$totalSynced} sản phẩm, {$totalErrors} lỗi")
                    ->warning()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi Đồng Bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function handleSyncResult(array $result, string $name): void
    {
        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];
            $message = "Đồng bộ thành công cho {$name}";

            if (isset($data['data_source']) && $data['data_source'] !== 'direct_api') {
                $message .= ' (sử dụng dữ liệu fallback)';
            }

            Notification::make()
                ->title('Đồng Bộ Thành Công')
                ->body($message)
                ->success()
                ->send();
        } else {
            $errorType = ErrorHandler::getErrorType($result);
            $errorMessage = ErrorHandler::getErrorMessage($result);

            if ($errorType === ErrorHandler::PERMISSION_ERROR) {
                Notification::make()
                    ->title('Lỗi Quyền Truy Cập')
                    ->body($errorMessage . ' Vui lòng liên hệ admin để cấp quyền store management.')
                    ->warning()
                    ->send();
            } else {
                Notification::make()
                    ->title('Đồng Bộ Thất Bại')
                    ->body($errorMessage)
                    ->danger()
                    ->send();
            }
        }
    }

    protected function handleBatchSyncResult(array $result): void
    {
        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];
            Notification::make()
                ->title('Đồng Bộ Hoàn Tất')
                ->body("Đã xử lý {$data['total_shops']} cửa hàng. Thành công: {$data['successful_shops']}, Lỗi: {$data['failed_shops']}")
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Đồng Bộ Thất Bại')
                ->body(ErrorHandler::getErrorMessage($result))
                ->danger()
                ->send();
        }
    }
}
