<?php

namespace App\Filament\Pages;

use App\Helpers\ErrorHandler;
use App\Models\ExclusiveAuthorization;
use App\Models\Shop;
use App\Services\TikTok\AuthorizationSyncService;
use App\Services\TikTok\TikTokApiService;
use Exception;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TikTokAuthorizationManagement extends Page implements Tables\Contracts\HasTable
{
    use InteractsWithForms, InteractsWithTable, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static ?string $navigationLabel = 'Quản Lý Ủy Quyền TikTok';
    protected static ?string $title = 'Quản Lý Ủy Quyền TikTok';
    protected static ?string $navigationGroup = 'TikTok API Management';
    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.tiktok-authorization-management';

    public ?array $data = [];
    public bool $operationInProgress = false;

    protected ?AuthorizationSyncService $syncService = null;

    public function mount(): void
    {
        $apiService = app(TikTokApiService::class);
        $this->syncService = new AuthorizationSyncService($apiService);

        $this->form->fill();
    }

    protected function getSyncService(): AuthorizationSyncService
    {
        if (!$this->syncService) {
            $apiService = app(TikTokApiService::class);
            $this->syncService = new AuthorizationSyncService($apiService);
        }

        return $this->syncService;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Cấp Quyền Ủy Quyền Độc Quyền')
                    ->description('Cấp quyền exclusive authorization cho ad account để tạo GMV Max Campaigns')
                    ->schema([
                        Forms\Components\Select::make('shop_id')
                            ->label('Chọn Cửa Hàng')
                            ->options(function () {
                                return Shop::where('status', 'active')
                                    ->whereNotNull('shop_id')
                                    ->whereNotNull('store_authorized_bc_id')
                                    ->pluck('name', 'id');
                            })
                            ->searchable()
                            ->placeholder('Chọn cửa hàng để cấp quyền')
                            ->helperText('Chỉ hiển thị shops có đủ thông tin để cấp quyền'),

                        Forms\Components\TextInput::make('advertiser_id')
                            ->label('Advertiser ID')
                            ->required()
                            ->placeholder('Nhập Advertiser ID')
                            ->helperText('ID của ad account sẽ được cấp quyền độc quyền'),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        return $table
            ->query(ExclusiveAuthorization::with('shop'))
            ->columns([
                Tables\Columns\TextColumn::make('store_id')
                    ->label('Store ID')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('shop.name')
                    ->label('Cửa Hàng')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('advertiser_id')
                    ->label('Advertiser ID')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('advertiser_name')
                    ->label('Advertiser Name')
                    ->searchable()
                    ->placeholder('N/A'),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng Thái Authorization')
                    ->colors([
                        'success' => 'EFFECTIVE',
                        'warning' => 'INEFFECTIVE',
                        'danger' => 'UNAUTHORIZED',
                        'secondary' => fn($state) => in_array($state, ['pending', 'granted', 'expired', 'revoked']),
                    ])
                    ->formatStateUsing(fn($record) => $record->display_status),

                Tables\Columns\BadgeColumn::make('advertiser_status')
                    ->label('Trạng Thái Advertiser')
                    ->colors([
                        'success' => 'STATUS_ENABLE',
                        'warning' => fn($state) => str_contains($state, 'PENDING'),
                        'danger' => fn($state) => str_contains($state, 'FAIL') || str_contains($state, 'DISABLE'),
                    ])
                    ->formatStateUsing(fn($record) => $record->display_advertiser_status)
                    ->placeholder('N/A'),

                Tables\Columns\TextColumn::make('granted_at')
                    ->label('Ngày Cấp')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('N/A'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập Nhật')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng Thái Authorization')
                    ->options([
                        'EFFECTIVE' => 'Có hiệu lực',
                        'INEFFECTIVE' => 'Không hiệu lực',
                        'UNAUTHORIZED' => 'Chưa được ủy quyền',
                        'pending' => 'Chờ xử lý',
                        'granted' => 'Đã cấp',
                        'expired' => 'Đã hết hạn',
                        'revoked' => 'Đã thu hồi',
                    ]),

                Tables\Filters\SelectFilter::make('shop')
                    ->relationship('shop', 'name')
                    ->label('Cửa Hàng'),

                Tables\Filters\Filter::make('effective_only')
                    ->label('Chỉ Authorization Hiệu Lực')
                    ->query(fn(Builder $query) => $query->where('status', 'EFFECTIVE')),
            ])
            ->actions([
                Tables\Actions\Action::make('sync_authorization')
                    ->label('Đồng Bộ')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function (ExclusiveAuthorization $record) {
                        $this->syncSingleAuthorization($record);
                    }),

                Tables\Actions\Action::make('grant_authorization')
                    ->label('Cấp Quyền')
                    ->icon('heroicon-o-key')
                    ->color('success')
                    ->visible(fn(ExclusiveAuthorization $record) => !$record->isEffective())
                    ->form([
                        Forms\Components\TextInput::make('advertiser_id')
                            ->label('Advertiser ID')
                            ->required()
                            ->default(fn(ExclusiveAuthorization $record) => $record->advertiser_id),
                    ])
                    ->action(function (ExclusiveAuthorization $record, array $data) {
                        $this->grantAuthorizationForRecord($record, $data['advertiser_id']);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('sync_selected')
                    ->label('Đồng Bộ Đã Chọn')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function ($records) {
                        $this->syncMultipleAuthorizations($records);
                    })
                    ->requiresConfirmation(),
            ])
            ->defaultSort('updated_at', 'desc')
            ->poll('30s');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('grant_authorization')
                ->label('Cấp Quyền Mới')
                ->icon('heroicon-o-key')
                ->color('success')
                ->form([
                    Forms\Components\Select::make('shop_id')
                        ->label('Cửa Hàng')
                        ->options(function () {
                            // Chỉ hiển thị shops chưa có authorization hoặc không hiệu lực
                            return Shop::where('status', 'active')
                                ->whereNotNull('shop_id')
                                ->whereNotNull('store_authorized_bc_id')
                                ->whereDoesntHave('exclusiveAuthorizations', function ($query) {
                                    $query->where('status', 'EFFECTIVE');
                                })
                                ->pluck('name', 'id');
                        })
                        ->searchable()
                        ->required()
                        ->helperText('Chỉ hiển thị shops chưa có authorization hiệu lực'),

                    Forms\Components\TextInput::make('advertiser_id')
                        ->label('Advertiser ID')
                        ->required()
                        ->placeholder('Nhập Advertiser ID'),
                ])
                ->action('grantNewAuthorization')
                ->modalWidth(MaxWidth::Medium),

            Action::make('batch_grant')
                ->label('Cấp Quyền Hàng Loạt')
                ->icon('heroicon-o-key')
                ->color('warning')
                ->form([
                    Forms\Components\TextInput::make('advertiser_id')
                        ->label('Advertiser ID')
                        ->required()
                        ->placeholder('Nhập Advertiser ID cho tất cả shops'),

                    Forms\Components\Checkbox::make('validate_only')
                        ->label('Chỉ Kiểm Tra Điều Kiện')
                        ->helperText('Chỉ kiểm tra shops có đủ điều kiện mà không thực hiện cấp quyền'),
                ])
                ->action('batchGrantAuthorizations')
                ->modalWidth(MaxWidth::Medium),

            Action::make('sync_all')
                ->label('Đồng Bộ Tất Cả')
                ->icon('heroicon-o-arrow-path')
                ->color('primary')
                ->action('syncAllAuthorizations')
                ->requiresConfirmation(),

            Action::make('view_summary')
                ->label('Thống Kê')
                ->icon('heroicon-o-chart-bar')
                ->color('gray')
                ->action('showSummary'),
        ];
    }

    public function grantNewAuthorization(): void
    {
        try {
            $this->operationInProgress = true;

            $data = $this->form->getState();

            $shop = Shop::find($data['shop_id']);
            if (!$shop) {
                Notification::make()
                    ->title('Lỗi')
                    ->body('Không tìm thấy cửa hàng')
                    ->danger()
                    ->send();
                return;
            }

            $result = $this->getSyncService()->grantExclusiveAuthorization($shop, $data['advertiser_id']);

            if (ErrorHandler::isSuccess($result)) {
                Notification::make()
                    ->title('Cấp Quyền Thành Công')
                    ->body("Đã cấp quyền exclusive authorization cho {$shop->name}")
                    ->success()
                    ->send();
            } else {
                $this->handleError($result);
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Hệ Thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        } finally {
            $this->operationInProgress = false;
        }
    }

    public function batchGrantAuthorizations(array $data): void
    {
        try {
            $this->operationInProgress = true;

            if ($data['validate_only']) {
                $this->validateShopsEligibility();
                return;
            }

            // Find shops needing authorization
            $findResult = $this->getSyncService()->findShopsNeedingAuthorization();

            if (!ErrorHandler::isSuccess($findResult)) {
                $this->handleError($findResult);
                return;
            }

            $shops = $findResult['data']['shops_needing_authorization'];
            $shopIds = $shops->pluck('shop_id')->toArray();

            if (empty($shopIds)) {
                Notification::make()
                    ->title('Thông Báo')
                    ->body('Tất cả shops đã có authorization!')
                    ->info()
                    ->send();
                return;
            }

            $result = $this->getSyncService()->batchGrantAuthorizations($shopIds, $data['advertiser_id']);

            if (ErrorHandler::isSuccess($result)) {
                $resultData = $result['data'];
                Notification::make()
                    ->title('Cấp Quyền Hàng Loạt Hoàn Tất')
                    ->body("Thành công: {$resultData['successful_grants']}, Thất bại: {$resultData['failed_grants']}")
                    ->success()
                    ->send();
            } else {
                $this->handleError($result);
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Hệ Thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        } finally {
            $this->operationInProgress = false;
        }
    }

    public function syncAllAuthorizations(): void
    {
        try {
            $this->operationInProgress = true;

            $result = $this->getSyncService()->syncAllShopsAuthorizations();

            if (ErrorHandler::isSuccess($result)) {
                $data = $result['data'];
                Notification::make()
                    ->title('Đồng Bộ Thành Công')
                    ->body("Đã xử lý {$data['total_shops']} shops. Thành công: {$data['successful_shops']}, Lỗi: {$data['failed_shops']}")
                    ->success()
                    ->send();
            } else {
                $this->handleError($result);
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Hệ Thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        } finally {
            $this->operationInProgress = false;
        }
    }

    public function showSummary(): void
    {
        try {
            $result = $this->getSyncService()->getAuthorizationSummary();

            if (ErrorHandler::isSuccess($result)) {
                $stats = $result['data'];

                $message = "📊 THỐNG KÊ AUTHORIZATION:\n\n";
                $message .= "🔐 Tổng authorizations: {$stats['total_authorizations']}\n";
                $message .= "✅ Có hiệu lực: {$stats['effective_authorizations']}\n";
                $message .= "❌ Không hiệu lực: {$stats['ineffective_authorizations']}\n";
                $message .= "🚫 Chưa ủy quyền: {$stats['unauthorized_shops']}\n";
                $message .= "👤 Advertisers đã duyệt: {$stats['approved_advertisers']}\n";
                $message .= "⏳ Advertisers chờ duyệt: {$stats['pending_advertisers']}";

                Notification::make()
                    ->title('Thống Kê Authorization')
                    ->body($message)
                    ->info()
                    ->persistent()
                    ->send();
            } else {
                $this->handleError($result);
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Hệ Thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function validateShopsEligibility(): void
    {
        try {
            $findResult = $this->getSyncService()->findShopsNeedingAuthorization();

            if (!ErrorHandler::isSuccess($findResult)) {
                $this->handleError($findResult);
                return;
            }

            $shops = $findResult['data']['shops_needing_authorization'];
            $eligible = 0;
            $ineligible = 0;

            foreach ($shops as $shop) {
                $validationResult = $this->getSyncService()->validateShopEligibility($shop);

                if (ErrorHandler::isSuccess($validationResult)) {
                    $eligible++;
                } else {
                    $ineligible++;
                }
            }

            $message = "📋 KẾT QUẢ KIỂM TRA:\n\n";
            $message .= "✅ Shops đủ điều kiện: {$eligible}\n";
            $message .= "❌ Shops không đủ điều kiện: {$ineligible}\n";
            $message .= "📊 Tổng shops cần kiểm tra: " . count($shops);

            Notification::make()
                ->title('Kiểm Tra Điều Kiện Hoàn Tất')
                ->body($message)
                ->info()
                ->persistent()
                ->send();
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Kiểm Tra')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function syncSingleAuthorization(ExclusiveAuthorization $authorization): void
    {
        try {
            $shop = $authorization->shop;
            if (!$shop) {
                Notification::make()
                    ->title('Lỗi')
                    ->body('Không tìm thấy thông tin cửa hàng')
                    ->danger()
                    ->send();
                return;
            }

            $result = $this->getSyncService()->syncShopAuthorization($shop, $authorization->advertiser_id);

            if (ErrorHandler::isSuccess($result)) {
                Notification::make()
                    ->title('Đồng Bộ Thành Công')
                    ->body("Đã đồng bộ authorization cho {$shop->name}")
                    ->success()
                    ->send();
            } else {
                $this->handleError($result);
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Đồng Bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function grantAuthorizationForRecord(ExclusiveAuthorization $record, string $advertiserId): void
    {
        try {
            $shop = $record->shop;
            if (!$shop) {
                Notification::make()
                    ->title('Lỗi')
                    ->body('Không tìm thấy thông tin cửa hàng')
                    ->danger()
                    ->send();
                return;
            }

            $result = $this->getSyncService()->grantExclusiveAuthorization($shop, $advertiserId);

            if (ErrorHandler::isSuccess($result)) {
                Notification::make()
                    ->title('Cấp Quyền Thành Công')
                    ->body("Đã cấp quyền cho {$shop->name}")
                    ->success()
                    ->send();
            } else {
                $this->handleError($result);
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Cấp Quyền')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function syncMultipleAuthorizations($records): void
    {
        try {
            $successful = 0;
            $failed = 0;

            foreach ($records as $record) {
                $shop = $record->shop;
                if (!$shop) {
                    $failed++;
                    continue;
                }

                $result = $this->getSyncService()->syncShopAuthorization($shop, $record->advertiser_id);

                if (ErrorHandler::isSuccess($result)) {
                    $successful++;
                } else {
                    $failed++;
                }
            }

            Notification::make()
                ->title('Đồng Bộ Hàng Loạt Hoàn Tất')
                ->body("Thành công: {$successful}, Thất bại: {$failed}")
                ->success()
                ->send();
        } catch (Exception $e) {
            Notification::make()
                ->title('Lỗi Đồng Bộ')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function handleError(array $result): void
    {
        $errorType = ErrorHandler::getErrorType($result);
        $errorMessage = ErrorHandler::getErrorMessage($result);

        $color = match ($errorType) {
            ErrorHandler::PERMISSION_ERROR, ErrorHandler::VALIDATION_ERROR => 'warning',
            default => 'danger',
        };

        Notification::make()
            ->title(ErrorHandler::getErrorTitle($errorType))
            ->body($errorMessage)
            ->color($color)
            ->send();
    }
}
