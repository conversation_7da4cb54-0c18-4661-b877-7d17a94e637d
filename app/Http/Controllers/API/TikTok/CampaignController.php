<?php

namespace App\Http\Controllers\API\TikTok;

use App\Http\Controllers\Controller;
use App\Services\TikTok\TikTokApiService;
use App\Helpers\ErrorHandler;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * TikTok Campaign API Controller
 * Handles TikTok GMV Max Campaign related API endpoints
 */
class CampaignController extends Controller
{
    private TikTokApiService $tikTokApiService;

    public function __construct(TikTokApiService $tikTokApiService)
    {
        $this->tikTokApiService = $tikTokApiService;
    }

    /**
     * Get GMV Max Campaign details
     * 
     * @param Request $request
     * @param string $campaignId
     * @return JsonResponse
     */
    public function getCampaignInfo(Request $request, string $campaignId): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make(array_merge($request->all(), ['campaign_id' => $campaignId]), [
                'advertiser_id' => 'required|string',
                'campaign_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $advertiserId = $request->input('advertiser_id');

            Log::info('Getting TikTok campaign info', [
                'campaign_id' => $campaignId,
                'advertiser_id' => $advertiserId
            ]);

            // Set advertiser ID for API service
            $this->tikTokApiService->setAdvertiserId($advertiserId);

            // Get campaign info from TikTok API
            $response = $this->tikTokApiService->getCampaignInfo($campaignId);

            // Check if API call was successful
            if (!ErrorHandler::isSuccess($response)) {
                Log::error('TikTok API error when getting campaign info', [
                    'campaign_id' => $campaignId,
                    'advertiser_id' => $advertiserId,
                    'response' => $response
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $response['message'] ?? 'Failed to get campaign info',
                    'error_code' => $response['code'] ?? null
                ], 400);
            }

            // Extract campaign data
            $campaignData = $response['data'] ?? [];

            Log::info('Successfully retrieved campaign info', [
                'campaign_id' => $campaignId,
                'campaign_name' => $campaignData['campaign_name'] ?? 'Unknown'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Campaign info retrieved successfully',
                'data' => $campaignData
            ]);

        } catch (\Exception $e) {
            Log::error('Exception when getting campaign info', [
                'campaign_id' => $campaignId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get multiple campaigns info
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCampaigns(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'advertiser_id' => 'required|string',
                'page' => 'sometimes|integer|min:1',
                'page_size' => 'sometimes|integer|min:1|max:100',
                'gmv_max_promotion_types' => 'sometimes|array',
                'gmv_max_promotion_types.*' => 'in:PRODUCT_GMV_MAX,LIVE_GMV_MAX',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $advertiserId = $request->input('advertiser_id');
            $options = $request->only(['page', 'page_size', 'gmv_max_promotion_types']);

            Log::info('Getting TikTok campaigns list', [
                'advertiser_id' => $advertiserId,
                'options' => $options
            ]);

            // Set advertiser ID for API service
            $this->tikTokApiService->setAdvertiserId($advertiserId);

            // Get campaigns from TikTok API
            $response = $this->tikTokApiService->getCampaigns($options);

            // Check if API call was successful
            if (!ErrorHandler::isSuccess($response)) {
                Log::error('TikTok API error when getting campaigns', [
                    'advertiser_id' => $advertiserId,
                    'response' => $response
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $response['message'] ?? 'Failed to get campaigns',
                    'error_code' => $response['code'] ?? null
                ], 400);
            }

            // Extract campaigns data
            $campaignsData = $response['data'] ?? [];

            Log::info('Successfully retrieved campaigns list', [
                'advertiser_id' => $advertiserId,
                'total_campaigns' => $campaignsData['page_info']['total_number'] ?? 0
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Campaigns retrieved successfully',
                'data' => $campaignsData
            ]);

        } catch (\Exception $e) {
            Log::error('Exception when getting campaigns', [
                'advertiser_id' => $request->input('advertiser_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test TikTok API connection
     * 
     * @return JsonResponse
     */
    public function testConnection(): JsonResponse
    {
        try {
            Log::info('Testing TikTok API connection');

            $response = $this->tikTokApiService->testConnection();

            if (ErrorHandler::isSuccess($response)) {
                return response()->json([
                    'success' => true,
                    'message' => 'TikTok API connection successful',
                    'data' => $response['data'] ?? []
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $response['message'] ?? 'Connection test failed',
                'error_code' => $response['code'] ?? null
            ], 400);

        } catch (\Exception $e) {
            Log::error('Exception when testing TikTok API connection', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
