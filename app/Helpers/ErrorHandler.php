<?php

namespace App\Helpers;

use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

/**
 * Centralized error handling helper for consistent error management
 */
class ErrorHandler
{
    /**
     * Error type constants
     */
    public const CONFIGURATION_ERROR = 'configuration_error';
    public const API_ERROR = 'api_error';
    public const SERVICE_ERROR = 'service_error';
    public const NETWORK_ERROR = 'network_error';
    public const VALIDATION_ERROR = 'validation_error';
    public const AUTHENTICATION_ERROR = 'authentication_error';
    public const RATE_LIMIT_ERROR = 'rate_limit_error';
    public const PERMISSION_ERROR = 'permission_error';

    /**
     * Create standardized error response
     */
    public static function createErrorResponse(
        string      $message,
        string      $errorType = self::API_ERROR,
        array       $context = [],
        ?\Throwable $exception = null
    ): array
    {
        $response = [
            'success' => false,
            'error' => $message,
            'error_type' => $errorType,
        ];

        // Add context if provided
        if (!empty($context)) {
            $response['context'] = $context;
        }

        // Log the error with context
        self::logError($message, $errorType, $context, $exception);

        return $response;
    }

    /**
     * Create success response
     */
    public static function createSuccessResponse(array $data = [], string $message = null): array
    {
        $response = [
            'success' => true,
        ];

        if ($message) {
            $response['message'] = $message;
        }

        return array_merge($response, $data);
    }

    /**
     * Log error with structured context
     */
    public static function logError(
        string      $message,
        string      $errorType,
        array       $context = [],
        ?\Throwable $exception = null
    ): void
    {
        $logContext = [
            'error_type' => $errorType,
            'context' => $context,
        ];

        if ($exception) {
            $logContext['exception'] = [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        Log::error($message, $logContext);
    }

    /**
     * Handle HTTP response errors
     */
    public static function handleHttpError(
        \Illuminate\Http\Client\Response $response,
        string                           $service = 'API',
        array                            $context = []
    ): array
    {
        $statusCode = $response->status();
        $errorBody = $response->body();

        $errorType = match (true) {
            $statusCode === 401 => self::AUTHENTICATION_ERROR,
            $statusCode === 403 => self::AUTHENTICATION_ERROR,
            $statusCode === 429 => self::RATE_LIMIT_ERROR,
            $statusCode >= 500 => self::SERVICE_ERROR,
            default => self::API_ERROR,
        };

        $message = match ($statusCode) {
            401 => "{$service} authentication failed. Please check your credentials.",
            403 => "{$service} access denied. Please verify your permissions.",
            429 => "{$service} rate limit exceeded. Please try again later.",
            500, 502, 503, 504 => "{$service} service temporarily unavailable. Please try again later.",
            default => "{$service} request failed. Please check your configuration and try again.",
        };

        return self::createErrorResponse(
            $message,
            $errorType,
            array_merge($context, [
                'status_code' => $statusCode,
                'response_body' => $errorBody,
            ])
        );
    }

    /**
     * Handle network/connection errors
     */
    public static function handleNetworkError(
        \Throwable $exception,
        string     $service = 'Service',
        array      $context = []
    ): array
    {
        $message = "Network error connecting to {$service}. Please check your internet connection and try again.";

        return self::createErrorResponse(
            $message,
            self::NETWORK_ERROR,
            $context,
            $exception
        );
    }

    /**
     * Handle configuration errors
     */
    public static function handleConfigurationError(
        string $message,
        array  $context = []
    ): array
    {
        return self::createErrorResponse(
            $message,
            self::CONFIGURATION_ERROR,
            $context
        );
    }

    /**
     * Handle validation errors
     */
    public static function handleValidationError(
        string $message,
        array  $context = []
    ): array
    {
        return self::createErrorResponse(
            $message,
            self::VALIDATION_ERROR,
            $context
        );
    }

    /**
     * Send Filament notification based on error type
     */
    public static function sendNotification(array $errorResponse): void
    {
        if ($errorResponse['success']) {
            Notification::make()
                ->title('Success')
                ->body($errorResponse['message'] ?? 'Operation completed successfully')
                ->success()
                ->send();
            return;
        }

        $errorType = $errorResponse['error_type'] ?? self::API_ERROR;
        $message = $errorResponse['error'] ?? 'An error occurred';

        $notification = Notification::make()
            ->title(self::getErrorTitle($errorType))
            ->body($message);

        $color = match ($errorType) {
            self::CONFIGURATION_ERROR => 'warning',
            self::NETWORK_ERROR => 'info',
            self::RATE_LIMIT_ERROR => 'warning',
            self::VALIDATION_ERROR => 'warning',
            self::PERMISSION_ERROR => 'warning',
            default => 'danger',
        };

        $notification->color($color)->send();
    }

    /**
     * Get user-friendly error title
     */
    public static function getErrorTitle(string $errorType): string
    {
        return match ($errorType) {
            self::CONFIGURATION_ERROR => 'Configuration Error',
            self::API_ERROR => 'API Error',
            self::SERVICE_ERROR => 'Service Unavailable',
            self::NETWORK_ERROR => 'Connection Error',
            self::VALIDATION_ERROR => 'Validation Error',
            self::AUTHENTICATION_ERROR => 'Authentication Error',
            self::RATE_LIMIT_ERROR => 'Rate Limit Exceeded',
            self::PERMISSION_ERROR => 'Permission Denied',
            default => 'Error',
        };
    }

    /**
     * Wrap operation with error handling
     */
    public static function wrapOperation(
        callable $operation,
        string   $operationName = 'Operation',
        array    $context = []
    ): array
    {
        try {
            $result = $operation();

            // If operation returns array with success key, return as-is
            if (is_array($result) && isset($result['success'])) {
                return $result;
            }

            // Otherwise, wrap in success response
            return self::createSuccessResponse(
                is_array($result) ? $result : ['data' => $result],
                "{$operationName} completed successfully"
            );

        } catch (\Throwable $exception) {
            return self::handleNetworkError(
                $exception,
                $operationName,
                $context
            );
        }
    }

    /**
     * Check if response indicates success
     */
    public static function isSuccess(array $response): bool
    {
        return $response['success'] ?? false;
    }

    /**
     * Check if response indicates success
     */
    public static function isSuccessResponse(array $response): bool
    {
        return ($response['success'] ?? false) === true;
    }

    /**
     * Extract error message from response
     */
    public static function getErrorMessage(array $response): string
    {
        return $response['error'] ?? 'Unknown error occurred';
    }

    /**
     * Extract error type from response
     */
    public static function getErrorType(array $response): string
    {
        return $response['error_type'] ?? self::API_ERROR;
    }
}
