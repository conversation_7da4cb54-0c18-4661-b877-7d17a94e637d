<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class TikTokSettings extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'tiktok_settings';

    /**
     * Các trường có thể mass assignment
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'is_encrypted',
        'is_public',
        'description',
        'group',
        'validation_rules',
        'default_value',
        'updated_by',
        'last_tested_at',
        'test_result',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu
     */
    protected $casts = [
        'is_encrypted' => 'boolean',
        'is_public' => 'boolean',
        'validation_rules' => 'array',
        'test_result' => 'array',
        'last_tested_at' => 'datetime',
    ];

    /**
     * Relationship: Setting được cập nhật bởi User
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Accessor: Lấy giá trị đã decrypt (nếu cần)
     */
    public function getDecryptedValueAttribute()
    {
        if ($this->is_encrypted && $this->value) {
            try {
                return Crypt::decryptString($this->value);
            } catch (\Exception $e) {
                return null;
            }
        }

        return $this->value;
    }

    /**
     * Mutator: Encrypt giá trị khi lưu (nếu cần)
     */
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    /**
     * Lấy giá trị setting theo type
     */
    public function getTypedValue()
    {
        $value = $this->decrypted_value ?? $this->default_value;

        if ($value === null) {
            return null;
        }

        return match ($this->type) {
            'boolean' => filter_var($value, FILTER_VALIDATE_BOOLEAN),
            'integer' => (int)$value,
            'float' => (float)$value,
            'json' => json_decode($value, true),
            'array' => is_array($value) ? $value : json_decode($value, true),
            default => (string)$value,
        };
    }

    /**
     * Static method: Lấy setting value theo key
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "tiktok_setting_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return $setting->getTypedValue() ?? $default;
        });
    }

    /**
     * Static method: Set setting value
     */
    public static function set(string $key, $value, array $options = []): self
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            array_merge([
                'value' => $value,
                'updated_by' => auth()->id(),
            ], $options)
        );

        // Clear cache
        Cache::forget("tiktok_setting_{$key}");

        return $setting;
    }

    /**
     * Static method: Lấy tất cả settings theo group
     */
    public static function getGroup(string $group): array
    {
        $cacheKey = "tiktok_settings_group_{$group}";

        return Cache::remember($cacheKey, 3600, function () use ($group) {
            return static::where('group', $group)
                ->get()
                ->mapWithKeys(function ($setting) {
                    return [$setting->key => $setting->getTypedValue()];
                })
                ->toArray();
        });
    }

    /**
     * Static method: Clear cache cho một setting hoặc tất cả
     */
    public static function clearCache(string $key = null): void
    {
        if ($key) {
            Cache::forget("tiktok_setting_{$key}");
        } else {
            // Clear all TikTok settings cache
            $groups = static::distinct('group')->pluck('group');
            foreach ($groups as $group) {
                Cache::forget("tiktok_settings_group_{$group}");
            }

            $keys = static::pluck('key');
            foreach ($keys as $settingKey) {
                Cache::forget("tiktok_setting_{$settingKey}");
            }
        }
    }

    /**
     * Test setting value (for API credentials)
     */
    public function test(): array
    {
        // Implementation sẽ depend on setting type
        // Ví dụ: test API connection cho access_token

        $result = [
            'success' => true,
            'message' => 'Setting is valid',
            'tested_at' => now(),
        ];

        $this->update([
            'last_tested_at' => now(),
            'test_result' => $result,
        ]);

        return $result;
    }

    /**
     * Scope: Chỉ lấy public settings
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope: Lấy theo group
     */
    public function scopeGroup($query, string $group)
    {
        return $query->where('group', $group);
    }
}
