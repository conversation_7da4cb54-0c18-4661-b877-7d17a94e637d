<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Model Product - Quản lý sản phẩm của shop
 *
 * @property string $product_id Product ID từ TikTok API (legacy)
 * @property string $item_group_id SPU ID từ TikTok API
 * @property string $store_id TikTok Store ID
 * @property int $shop_id ID cửa hàng local
 * @property string $name Tên sản phẩm (legacy)
 * @property string $title Tiêu đề sản phẩm từ API
 * @property string $product_image_url URL hình ảnh sản phẩm
 * @property float $price Gi<PERSON> sản phẩm (legacy)
 * @property float $min_price Giá tối thiểu
 * @property float $max_price Giá tối đa
 * @property string $currency Mã tiền tệ
 * @property string $status Trạng thái sản phẩm (legacy)
 * @property string $api_status Trạng thái từ TikTok API
 * @property string $category Danh mục sản phẩm
 * @property int $historical_sales Số lượng bán lịch sử
 * @property bool $is_occupied Đã được sử dụng trong quảng cáo (legacy)
 * @property string $gmv_max_ads_status Trạng thái trong GMV Max Campaign
 * @property bool $is_running_custom_shop_ads Đang chạy Shopping Ads
 * @property string $catalog_id ID catalog (deprecated)
 */
class Product extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'shop_products';

    /**
     * Các trường có thể mass assignment theo TikTok API specification
     */
    protected $fillable = [
        // Legacy fields
        'product_id',
        'name',
        'price',
        'status',
        'is_occupied',

        // TikTok API fields
        'item_group_id',
        'store_id',
        'title',
        'product_image_url',
        'min_price',
        'max_price',
        'currency',
        'category',
        'historical_sales',
        'api_status',
        'gmv_max_ads_status',
        'is_running_custom_shop_ads',
        'catalog_id',

        // Relationships
        'shop_id',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu
     */
    protected $casts = [
        // Legacy fields
        'price' => 'decimal:2',
        'is_occupied' => 'boolean',

        // TikTok API fields
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',
        'historical_sales' => 'integer',
        'is_running_custom_shop_ads' => 'boolean',

        // Timestamps
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: Product thuộc về một Shop
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Chỉ lấy products active
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Lọc products available (chưa occupied)
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_occupied', false)->where('status', 'active');
    }

    /**
     * Scope: Lọc products available cho GMV Max
     */
    public function scopeAvailableForGmvMax($query)
    {
        return $query->where('api_status', 'AVAILABLE')
                    ->where('gmv_max_ads_status', 'UNOCCUPIED');
    }

    /**
     * Scope: Lọc products eligible cho Shopping Ads
     */
    public function scopeEligibleForShoppingAds($query)
    {
        return $query->where('api_status', 'AVAILABLE')
                    ->where('is_running_custom_shop_ads', false);
    }

    /**
     * Scope: Lọc theo category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope: Lọc theo khoảng giá (min_price)
     */
    public function scopeByPriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('min_price', [$minPrice, $maxPrice]);
    }

    /**
     * Scope: Lọc theo historical sales
     */
    public function scopeByHistoricalSales($query, $minSales = 0)
    {
        return $query->where('historical_sales', '>=', $minSales);
    }

    /**
     * Scope: Lọc theo store_id
     */
    public function scopeByStoreId($query, $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    /**
     * Business method: Đánh dấu product là occupied
     */
    public function markAsOccupied(): bool
    {
        return $this->update(['is_occupied' => true]);
    }

    /**
     * Business method: Đánh dấu product là available
     */
    public function markAsAvailable(): bool
    {
        return $this->update(['is_occupied' => false]);
    }

    /**
     * Business method: Kiểm tra product có thể sử dụng cho ads không
     */
    public function canBeUsedForAds(): bool
    {
        return $this->status === 'active' && !$this->is_occupied;
    }

    /**
     * Business method: Kiểm tra product có thể sử dụng cho GMV Max không
     */
    public function canBeUsedForGmvMax(): bool
    {
        return $this->api_status === 'AVAILABLE' && $this->gmv_max_ads_status === 'UNOCCUPIED';
    }

    /**
     * Business method: Kiểm tra product có thể sử dụng cho Shopping Ads không
     */
    public function canBeUsedForShoppingAds(): bool
    {
        return $this->api_status === 'AVAILABLE' && !$this->is_running_custom_shop_ads;
    }

    /**
     * Business method: Đánh dấu product là occupied trong GMV Max
     */
    public function markAsGmvMaxOccupied(): bool
    {
        return $this->update(['gmv_max_ads_status' => 'OCCUPIED']);
    }

    /**
     * Business method: Đánh dấu product là unoccupied trong GMV Max
     */
    public function markAsGmvMaxUnoccupied(): bool
    {
        return $this->update(['gmv_max_ads_status' => 'UNOCCUPIED']);
    }

    /**
     * Accessor: Format giá tiền (legacy)
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price, 0, ',', '.') . ' ₫';
    }

    /**
     * Accessor: Format min price
     */
    public function getFormattedMinPriceAttribute(): string
    {
        if (!$this->min_price) return 'N/A';
        return number_format($this->min_price, 2) . ' ' . ($this->currency ?? '');
    }

    /**
     * Accessor: Format max price
     */
    public function getFormattedMaxPriceAttribute(): string
    {
        if (!$this->max_price) return 'N/A';
        return number_format($this->max_price, 2) . ' ' . ($this->currency ?? '');
    }

    /**
     * Accessor: Format price range
     */
    public function getFormattedPriceRangeAttribute(): string
    {
        if (!$this->min_price || !$this->max_price) return 'N/A';

        if ($this->min_price == $this->max_price) {
            return $this->formatted_min_price;
        }

        return $this->formatted_min_price . ' - ' . $this->formatted_max_price;
    }
}
