<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Model CampaignReport - Báo cáo hiệu suất chiến dịch
 *
 * @property string $report_id Report ID từ TikTok API
 * @property string $campaign_id TikTok Campaign ID
 * @property Carbon $report_date Ngày báo cáo
 * @property float $total_cost Chi phí tổng
 * @property int $orders_count Số đơn hàng
 * @property float $gross_revenue Doanh thu tổng
 * @property float $cost_per_order Chi phí trên mỗi đơn hàng
 * @property float $roi Return on Investment (%)
 * @property int $impressions Số lượt hiển thị
 * @property int $clicks Số lượt click
 * @property float $ctr Click-through rate (%)
 * @property float $conversion_rate Tỷ lệ chuyển đổi (%)
 */
class CampaignReport extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'campaign_reports';

    /**
     * <PERSON><PERSON><PERSON> trường có thể mass assignment theo JSON specification
     */
    protected $fillable = [
        'report_id',
        'campaign_id',
        'report_date',
        'total_cost',
        'orders_count',
        'gross_revenue',
        'cost_per_order',
        'roi',
        'impressions',
        'clicks',
        'ctr',
        'conversion_rate',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu theo JSON specification
     */
    protected $casts = [
        'total_cost' => 'decimal:2',
        'gross_revenue' => 'decimal:2',
        'cost_per_order' => 'decimal:2',
        'roi' => 'decimal:2',
        'ctr' => 'decimal:2',
        'conversion_rate' => 'decimal:2',
        'report_date' => 'date',
        'orders_count' => 'integer',
        'impressions' => 'integer',
        'clicks' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: CampaignReport thuộc về một Campaign
     * Map campaign_id (TikTok Campaign ID) với campaign_id field trong gmv_max_campaigns
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id', 'campaign_id');
    }

    /**
     * Scope: Lọc theo khoảng thời gian
     */
    public function scopeByDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('report_date', [$startDate, $endDate]);
    }

    /**
     * Scope: Lọc theo campaign
     */
    public function scopeByCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    /**
     * Scope: Lọc reports có ROI cao
     */
    public function scopeHighRoi($query, $minRoi = 100)
    {
        return $query->where('roi', '>=', $minRoi);
    }

    /**
     * Scope: Lọc reports có performance tốt
     */
    public function scopeGoodPerformance($query)
    {
        return $query->where('roi', '>', 0)
                    ->where('conversion_rate', '>', 1)
                    ->where('orders_count', '>', 0);
    }

    /**
     * Scope: Lọc theo tháng
     */
    public function scopeByMonth($query, int $year, int $month)
    {
        return $query->whereYear('report_date', $year)
                    ->whereMonth('report_date', $month);
    }

    /**
     * Scope: Lọc reports gần đây
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('report_date', '>=', now()->subDays($days));
    }

    /**
     * Accessor: Tính profit (lợi nhuận)
     */
    public function getProfitAttribute(): float
    {
        return $this->gross_revenue - $this->total_cost;
    }

    /**
     * Accessor: Kiểm tra có profitable không
     */
    public function getIsProfitableAttribute(): bool
    {
        return $this->profit > 0;
    }

    /**
     * Accessor: Tính ROAS (Return on Ad Spend)
     */
    public function getRoasAttribute(): float
    {
        if ($this->total_cost == 0) {
            return 0;
        }

        return ($this->gross_revenue / $this->total_cost) * 100;
    }

    /**
     * Accessor: Format chi phí
     */
    public function getFormattedCostAttribute(): string
    {
        return number_format($this->total_cost, 0, ',', '.') . ' ₫';
    }

    /**
     * Accessor: Format doanh thu
     */
    public function getFormattedRevenueAttribute(): string
    {
        return number_format($this->gross_revenue, 0, ',', '.') . ' ₫';
    }

    /**
     * Accessor: Format profit
     */
    public function getFormattedProfitAttribute(): string
    {
        $profit = $this->profit;
        $color = $profit >= 0 ? 'green' : 'red';
        $sign = $profit >= 0 ? '+' : '';

        return $sign . number_format($profit, 0, ',', '.') . ' ₫';
    }

    /**
     * Analytics method: Tính average metrics cho một campaign
     */
    public static function getAverageMetricsForCampaign(int $campaignId): array
    {
        $reports = self::where('campaign_id', $campaignId);

        return [
            'avg_roi' => $reports->avg('roi'),
            'avg_ctr' => $reports->avg('ctr'),
            'avg_conversion_rate' => $reports->avg('conversion_rate'),
            'avg_cost_per_order' => $reports->avg('cost_per_order'),
            'total_cost' => $reports->sum('total_cost'),
            'total_revenue' => $reports->sum('gross_revenue'),
            'total_orders' => $reports->sum('orders_count'),
        ];
    }

    /**
     * Analytics method: Lấy top performing dates
     */
    public static function getTopPerformingDates(int $limit = 10): CampaignReport
    {
        return self::orderByDesc('roi')
                  ->orderByDesc('gross_revenue')
                  ->limit($limit)
                  ->get();
    }

    /**
     * Analytics method: Tính growth rate so với ngày trước
     */
    public function getGrowthRateAttribute(): ?float
    {
        $previousReport = self::where('campaign_id', $this->campaign_id)
                             ->where('report_date', '<', $this->report_date)
                             ->orderByDesc('report_date')
                             ->first();

        if (!$previousReport || $previousReport->gross_revenue == 0) {
            return null;
        }

        return (($this->gross_revenue - $previousReport->gross_revenue) / $previousReport->gross_revenue) * 100;
    }

    /**
     * Business method: Kiểm tra performance có tốt không
     */
    public function hasGoodPerformance(): bool
    {
        return $this->roi > 100
               && $this->conversion_rate > 2
               && $this->orders_count > 0;
    }

    /**
     * Business method: Kiểm tra cần optimization không
     */
    public function needsOptimization(): bool
    {
        return $this->roi < 50
               || $this->ctr < 1
               || $this->conversion_rate < 1;
    }

    /**
     * Business method: Tính performance score (0-100)
     */
    public function getPerformanceScore(): int
    {
        $roiScore = min(($this->roi / 200) * 40, 40); // Max 40 points
        $ctrScore = min(($this->ctr / 5) * 30, 30);   // Max 30 points
        $conversionScore = min(($this->conversion_rate / 10) * 30, 30); // Max 30 points

        return (int) ($roiScore + $ctrScore + $conversionScore);
    }
}
