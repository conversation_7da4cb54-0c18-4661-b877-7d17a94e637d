<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class AiAnalysis extends Model
{
    use HasFactory;

    protected $fillable = [
        'campaign_id',
        'analysis_type',
        'ai_result',
        'confidence_score',
        'is_current',
        'analyzed_at',
    ];

    protected $casts = [
        'ai_result' => 'array',
        'confidence_score' => 'decimal:2',
        'is_current' => 'boolean',
        'analyzed_at' => 'datetime',
    ];

    /**
     * Analysis types
     */
    public const TYPE_RECOMMENDATIONS = 'recommendations';
    public const TYPE_ROI_PREDICTION = 'roi_prediction';
    public const TYPE_BUDGET_OPTIMIZATION = 'budget_optimization';

    public static function getAnalysisTypes(): array
    {
        return [
            self::TYPE_RECOMMENDATIONS,
            self::TYPE_ROI_PREDICTION,
            self::TYPE_BUDGET_OPTIMIZATION,
        ];
    }

    /**
     * Relationships
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Scopes
     */
    public function scopeCurrent(Builder $query): Builder
    {
        return $query->where('is_current', true);
    }

    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('analysis_type', $type);
    }

    public function scopeForCampaign(Builder $query, int $campaignId): Builder
    {
        return $query->where('campaign_id', $campaignId);
    }

    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('analyzed_at', '>=', Carbon::now()->subHours($hours));
    }

    /**
     * Helper methods
     */
    public function isStale(int $hours = 24): bool
    {
        return $this->analyzed_at->lt(Carbon::now()->subHours($hours));
    }

    public function getRecommendationsAttribute(): array
    {
        return $this->ai_result['recommendations'] ?? [];
    }

    public function getPriorityActionsAttribute(): array
    {
        return $this->ai_result['priority_actions'] ?? [];
    }

    public function getPerformanceInsightsAttribute(): array
    {
        return $this->ai_result['performance_insights'] ?? [];
    }

    public function getFormattedRecommendationsAttribute(): string
    {
        $recommendations = $this->getRecommendationsAttribute();

        if (empty($recommendations)) {
            return 'No recommendations available';
        }

        // Take first 2 recommendations and format them
        $formatted = array_slice($recommendations, 0, 2);
        $formatted = array_map(static function ($rec) {
            return is_array($rec) ? ($rec['text'] ?? $rec['recommendation'] ?? 'Recommendation') : (string)$rec;
        }, $formatted);

        return implode(' • ', $formatted);
    }

    /**
     * Create or update analysis for a campaign
     */
    public static function createOrUpdateAnalysis(
        int    $campaignId,
        string $analysisType,
        array  $aiResult,
        ?float $confidenceScore = null
    ): self
    {
        // Use database transaction to ensure consistency
        return DB::transaction(function () use ($campaignId, $analysisType, $aiResult, $confidenceScore) {
            // Mark existing current analysis as not current
            self::where('campaign_id', $campaignId)
                ->where('analysis_type', $analysisType)
                ->where('is_current', true)
                ->update(['is_current' => false]);

            // Create new current analysis
            return self::create([
                'campaign_id' => $campaignId,
                'analysis_type' => $analysisType,
                'ai_result' => $aiResult,
                'confidence_score' => $confidenceScore,
                'is_current' => true,
                'analyzed_at' => Carbon::now(),
            ]);
        });
    }

    /**
     * Get current analysis for a campaign and type
     */
    public static function getCurrentAnalysis(int $campaignId, string $analysisType): ?self
    {
        return self::forCampaign($campaignId)
            ->ofType($analysisType)
            ->current()
            ->first();
    }
}
