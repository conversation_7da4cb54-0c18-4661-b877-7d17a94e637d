<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Model ExclusiveAuthorization - Quản lý quyền ủy quyền độc quyền
 *
 * @property string $authorization_id Authorization ID từ TikTok API
 * @property string $store_id TikTok Store ID
 * @property string $store_authorized_bc_id Business Center ID authorized for store
 * @property int $shop_id ID cửa hàng local
 * @property string $advertiser_id Advertiser ID
 * @property string $advertiser_name Advertiser account name
 * @property string $advertiser_status Advertiser account status
 * @property string $identity_id Official TikTok account identity ID
 * @property string $status Authorization status
 * @property Carbon $granted_at Ngày cấp quyền
 * @property Carbon $expires_at Ngày hết hạn
 */
class ExclusiveAuthorization extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'exclusive_authorizations';

    /**
     * <PERSON><PERSON>c trường có thể mass assignment theo TikTok API v1.3 specification
     */
    protected $fillable = [
        'authorization_id',
        'store_id',
        'store_authorized_bc_id',
        'shop_id',
        'advertiser_id',
        'advertiser_name',
        'advertiser_status',
        'identity_id',
        'status',
        'granted_at',
        'expires_at',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu theo JSON specification
     */
    protected $casts = [
        'granted_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: ExclusiveAuthorization thuộc về một Shop
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Chỉ lấy authorizations granted
     */
    public function scopeGranted($query)
    {
        return $query->where('status', 'granted');
    }

    /**
     * Scope: Lọc authorizations active (granted và chưa hết hạn)
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'granted')
                    ->where(function($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope: Lọc authorizations expired
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'granted')
                    ->where('expires_at', '<=', now());
    }

    /**
     * Scope: Lọc theo advertiser
     */
    public function scopeByAdvertiser($query, $advertiserId)
    {
        return $query->where('advertiser_id', $advertiserId);
    }

    /**
     * Scope: Lọc authorizations sắp hết hạn (trong X ngày)
     */
    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('status', 'granted')
                    ->whereBetween('expires_at', [now(), now()->addDays($days)]);
    }

    /**
     * Accessor: Kiểm tra authorization có active không
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'granted'
               && ($this->expires_at === null || $this->expires_at > now());
    }

    /**
     * Accessor: Kiểm tra authorization có expired không
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->status === 'granted'
               && $this->expires_at !== null
               && $this->expires_at <= now();
    }

    /**
     * Accessor: Tính số ngày còn lại
     */
    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->expires_at || $this->status !== 'granted') {
            return null;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }

    /**
     * Accessor: Lấy trạng thái hiển thị
     */
    public function getDisplayStatusAttribute(): string
    {
        if ($this->is_expired) {
            return 'Đã hết hạn';
        }

        return match($this->status) {
            'pending' => 'Chờ xử lý',
            'granted' => 'Đã cấp',
            'expired' => 'Đã hết hạn',
            'revoked' => 'Đã thu hồi',
            // TikTok API v1.3 status values
            'EFFECTIVE' => 'Có hiệu lực',
            'INEFFECTIVE' => 'Không hiệu lực',
            'UNAUTHORIZED' => 'Chưa được ủy quyền',
            default => 'Không xác định'
        };
    }

    /**
     * Accessor: Lấy trạng thái advertiser hiển thị
     */
    public function getDisplayAdvertiserStatusAttribute(): string
    {
        if (!$this->advertiser_status) {
            return 'N/A';
        }

        return match($this->advertiser_status) {
            'STATUS_ENABLE' => 'Đã phê duyệt',
            'STATUS_CONFIRM_FAIL' => 'Không được phê duyệt',
            'STATUS_PENDING_CONFIRM' => 'Đang xem xét',
            'STATUS_LIMIT' => 'Bị tạm ngưng',
            'STATUS_CONTRACT_PENDING' => 'Hợp đồng chưa có hiệu lực',
            'STATUS_DISABLE' => 'Bị vô hiệu hóa',
            'STATUS_PENDING_CONFIRM_MODIFY' => 'Thay đổi đang chờ xem xét',
            'STATUS_PENDING_VERIFIED' => 'Đang chờ xác minh',
            'STATUS_SELF_SERVICE_UNAUDITED' => 'Chờ xác minh tài khoản tự phục vụ',
            'STATUS_WAIT_FOR_BPM_AUDIT' => 'Chờ xem xét hệ thống CRM',
            'STATUS_CONFIRM_FAIL_END' => 'Xem xét hệ thống CRM thất bại',
            'STATUS_CONFIRM_MODIFY_FAIL' => 'Xem xét thay đổi thất bại',
            default => $this->advertiser_status
        };
    }

    /**
     * Check if authorization is effective (TikTok API v1.3)
     */
    public function isEffective(): bool
    {
        return $this->status === 'EFFECTIVE';
    }

    /**
     * Check if authorization is ineffective (TikTok API v1.3)
     */
    public function isIneffective(): bool
    {
        return $this->status === 'INEFFECTIVE';
    }

    /**
     * Check if authorization is unauthorized (TikTok API v1.3)
     */
    public function isUnauthorized(): bool
    {
        return $this->status === 'UNAUTHORIZED';
    }

    /**
     * Check if advertiser is approved
     */
    public function isAdvertiserApproved(): bool
    {
        return $this->advertiser_status === 'STATUS_ENABLE';
    }

    /**
     * Business method: Cấp quyền ủy quyền
     */
    public function grant(?Carbon $expiresAt = null): bool
    {
        return $this->update([
            'status' => 'granted',
            'granted_at' => now(),
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Business method: Thu hồi quyền ủy quyền
     */
    public function revoke(): bool
    {
        return $this->update(['status' => 'revoked']);
    }

    /**
     * Business method: Gia hạn authorization
     */
    public function extend(Carbon $newExpiresAt): bool
    {
        if ($this->status !== 'granted') {
            return false;
        }

        return $this->update(['expires_at' => $newExpiresAt]);
    }

    /**
     * Business method: Kiểm tra có thể gia hạn không
     */
    public function canBeExtended(): bool
    {
        return $this->status === 'granted' && !$this->is_expired;
    }

    /**
     * Business method: Kiểm tra có thể thu hồi không
     */
    public function canBeRevoked(): bool
    {
        return in_array($this->status, ['pending', 'granted']);
    }

    /**
     * Boot method để handle events
     */
    protected static function boot()
    {
        parent::boot();

        // Tự động update status thành expired khi hết hạn
        static::updating(function ($authorization) {
            if ($authorization->status === 'granted'
                && $authorization->expires_at
                && $authorization->expires_at <= now()) {
                $authorization->status = 'expired';
            }
        });
    }
}
