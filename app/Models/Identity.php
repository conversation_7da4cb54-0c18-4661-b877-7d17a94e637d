<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Model Identity - Quản lý identity cho GMV Max
 *
 * @property string $identity_id Identity ID từ TikTok API
 * @property string $name Tên identity
 * @property string $type Loại identity
 * @property string $status Trạng thái identity
 * @property string $advertiser_id Advertiser ID liên kết
 */
class Identity extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'gmv_max_identities';

    /**
     * Các trường có thể mass assignment theo JSON specification
     */
    protected $fillable = [
        'identity_id',
        'name',
        'type',
        'status',
        'advertiser_id',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Validation rules cho Identity
     */
    public static function validationRules(): array
    {
        return [
            'identity_id' => 'required|string|unique:gmv_max_identities,identity_id',
            'name' => 'required|string|max:255',
            'type' => 'required|in:creator,brand,agency,other',
            'status' => 'required|in:active,inactive,pending,suspended',
            'advertiser_id' => 'required|string|max:255',
        ];
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Lọc theo loại identity
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope: Chỉ lấy identities active
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Accessor: Lấy tên hiển thị với type
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . ucfirst($this->type) . ')';
    }

    /**
     * Business method: Kiểm tra identity có active không
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Business method: Kiểm tra identity có thể sử dụng cho campaigns không
     */
    public function canBeUsedForCampaigns(): bool
    {
        return in_array($this->status, ['active', 'pending']);
    }
}
