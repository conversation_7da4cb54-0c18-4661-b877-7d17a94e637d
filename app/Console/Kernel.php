<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // AI Performance Alerts - Run every hour during business hours
        $schedule->command('ai:performance-alerts')
            ->hourly()
            ->between('8:00', '20:00')
            ->withoutOverlapping()
            ->runInBackground();

        // TikTok Data Sync - Run every 4 hours
        $schedule->command('tiktok:sync-advertisers')
            ->everyFourHours()
            ->withoutOverlapping()
            ->runInBackground();

        // AI Campaign Analysis - Run every hour
        $schedule->command('ai:analyze-campaigns --type=all')
            ->hourly()
            ->withoutOverlapping()
            ->runInBackground();

        // Video Sync - Run every 6 hours for all active campaigns
        $schedule->command('tiktok:sync-videos --all-active --queue --no-notify')
            ->everySixHours()
            ->withoutOverlapping()
            ->runInBackground()
            ->appendOutputTo(storage_path('logs/scheduled-video-sync.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
