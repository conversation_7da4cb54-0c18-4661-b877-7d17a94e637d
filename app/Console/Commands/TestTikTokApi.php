<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TikTok\Examples\TikTokApiExamples;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;

class TestTikTokApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:test {action=all : Action to test (all|connection|campaigns|shops|sync)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test TikTok API integration and functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        $examples = new TikTokApiExamples();

        $this->info("🚀 Testing TikTok API Integration...\n");

        try {
            switch ($action) {
                case 'connection':
                    $this->testConnection($examples);
                    break;
                case 'campaigns':
                    $this->testCampaigns($examples);
                    break;
                case 'shops':
                    $this->testShops($examples);
                    break;
                case 'sync':
                    $this->testSync($examples);
                    break;
                case 'all':
                default:
                    $this->testConnection($examples);
                    $this->testShops($examples);
                    $this->testCampaigns($examples);
                    $this->testSync($examples);
                    break;
            }

            $this->info("\n✅ TikTok API testing completed successfully!");

        } catch (\Exception $e) {
            $this->error("\n❌ TikTok API testing failed: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    protected function testConnection(TikTokApiExamples $examples): void
    {
        $this->info("🔗 Testing API Connection...");

        $isConnected = $examples->testApiConnection();

        if ($isConnected) {
            $this->info("✅ API Connection: SUCCESS");
        } else {
            $this->error("❌ API Connection: FAILED");
        }

        $rateLimitStatus = $examples->getRateLimitStatus();
        $this->info("📊 Rate Limit Status: {$rateLimitStatus['remaining']} requests remaining");
    }

    protected function testShops(TikTokApiExamples $examples): void
    {
        $this->info("\n🏪 Testing Shops API...");

        try {
            $shops = $examples->getAvailableShops();
            $this->info("✅ Shops Retrieved: " . count($shops) . " GMV Max available shops");

            if (!empty($shops)) {
                $this->table(
                    ['Shop ID', 'Shop Name', 'GMV Max Available'],
                    array_slice(array_map(function($shop) {
                        return [
                            $shop['shop_id'] ?? 'N/A',
                            $shop['shop_name'] ?? 'N/A',
                            ($shop['is_gmv_max_available'] ?? false) ? 'Yes' : 'No'
                        ];
                    }, $shops), 0, 5)
                );
            }
        } catch (\Exception $e) {
            $this->error("❌ Shops API: " . $e->getMessage());
        }
    }

    protected function testCampaigns(TikTokApiExamples $examples): void
    {
        $this->info("\n📢 Testing Campaigns API...");

        try {
            // Test Product GMV Max campaigns
            $productCampaigns = $examples->getProductGmvMaxCampaigns();
            $productCount = $productCampaigns['page_info']['total_number'] ?? 0;
            $this->info("✅ Product GMV Max Campaigns: {$productCount} found");

            // Test Live GMV Max campaigns
            $liveCampaigns = $examples->getLiveGmvMaxCampaigns();
            $liveCount = $liveCampaigns['page_info']['total_number'] ?? 0;
            $this->info("✅ Live GMV Max Campaigns: {$liveCount} found");

            // Show campaign details if any exist
            if (!empty($productCampaigns['list'])) {
                $this->table(
                    ['Campaign ID', 'Campaign Name', 'Status', 'Created'],
                    array_slice(array_map(function($campaign) {
                        return [
                            $campaign['campaign_id'] ?? 'N/A',
                            $campaign['campaign_name'] ?? 'N/A',
                            $campaign['operation_status'] ?? 'N/A',
                            $campaign['create_time'] ?? 'N/A'
                        ];
                    }, $productCampaigns['list']), 0, 5)
                );
            }

        } catch (\Exception $e) {
            $this->error("❌ Campaigns API: " . $e->getMessage());
        }
    }

    protected function testSync(TikTokApiExamples $examples): void
    {
        $this->info("\n🔄 Testing Data Synchronization...");

        if ($this->confirm('Do you want to perform a full data sync? This will update your local database.')) {
            try {
                $results = $examples->syncAllDataFromTikTok();

                $this->info("✅ Data Sync Completed:");
                $this->table(
                    ['Data Type', 'Synced', 'Created', 'Updated'],
                    [
                        ['Shops', $results['shops']['synced'] ?? 0, $results['shops']['created'] ?? 0, $results['shops']['updated'] ?? 0],
                        ['Campaigns', $results['campaigns']['synced'] ?? 0, $results['campaigns']['created'] ?? 0, $results['campaigns']['updated'] ?? 0],
                        ['Identities', $results['identities']['synced'] ?? 0, 'N/A', 'N/A'],
                        ['Authorizations', $results['exclusive_authorizations']['synced'] ?? 0, 'N/A', 'N/A'],
                        ['Reports', $results['reports']['synced'] ?? 0, 'N/A', 'N/A'],
                    ]
                );

            } catch (\Exception $e) {
                $this->error("❌ Data Sync: " . $e->getMessage());
            }
        } else {
            $this->info("⏭️  Data sync skipped");
        }
    }
}
