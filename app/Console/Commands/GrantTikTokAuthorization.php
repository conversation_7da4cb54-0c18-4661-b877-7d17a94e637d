<?php

namespace App\Console\Commands;

use App\Models\Shop;
use App\Services\TikTok\AuthorizationSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Helpers\ErrorHandler;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GrantTikTokAuthorization extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:grant-authorization
                            {--shop-id= : Grant authorization for specific shop ID}
                            {--advertiser-id= : Advertiser ID to grant authorization to}
                            {--batch : Grant authorization for multiple shops}
                            {--validate-only : Only validate shop eligibility without granting}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cấp quyền exclusive authorization cho TikTok Shop';

    protected AuthorizationSyncService $syncService;

    public function __construct()
    {
        parent::__construct();

        $apiService = app(TikTokApiService::class);
        $this->syncService = new AuthorizationSyncService($apiService);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔐 TikTok Exclusive Authorization Grant Tool');

        try {
            if ($this->option('validate-only')) {
                return $this->validateShops();
            }

            if ($this->option('batch')) {
                return $this->batchGrantAuthorizations();
            }

            if ($shopId = $this->option('shop-id')) {
                return $this->grantSingleShopAuthorization($shopId);
            } else {
                return $this->interactiveMode();
            }
        } catch (\Exception $e) {
            $this->error('❌ Lỗi: ' . $e->getMessage());
            Log::error('Grant Authorization Command Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Grant authorization cho một shop cụ thể
     */
    protected function grantSingleShopAuthorization(string $shopId): int
    {
        $shop = Shop::where('shop_id', $shopId)->first();

        if (!$shop) {
            $this->error("❌ Không tìm thấy shop với ID: {$shopId}");
            return Command::FAILURE;
        }

        $this->info("🏪 Shop: {$shop->name} ({$shop->shop_id})");

        // Validate shop eligibility
        $validationResult = $this->syncService->validateShopEligibility($shop);
        if (!ErrorHandler::isSuccess($validationResult)) {
            $this->error("❌ Shop không đủ điều kiện:");
            $issues = $validationResult['context']['issues'] ?? [];
            foreach ($issues as $issue) {
                $this->line("   - {$issue}");
            }
            return Command::FAILURE;
        }

        $this->info("✅ Shop đủ điều kiện cho GMV Max authorization");

        // Get advertiser ID
        $advertiserId = $this->getAdvertiserId();
        if (!$advertiserId) {
            return Command::FAILURE;
        }

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirm("Bạn có chắc chắn muốn cấp quyền exclusive authorization cho shop {$shop->name}?")) {
                $this->info("Hủy bỏ thao tác.");
                return Command::SUCCESS;
            }
        }

        // Grant authorization
        $this->info("🔐 Đang cấp quyền authorization...");
        $result = $this->syncService->grantExclusiveAuthorization($shop, $advertiserId);

        return $this->handleGrantResult($result, $shop->name);
    }

    /**
     * Batch grant authorizations
     */
    protected function batchGrantAuthorizations(): int
    {
        $this->info("📋 Batch Grant Authorizations");

        // Find shops needing authorization
        $findResult = $this->syncService->findShopsNeedingAuthorization();
        if (!ErrorHandler::isSuccess($findResult)) {
            $this->error("❌ Không thể tìm shops: " . ErrorHandler::getErrorMessage($findResult));
            return Command::FAILURE;
        }

        $data = $findResult['data'] ?? $findResult;
        $shops = $data['shops_needing_authorization'] ?? [];
        $count = $data['count'] ?? count($shops);

        if ($count === 0) {
            $this->info("✅ Tất cả shops đã có authorization!");
            return Command::SUCCESS;
        }

        $this->info("🔍 Tìm thấy {$count} shops cần authorization:");
        foreach ($shops as $shop) {
            $this->line("   - {$shop->shop_id}: {$shop->name}");
        }

        // Get advertiser ID
        $advertiserId = $this->getAdvertiserId();
        if (!$advertiserId) {
            return Command::FAILURE;
        }

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirm("Bạn có muốn cấp quyền cho tất cả {$count} shops này?")) {
                $this->info("Hủy bỏ thao tác.");
                return Command::SUCCESS;
            }
        }

        // Batch grant
        $shopIds = $shops->pluck('shop_id')->toArray();
        $this->info("🔐 Đang cấp quyền cho {$count} shops...");

        $progressBar = $this->output->createProgressBar($count);
        $progressBar->start();

        $result = $this->syncService->batchGrantAuthorizations($shopIds, $advertiserId);

        $progressBar->finish();
        $this->line('');

        return $this->handleBatchGrantResult($result);
    }

    /**
     * Interactive mode để chọn shop
     */
    protected function interactiveMode(): int
    {
        $this->info("🔍 Tìm shops cần authorization...");

        $findResult = $this->syncService->findShopsNeedingAuthorization();
        if (!ErrorHandler::isSuccess($findResult)) {
            $this->error("❌ Không thể tìm shops: " . ErrorHandler::getErrorMessage($findResult));
            return Command::FAILURE;
        }

        $data = $findResult['data'] ?? $findResult;
        $shops = $data['shops_needing_authorization'] ?? [];
        $count = $data['count'] ?? count($shops);

        if ($count === 0) {
            $this->info("✅ Tất cả shops đã có authorization!");
            return Command::SUCCESS;
        }

        // Create choices array
        $choices = [];
        foreach ($shops as $shop) {
            $choices[$shop->shop_id] = "{$shop->name} ({$shop->shop_id})";
        }
        $choices['all'] = "Tất cả {$count} shops";

        $selected = $this->choice(
            "Chọn shop để cấp quyền:",
            $choices
        );

        if ($selected === 'all') {
            $this->line(''); // Add spacing
            return $this->batchGrantAuthorizations();
        } else {
            $this->line(''); // Add spacing
            return $this->grantSingleShopAuthorization($selected);
        }
    }

    /**
     * Validate shops only
     */
    protected function validateShops(): int
    {
        $this->info("🔍 Validating shop eligibility...");

        $findResult = $this->syncService->findShopsNeedingAuthorization();
        if (!ErrorHandler::isSuccess($findResult)) {
            $this->error("❌ Không thể tìm shops: " . ErrorHandler::getErrorMessage($findResult));
            return Command::FAILURE;
        }

        $data = $findResult['data'] ?? $findResult;
        $shops = $data['shops_needing_authorization'] ?? [];

        $this->info("📊 VALIDATION RESULTS:");
        $eligible = 0;
        $ineligible = 0;

        foreach ($shops as $shop) {
            $validationResult = $this->syncService->validateShopEligibility($shop);

            if (ErrorHandler::isSuccess($validationResult)) {
                $this->info("   ✅ {$shop->name} ({$shop->shop_id}) - Eligible");
                $eligible++;
            } else {
                $this->error("   ❌ {$shop->name} ({$shop->shop_id}) - Not eligible");
                $issues = $validationResult['context']['issues'] ?? [];
                foreach ($issues as $issue) {
                    $this->line("      - {$issue}");
                }
                $ineligible++;
            }
        }

        $this->info("\n📈 SUMMARY:");
        $this->info("   ✅ Eligible: {$eligible}");
        $this->info("   ❌ Ineligible: {$ineligible}");

        return Command::SUCCESS;
    }

    /**
     * Get advertiser ID from option or prompt
     */
    protected function getAdvertiserId(): ?string
    {
        $advertiserId = $this->option('advertiser-id');

        if (!$advertiserId) {
            $advertiserId = $this->ask('Nhập Advertiser ID:');
        }

        if (!$advertiserId) {
            $this->error("❌ Advertiser ID là bắt buộc");
            return null;
        }

        $this->info("👤 Advertiser ID: {$advertiserId}");
        return $advertiserId;
    }

    /**
     * Handle grant result cho single shop
     */
    protected function handleGrantResult(array $result, string $shopName): int
    {
        if (ErrorHandler::isSuccess($result)) {
            $this->info("✅ Cấp quyền thành công cho {$shopName}!");

            $data = $result['data'];
            if (isset($data['sync_result']['authorization'])) {
                $auth = $data['sync_result']['authorization'];
                $this->info("   🔐 Authorization Status: " . ($auth->status ?? 'Unknown'));
            }

            return Command::SUCCESS;
        } else {
            $errorType = ErrorHandler::getErrorType($result);
            $errorMessage = ErrorHandler::getErrorMessage($result);

            $this->error("❌ Cấp quyền thất bại cho {$shopName}:");
            $this->error("   {$errorMessage}");

            if ($errorType === ErrorHandler::VALIDATION_ERROR) {
                $context = $result['context'] ?? [];
                if (isset($context['existing_authorization'])) {
                    $this->warn("   💡 Authorization đã tồn tại cho shop này");
                }
            }

            return Command::FAILURE;
        }
    }

    /**
     * Handle batch grant result
     */
    protected function handleBatchGrantResult(array $result): int
    {
        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];

            $this->info("✅ Batch grant hoàn tất:");
            $this->info("   📊 Tổng shops yêu cầu: {$data['requested_shops']}");
            $this->info("   🔍 Shops tìm thấy: {$data['found_shops']}");
            $this->info("   ✅ Cấp quyền thành công: {$data['successful_grants']}");
            $this->info("   ❌ Cấp quyền thất bại: {$data['failed_grants']}");

            // Show details for failed grants
            if ($data['failed_grants'] > 0) {
                $this->warn("\n⚠️ Chi tiết shops thất bại:");
                foreach ($data['results'] as $shopId => $shopResult) {
                    if (!ErrorHandler::isSuccess($shopResult)) {
                        $this->line("   - {$shopId}: " . ErrorHandler::getErrorMessage($shopResult));
                    }
                }
            }

            return $data['failed_grants'] > 0 ? Command::FAILURE : Command::SUCCESS;
        } else {
            $this->error("❌ Batch grant thất bại: " . ErrorHandler::getErrorMessage($result));
            return Command::FAILURE;
        }
    }
}
