<?php

namespace App\Console\Commands;

use App\Models\TikTokSettings;
use App\Services\TikTok\TikTokConfigService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

/**
 * Debug command for TikTok configuration issues
 *
 * This command helps diagnose and fix common TikTok API configuration problems:
 * - Cache inconsistency between merged config and individual settings
 * - Missing or corrupted configuration values
 * - Encryption/decryption issues
 *
 * Usage:
 * - php artisan tiktok:debug-config (basic debug)
 * - php artisan tiktok:debug-config --show-values (show actual values)
 * - php artisan tiktok:debug-config --fix (auto-fix cache issues)
 */

class DebugTikTokConfigCommand extends Command
{
    protected $signature = 'tiktok:debug-config
                            {--show-values : Show actual values instead of [SET]/[EMPTY] placeholders}
                            {--fix : Attempt to fix cache issues automatically}';
    protected $description = 'Debug TikTok configuration loading and fix common issues';

    public function handle()
    {
        $this->info('🔍 TikTok Configuration Debug');
        $this->newLine();

        // 1. Check database settings
        $this->info('1. Database Settings:');
        $dbSettings = TikTokSettings::whereIn('key', ['api.access_token', 'api.app_id', 'api.secret'])
            ->get(['key', 'value', 'is_encrypted']);

        if ($dbSettings->isEmpty()) {
            $this->error('❌ No TikTok settings found in database!');
        } else {
            foreach ($dbSettings as $setting) {
                $value = $setting->is_encrypted ? '[ENCRYPTED]' : $setting->value;
                $this->line("   {$setting->key}: {$value}");
            }
        }

        $this->newLine();

        // 2. Check config service
        $this->info('2. Config Service Values:');
        $configService = new TikTokConfigService();

        $keys = ['api.access_token', 'api.app_id', 'api.secret'];
        foreach ($keys as $key) {
            $value = $configService->get($key);

            if ($this->option('show-values')) {
                $display = $value ?: '[EMPTY]';
            } else {
                $display = empty($value) ? '[EMPTY]' : '[SET]';
            }

            $this->line("   {$key}: {$display}");
        }

        $this->newLine();

        // 3. Check cache
        $this->info('3. Cache Status:');
        $cacheKeys = [
            'tiktok_config_merged',
            'tiktok_setting_api.access_token',
            'tiktok_setting_api.app_id',
            'tiktok_setting_api.secret'
        ];

        foreach ($cacheKeys as $cacheKey) {
            $cached = Cache::has($cacheKey);
            $status = $cached ? '✅ EXISTS' : '❌ MISSING';
            $this->line("   {$cacheKey}: {$status}");
        }

        $this->newLine();

        // 4. Test decryption
        $this->info('4. Testing Decryption:');
        $encryptedSettings = TikTokSettings::where('is_encrypted', true)
            ->whereIn('key', ['api.access_token', 'api.app_id', 'api.secret'])
            ->get();

        foreach ($encryptedSettings as $setting) {
            try {
                $decrypted = $setting->getTypedValue();
                $status = !empty($decrypted) ? '✅ OK' : '❌ EMPTY';
                $this->line("   {$setting->key}: {$status}");
            } catch (\Exception $e) {
                $this->error("   {$setting->key}: ❌ ERROR - {$e->getMessage()}");
            }
        }

        $this->newLine();

        // 5. Check for issues and fix if requested
        $this->info('5. Issue Detection:');
        $issues = [];

        // Kiểm tra cache inconsistency
        $cacheInconsistent = false;
        foreach ($cacheKeys as $cacheKey) {
            if (!Cache::has($cacheKey)) {
                $cacheInconsistent = true;
                $issues[] = "Cache key missing: {$cacheKey}";
            }
        }

        // Kiểm tra config values
        $configIncomplete = false;
        foreach ($keys as $key) {
            if (empty($configService->get($key))) {
                $configIncomplete = true;
                $issues[] = "Config value missing: {$key}";
            }
        }

        if (empty($issues)) {
            $this->info("   ✅ No issues detected");
        } else {
            $this->error("   ❌ Issues detected:");
            foreach ($issues as $issue) {
                $this->line("      - {$issue}");
            }

            // Fix issues if requested
            if ($this->option('fix') || ($cacheInconsistent && $this->confirm('Cache inconsistency detected. Do you want to rebuild the cache?'))) {
                $this->info("\n🔧 Fixing issues...");

                // Clear and rebuild cache
                $this->line("   Rebuilding config cache...");
                $configService->clearCache();
                $configService->rebuildConfigCache();

                // Verify fix
                $this->line("   Verifying fix...");
                $fixedIssues = 0;

                // Check cache again
                foreach ($cacheKeys as $cacheKey) {
                    if (Cache::has($cacheKey)) {
                        $fixedIssues++;
                    }
                }

                $this->info("   ✅ Fixed {$fixedIssues} of " . count($issues) . " issues");
            }
        }

        // 6. Full config dump
        $this->info("\n6. Full Config:");
        $fullConfig = $configService->getConfig();
        $this->line(json_encode($fullConfig, JSON_PRETTY_PRINT));

        return Command::SUCCESS;
    }
}
