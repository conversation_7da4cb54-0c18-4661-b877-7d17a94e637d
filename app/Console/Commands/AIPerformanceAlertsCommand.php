<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use App\Services\AI\AIScoringService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AIPerformanceAlertsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:performance-alerts
                            {--force : Force alerts even if recently sent}
                            {--campaign= : Analyze specific campaign ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate AI-powered performance alerts for campaigns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🤖 Starting AI Performance Alerts Analysis...');

        $aiService = new AIScoringService();

        // Check if AI is enabled
        if (!$aiService->getConfig()['enabled']) {
            $this->warn('AI scoring is disabled. Enable it in admin panel.');
            return Command::SUCCESS;
        }

        $campaignId = $this->option('campaign');
        $campaigns = $campaignId
            ? Campaign::where('id', $campaignId)->get()
            : Campaign::where('status', 'active')->get();

        if ($campaigns->isEmpty()) {
            $this->warn('No campaigns found for analysis.');
            return Command::SUCCESS;
        }

        $this->info("Analyzing {$campaigns->count()} campaigns...");

        $alertsGenerated = 0;
        $criticalIssues = 0;
        $optimizationOpportunities = 0;

        foreach ($campaigns as $campaign) {
            $this->line("Analyzing campaign: {$campaign->name}");

            try {
                // AI Campaign Analysis
                $analysis = $aiService->analyzeCampaign($campaign);

                if ($analysis['success']) {
                    $alerts = $this->processAnalysisAlerts($campaign, $analysis);
                    $alertsGenerated += count($alerts);

                    foreach ($alerts as $alert) {
                        if ($alert['severity'] === 'critical') {
                            $criticalIssues++;
                        }
                    }
                }

                // Budget Optimization Analysis
                $budgetAnalysis = $aiService->optimizeBudget($campaign);

                if ($budgetAnalysis['success']) {
                    $budgetAlerts = $this->processBudgetAlerts($campaign, $budgetAnalysis);
                    $alertsGenerated += count($budgetAlerts);
                    $optimizationOpportunities += count($budgetAlerts);
                }

                // Performance Alerts
                $performanceAlerts = $aiService->generatePerformanceAlerts();

                if ($performanceAlerts['success']) {
                    $this->processPerformanceAlerts($performanceAlerts);
                }

            } catch (\Exception $e) {
                $this->error("Failed to analyze campaign {$campaign->name}: {$e->getMessage()}");
                Log::error('AI Performance Alert failed', [
                    'campaign_id' => $campaign->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Summary
        $this->newLine();
        $this->info('📊 AI Performance Alerts Summary:');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Campaigns Analyzed', $campaigns->count()],
                ['Total Alerts Generated', $alertsGenerated],
                ['Critical Issues', $criticalIssues],
                ['Optimization Opportunities', $optimizationOpportunities],
            ]
        );

        if ($criticalIssues > 0) {
            $this->warn("⚠️  {$criticalIssues} critical issues require immediate attention!");
        }

        if ($optimizationOpportunities > 0) {
            $this->info("💡 {$optimizationOpportunities} optimization opportunities identified!");
        }

        return Command::SUCCESS;
    }

    /**
     * Process AI analysis alerts
     */
    protected function processAnalysisAlerts(Campaign $campaign, array $analysis): array
    {
        $alerts = [];
        $score = $analysis['ai_score'] ?? 0;
        $confidence = $analysis['confidence'] ?? 0;

        // Low AI Score Alert
        if ($score < 60) {
            $alerts[] = [
                'type' => 'low_ai_score',
                'severity' => 'critical',
                'message' => "Campaign '{$campaign->name}' has low AI score: {$score}/100",
                'recommendations' => $analysis['recommendations'] ?? [],
            ];

            $this->warn("🚨 CRITICAL: {$campaign->name} - AI Score: {$score}/100");
        }

        // Low Confidence Alert
        if ($confidence < 0.7) {
            $alerts[] = [
                'type' => 'low_confidence',
                'severity' => 'warning',
                'message' => "AI analysis confidence is low: " . round($confidence * 100) . "%",
            ];
        }

        // Risk Factors Alert
        if (!empty($analysis['risk_factors'])) {
            $alerts[] = [
                'type' => 'risk_factors',
                'severity' => 'warning',
                'message' => "Risk factors detected: " . implode(', ', $analysis['risk_factors']),
            ];
        }

        return $alerts;
    }

    /**
     * Process budget optimization alerts
     */
    protected function processBudgetAlerts(Campaign $campaign, array $budgetAnalysis): array
    {
        $alerts = [];
        $improvement = $budgetAnalysis['expected_improvement'] ?? 0;
        $currentBudget = $budgetAnalysis['current_budget'] ?? 0;
        $recommendedBudget = $budgetAnalysis['recommended_budget'] ?? 0;

        // Significant Budget Optimization Opportunity
        if ($improvement > 0.2) { // 20% improvement potential
            $improvementPercent = round($improvement * 100);
            $alerts[] = [
                'type' => 'budget_optimization',
                'severity' => 'info',
                'message' => "Campaign '{$campaign->name}' has {$improvementPercent}% budget optimization potential",
                'current_budget' => $currentBudget,
                'recommended_budget' => $recommendedBudget,
            ];

            $this->info("💰 OPPORTUNITY: {$campaign->name} - {$improvementPercent}% budget optimization potential");
        }

        return $alerts;
    }

    /**
     * Process performance alerts
     */
    protected function processPerformanceAlerts(array $performanceAlerts): void
    {
        if (empty($performanceAlerts['alerts'])) {
            return;
        }

        foreach ($performanceAlerts['alerts'] as $alert) {
            $campaignName = $alert['campaign_name'] ?? 'Unknown';
            $alertCount = count($alert['alerts'] ?? []);

            $this->warn("⚠️  {$campaignName}: {$alertCount} performance alerts");

            foreach ($alert['alerts'] as $specificAlert) {
                $this->line("   - {$specificAlert['message']} ({$specificAlert['severity']})");
            }
        }
    }
}
