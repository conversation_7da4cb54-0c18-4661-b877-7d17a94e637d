<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use App\Services\TikTok\GmvMaxReportService;
use App\Services\TikTok\TikTokApiService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncGmvMaxReports extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'gmv-max:sync-reports
                            {--days=7 : Number of days to sync reports for}
                            {--campaign= : Specific campaign ID to sync}
                            {--store= : Specific store ID to sync}
                            {--start-date= : Start date (YYYY-MM-DD)}
                            {--end-date= : End date (YYYY-MM-DD)}
                            {--force : Force sync even if data exists}';

    /**
     * The console command description.
     */
    protected $description = 'Sync GMV Max campaign reports from TikTok API';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Starting GMV Max Reports Sync...');

        try {
            // Initialize services
            $apiService = new TikTokApiService();
            $reportService = new GmvMaxReportService($apiService);

            // Get parameters
            $days = (int) $this->option('days');
            $campaignId = $this->option('campaign');
            $storeId = $this->option('store');
            $startDate = $this->option('start-date');
            $endDate = $this->option('end-date');
            $force = $this->option('force');

            // Determine date range
            if ($startDate && $endDate) {
                $start = $startDate;
                $end = $endDate;

                // Validate custom date range
                $startCarbon = Carbon::parse($start);
                $endCarbon = Carbon::parse($end);
                $daysDiff = $startCarbon->diffInDays($endCarbon);

                if ($daysDiff > 30) {
                    $this->error('❌ Date range cannot exceed 30 days for daily breakdown reports.');
                    return self::FAILURE;
                }

                if ($startCarbon->isFuture() || $endCarbon->isFuture()) {
                    $this->error('❌ Start date and end date cannot be in the future.');
                    return self::FAILURE;
                }

                if ($startCarbon->isAfter($endCarbon)) {
                    $this->error('❌ Start date must be before or equal to end date.');
                    return self::FAILURE;
                }
            } else {
                $start = Carbon::now()->subDays($days)->format('Y-m-d');
                $end = Carbon::now()->format('Y-m-d');
            }

            $daysDiff = Carbon::parse($start)->diffInDays(Carbon::parse($end));
            $this->info("📅 Syncing reports from {$start} to {$end} ({$daysDiff} days)");

            // Get store IDs
            $storeIds = $this->getStoreIds($storeId);
            if (empty($storeIds)) {
                $this->error('❌ No valid store IDs found');
                return self::FAILURE;
            }

            $this->info("🏪 Store IDs: " . implode(', ', $storeIds));

            // Get campaign IDs
            $campaignIds = $this->getCampaignIds($campaignId);
            if (!empty($campaignIds)) {
                $this->info("📊 Campaign IDs: " . implode(', ', $campaignIds));
            } else {
                $this->info("📊 Syncing all campaigns");
            }

            // Progress bar
            $this->output->progressStart(count($storeIds));

            $totalResults = [
                'success' => true,
                'total_synced' => 0,
                'created' => 0,
                'updated' => 0,
                'errors' => []
            ];

            // Sync reports for each store
            foreach ($storeIds as $currentStoreId) {
                $this->info("\n🔄 Syncing store: {$currentStoreId}");

                $result = $reportService->syncReports(
                    [$currentStoreId],
                    $start,
                    $end,
                    $campaignIds
                );

                if ($result['success']) {
                    $totalResults['total_synced'] += $result['total_synced'];
                    $totalResults['created'] += $result['created'];
                    $totalResults['updated'] += $result['updated'];

                    $this->info("✅ Store {$currentStoreId}: {$result['total_synced']} reports synced");
                } else {
                    $totalResults['errors'][] = "Store {$currentStoreId}: " . $result['error'];
                    $this->error("❌ Store {$currentStoreId}: " . $result['error']);
                }

                $this->output->progressAdvance();
            }

            $this->output->progressFinish();

            // Display results
            $this->displayResults($totalResults);

            // Log results
            Log::info('GMV Max Reports Sync Completed', $totalResults);

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Sync failed: " . $e->getMessage());
            Log::error('GMV Max Reports Sync Failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return self::FAILURE;
        }
    }

    /**
     * Get store IDs to sync
     */
    private function getStoreIds(?string $storeId): array
    {
        if ($storeId) {
            return [$storeId];
        }

        // Get store IDs from campaigns via shop relationship
        // Note: shop_id in gmv_max_campaigns table is foreign key to tiktok_shops.id
        // We need shop_id from tiktok_shops table which is the TikTok Store ID
        $storeIds = Campaign::join('tiktok_shops', 'gmv_max_campaigns.shop_id', '=', 'tiktok_shops.id')
            ->whereNotNull('tiktok_shops.shop_id')
            ->where('gmv_max_campaigns.deleted_at', null) // Only active campaigns
            ->distinct()
            ->pluck('tiktok_shops.shop_id')
            ->toArray();

        if (empty($storeIds)) {
            // Fallback to configuration or ask user
            $storeIds = config('tiktok.default_store_ids', []);
        }

        return array_filter($storeIds);
    }

    /**
     * Get campaign IDs to sync
     */
    private function getCampaignIds(?string $campaignId): array
    {
        if ($campaignId) {
            return [$campaignId];
        }

        // Return empty array to sync all campaigns
        return [];
    }

    /**
     * Display sync results
     */
    private function displayResults(array $results): void
    {
        $this->newLine();
        $this->info('📊 Sync Results:');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Reports Synced', $results['total_synced']],
                ['New Reports Created', $results['created']],
                ['Reports Updated', $results['updated']],
                ['Errors', count($results['errors'])]
            ]
        );

        if (!empty($results['errors'])) {
            $this->newLine();
            $this->error('❌ Errors encountered:');
            foreach ($results['errors'] as $error) {
                $this->line("  • {$error}");
            }
        }

        if ($results['total_synced'] > 0) {
            $this->newLine();
            $this->info('✅ GMV Max reports sync completed successfully!');

            // Show performance summary
            $this->showPerformanceSummary();
        }
    }

    /**
     * Show performance summary
     */
    private function showPerformanceSummary(): void
    {
        try {
            $apiService = new TikTokApiService();
            $reportService = new GmvMaxReportService($apiService);

            // Get recent campaigns
            $campaigns = Campaign::where('status', 'active')->limit(5)->pluck('campaign_id')->toArray();

            if (!empty($campaigns)) {
                $summary = $reportService->getPerformanceSummary($campaigns, 7);

                $this->newLine();
                $this->info('📈 Performance Summary (Last 7 days):');
                $this->table(
                    ['Metric', 'Value'],
                    [
                        ['Total Cost', number_format($summary['total_cost'], 0) . ' VND'],
                        ['Total Revenue', number_format($summary['total_revenue'], 0) . ' VND'],
                        ['Total Orders', number_format($summary['total_orders'])],
                        ['Average ROI', number_format($summary['average_roi'], 2) . '%'],
                        ['Profit', number_format($summary['profit'], 0) . ' VND'],
                        ['Reports Count', $summary['report_count']]
                    ]
                );
            }

        } catch (\Exception $e) {
            $this->warn('Could not generate performance summary: ' . $e->getMessage());
        }
    }
}
