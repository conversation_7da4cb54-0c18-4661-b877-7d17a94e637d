<?php

namespace App\Console\Commands;

use App\Models\AiAnalysis;
use App\Models\Campaign;
use App\Services\AI\AIScoringService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use Symfony\Component\Console\Command\Command as CommandAlias;

class ProcessAiAnalysis extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:analyze-campaigns
                            {--type=all : Analysis type (recommendations, roi_prediction, budget_optimization, all)}
                            {--campaign= : Specific campaign ID to analyze}
                            {--force : Force analysis even if recent analysis exists}
                            {--hours=24 : Consider analysis stale after X hours}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process AI analysis for campaigns in background';

    protected AIScoringService $aiService;

    public function __construct()
    {
        parent::__construct();
        $this->aiService = new AIScoringService();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🤖 Starting AI Campaign Analysis...');

        $analysisType = $this->option('type');
        $campaignId = $this->option('campaign');
        $force = $this->option('force');
        $staleHours = (int)$this->option('hours');

        try {
            if ($campaignId) {
                $this->processSingleCampaign($campaignId, $analysisType, $force, $staleHours);
            } else {
                $this->processAllCampaigns($analysisType, $force, $staleHours);
            }

            $this->info('✅ AI Campaign Analysis completed successfully!');
            return CommandAlias::SUCCESS;

        } catch (Exception $e) {
            $this->error('❌ AI Campaign Analysis failed: ' . $e->getMessage());
            Log::error('AI Campaign Analysis failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return CommandAlias::FAILURE;
        }
    }

    protected function processSingleCampaign(int $campaignId, string $analysisType, bool $force, int $staleHours): void
    {
        $campaign = Campaign::find($campaignId);

        if (!$campaign) {
            $this->error("Campaign {$campaignId} not found");
            return;
        }

        $this->info("Processing campaign: {$campaign->name} (ID: {$campaign->id})");

        $types = $analysisType === 'all' ? AiAnalysis::getAnalysisTypes() : [$analysisType];

        foreach ($types as $type) {
            $this->processCampaignAnalysis($campaign, $type, $force, $staleHours);
        }
    }

    protected function processAllCampaigns(string $analysisType, bool $force, int $staleHours): void
    {
        // Get active campaigns that need analysis
        $campaigns = Campaign::where('status', 'active')
            ->with('currentAiAnalyses')
            ->get();

        if ($campaigns->isEmpty()) {
            $this->warn('No active campaigns found');
            return;
        }

        $this->info("Found {$campaigns->count()} active campaigns");

        $types = $analysisType === 'all' ? AiAnalysis::getAnalysisTypes() : [$analysisType];
        $progressBar = $this->output->createProgressBar($campaigns->count() * count($types));
        $progressBar->start();

        foreach ($campaigns as $campaign) {
            foreach ($types as $type) {
                $this->processCampaignAnalysis($campaign, $type, $force, $staleHours);
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->newLine();
    }

    protected function processCampaignAnalysis(Campaign $campaign, string $type, bool $force, int $staleHours): void
    {
        try {
            // Check if analysis is needed
            if (!$force && !$this->needsAnalysis($campaign, $type, $staleHours)) {
                return;
            }

            $this->line("  Analyzing {$type} for campaign: {$campaign->name}");

            // Perform AI analysis based on type
            $result = match ($type) {
                AiAnalysis::TYPE_RECOMMENDATIONS => $this->aiService->generateCampaignRecommendations($campaign),
                AiAnalysis::TYPE_ROI_PREDICTION => $this->aiService->analyzeCampaign($campaign),
                AiAnalysis::TYPE_BUDGET_OPTIMIZATION => $this->aiService->optimizeBudget($campaign),
                default => throw new InvalidArgumentException("Unknown analysis type: {$type}")
            };

            if ($result['success']) {
                // Store analysis result
                $confidence = $result['confidence'] ?? null;
                if ($confidence !== null && !is_float($confidence)) {
                    $confidence = is_numeric($confidence) ? (float) $confidence : null;
                }

                AiAnalysis::createOrUpdateAnalysis(
                    $campaign->id,
                    $type,
                    $result,
                    $confidence
                );

                $this->line("    ✅ {$type} analysis completed");
            } else {
                $this->line("    ❌ {$type} analysis failed: " . ($result['error'] ?? 'Unknown error'));

                Log::warning('AI analysis failed for campaign', [
                    'campaign_id' => $campaign->id,
                    'analysis_type' => $type,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }

        } catch (Exception $e) {
            $this->line("    ❌ {$type} analysis error: " . $e->getMessage());

            Log::error('AI analysis exception for campaign', [
                'campaign_id' => $campaign->id,
                'analysis_type' => $type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    protected function needsAnalysis(Campaign $campaign, string $type, int $staleHours): bool
    {
        $currentAnalysis = $campaign->getCurrentAiAnalysis($type);

        if (!$currentAnalysis) {
            return true; // No analysis exists
        }

        return $currentAnalysis->isStale($staleHours); // Analysis is stale
    }
}
