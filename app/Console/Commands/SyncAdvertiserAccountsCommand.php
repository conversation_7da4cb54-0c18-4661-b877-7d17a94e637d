<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TikTok\AdvertiserAccountSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Jobs\SyncAdvertiserAccountsJob;
use App\Helpers\ErrorHandler;

class SyncAdvertiserAccountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:sync-advertisers
                            {--queue : Run sync in background queue}
                            {--force : Force sync even if recently synced}
                            {--no-notify : Do not send notifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ advertiser accounts từ TikTok Business API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Bắt đầu đồng bộ advertiser accounts từ TikTok API...');

        try {
            // Kiểm tra nếu chạy trong queue
            if ($this->option('queue')) {
                $this->handleQueueSync();
                return Command::SUCCESS;
            }

            // Chạy sync trực tiếp
            $this->handleDirectSync();
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $errorResponse = ErrorHandler::handleNetworkError(
                $e,
                'Advertiser Account Sync',
                ['command' => 'sync-advertisers']
            );

            $this->error("❌ Sync failed: " . ErrorHandler::getErrorMessage($errorResponse));
            return Command::FAILURE;
        }
    }

    /**
     * Xử lý sync trong queue
     */
    protected function handleQueueSync(): void
    {
        $notifyUsers = !$this->option('no-notify');

        SyncAdvertiserAccountsJob::dispatch($notifyUsers);

        $this->info('✅ Đã thêm job sync vào queue. Kiểm tra queue worker để theo dõi tiến trình.');
        $this->line('💡 Chạy: php artisan queue:work để xử lý job');
    }

    /**
     * Xử lý sync trực tiếp
     */
    protected function handleDirectSync(): void
    {
        // Khởi tạo services
        $apiService = new TikTokApiService();
        $syncService = new AdvertiserAccountSyncService($apiService);

        // Kiểm tra nếu cần sync
        if (!$this->option('force') && !$syncService->shouldSync()) {
            $this->warn('⚠️  Advertiser accounts đã được sync gần đây. Sử dụng --force để sync lại.');
            return;
        }

        // Hiển thị progress bar
        $this->info('📡 Đang lấy dữ liệu từ TikTok API...');

        $progressBar = $this->output->createProgressBar(4);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');

        $progressBar->setMessage('Kết nối TikTok API...');
        $progressBar->advance();

        // Thực hiện sync
        $result = $syncService->syncAdvertiserAccounts();

        $progressBar->setMessage('Xử lý dữ liệu...');
        $progressBar->advance();

        $progressBar->setMessage('Cập nhật database...');
        $progressBar->advance();

        $progressBar->setMessage('Hoàn thành!');
        $progressBar->finish();

        $this->newLine(2);

        if ($result['success']) {
            $this->displaySuccessResults($result['stats']);
        } else {
            $this->error("❌ Sync thất bại: {$result['error']}");
        }
    }

    /**
     * Hiển thị kết quả thành công
     */
    protected function displaySuccessResults(array $stats): void
    {
        $this->info('✅ Đồng bộ advertiser accounts thành công!');
        $this->newLine();

        // Tạo bảng thống kê
        $this->table(
            ['Thống kê', 'Số lượng'],
            [
                ['Tổng số accounts từ API', $stats['total_fetched']],
                ['Accounts tạo mới', $stats['created']],
                ['Accounts cập nhật', $stats['updated']],
                ['Accounts vô hiệu hóa', $stats['deactivated']],
                ['Lỗi xảy ra', $stats['errors']],
            ]
        );

        // Hiển thị thời gian
        if ($stats['start_time'] && $stats['end_time']) {
            $duration = $stats['start_time']->diffInSeconds($stats['end_time']);
            $this->info("⏱️  Thời gian thực hiện: {$duration} giây");
        }

        $this->newLine();
        $this->line('💡 Truy cập admin panel để xem chi tiết: http://localhost:8000/admin/advertiser-accounts');
    }
}
