<?php

namespace App\Console\Commands;

use App\Models\Shop;
use App\Services\TikTok\AuthorizationSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Helpers\ErrorHandler;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncTikTokAuthorizations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:sync-authorizations
                            {--shop-id= : Sync authorization for specific shop ID}
                            {--advertiser-id= : Sync for specific advertiser ID}
                            {--summary : Show authorization summary}
                            {--find-missing : Find shops needing authorization}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ exclusive authorization từ TikTok API';

    protected AuthorizationSyncService $syncService;

    public function __construct()
    {
        parent::__construct();

        $apiService = app(TikTokApiService::class);
        $this->syncService = new AuthorizationSyncService($apiService);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔐 Bắt đầu đồng bộ TikTok exclusive authorizations...');

        try {
            if ($this->option('summary')) {
                return $this->showSummary();
            }

            if ($this->option('find-missing')) {
                return $this->findMissingAuthorizations();
            }

            if ($shopId = $this->option('shop-id')) {
                return $this->syncSpecificShop($shopId);
            } else {
                return $this->syncAllShops();
            }
        } catch (\Exception $e) {
            $this->error('❌ Lỗi: ' . $e->getMessage());
            Log::error('TikTok Authorization Sync Command Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Đồng bộ authorization cho một shop cụ thể
     */
    protected function syncSpecificShop(string $shopId): int
    {
        $shop = Shop::where('shop_id', $shopId)->first();

        if (!$shop) {
            $this->error("❌ Không tìm thấy shop với ID: {$shopId}");
            return Command::FAILURE;
        }

        $this->info("🔐 Đồng bộ authorization cho shop: {$shop->name}");

        $advertiserId = $this->option('advertiser-id');
        $result = $this->syncService->syncShopAuthorization($shop, $advertiserId);

        return $this->handleSyncResult($result, $shop->name);
    }

    /**
     * Đồng bộ authorization cho tất cả shops
     */
    protected function syncAllShops(): int
    {
        $advertiserId = $this->option('advertiser-id');

        $this->info("🔐 Đồng bộ authorization cho tất cả shops...");

        if ($advertiserId) {
            $this->info("👤 Advertiser ID: {$advertiserId}");
        }

        $result = $this->syncService->syncAllShopsAuthorizations($advertiserId);

        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];
            $this->info("✅ Đồng bộ hoàn tất:");
            $this->info("   📊 Tổng shops: {$data['total_shops']}");
            $this->info("   ✅ Thành công: {$data['successful_shops']}");
            $this->info("   ❌ Thất bại: {$data['failed_shops']}");

            // Show details for failed shops
            if ($data['failed_shops'] > 0) {
                $this->warn("\n⚠️ Chi tiết shops thất bại:");
                foreach ($data['shop_results'] as $shopId => $shopResult) {
                    if (!ErrorHandler::isSuccess($shopResult)) {
                        $this->line("   - {$shopId}: " . ErrorHandler::getErrorMessage($shopResult));
                    }
                }
            }

            return $data['failed_shops'] > 0 ? Command::FAILURE : Command::SUCCESS;
        } else {
            $this->error("❌ Đồng bộ thất bại: " . ErrorHandler::getErrorMessage($result));
            return Command::FAILURE;
        }
    }

    /**
     * Hiển thị tổng quan authorization
     */
    protected function showSummary(): int
    {
        $result = $this->syncService->getAuthorizationSummary();

        if (ErrorHandler::isSuccess($result)) {
            $stats = $result['data'];

            $this->info('📊 TỔNG QUAN EXCLUSIVE AUTHORIZATIONS:');
            $this->info("   🔐 Tổng authorizations: {$stats['total_authorizations']}");
            $this->info("   ✅ Có hiệu lực (EFFECTIVE): {$stats['effective_authorizations']}");
            $this->info("   ❌ Không hiệu lực (INEFFECTIVE): {$stats['ineffective_authorizations']}");
            $this->info("   🚫 Chưa ủy quyền (UNAUTHORIZED): {$stats['unauthorized_shops']}");
            $this->info("   👤 Advertisers đã phê duyệt: {$stats['approved_advertisers']}");
            $this->info("   ⏳ Advertisers đang chờ: {$stats['pending_advertisers']}");

            return Command::SUCCESS;
        } else {
            $this->error("❌ Không thể lấy thống kê: " . ErrorHandler::getErrorMessage($result));
            return Command::FAILURE;
        }
    }

    /**
     * Tìm shops cần authorization
     */
    protected function findMissingAuthorizations(): int
    {
        $result = $this->syncService->findShopsNeedingAuthorization();

        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];
            $shops = $data['shops_needing_authorization'];

            $this->info("🔍 SHOPS CẦN AUTHORIZATION ({$data['count']} shops):");

            if ($data['count'] > 0) {
                foreach ($shops as $shop) {
                    $this->line("   - {$shop->shop_id}: {$shop->name} ({$shop->region})");
                }

                $this->warn("\n💡 Để đồng bộ authorization cho các shops này:");
                $this->line("   php artisan tiktok:sync-authorizations");
            } else {
                $this->info("   ✅ Tất cả shops đã có authorization!");
            }

            return Command::SUCCESS;
        } else {
            $this->error("❌ Không thể tìm shops: " . ErrorHandler::getErrorMessage($result));
            return Command::FAILURE;
        }
    }

    /**
     * Xử lý kết quả sync cho một shop
     */
    protected function handleSyncResult(array $result, string $shopName): int
    {
        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];
            $apiData = $data['api_data'] ?? [];

            $this->info("✅ Đồng bộ thành công cho {$shopName}:");
            $this->info("   🔐 Authorization Status: " . ($apiData['authorization_status'] ?? 'Unknown'));

            if (isset($apiData['advertiser_name'])) {
                $this->info("   👤 Advertiser: {$apiData['advertiser_name']}");
            }

            if (isset($apiData['advertiser_status'])) {
                $this->info("   📊 Advertiser Status: {$apiData['advertiser_status']}");
            }

            return Command::SUCCESS;
        } else {
            $this->error("❌ Đồng bộ thất bại cho {$shopName}: " . ErrorHandler::getErrorMessage($result));
            return Command::FAILURE;
        }
    }
}
