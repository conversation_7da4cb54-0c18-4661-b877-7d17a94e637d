<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MakeActionClassCommand extends Command
{
    protected $signature = 'make:action-class {resource} {name} {--type=action : Type of action (action, bulk)} {--description= : Description of the action}';
    protected $description = 'Create a new Filament action class for a resource';

    public function handle()
    {
        $resource = $this->argument('resource');
        $name = $this->argument('name');
        $type = $this->option('type');
        $description = $this->option('description') ?: 'Custom action';

        // Validate inputs
        if (!$this->validateInputs($resource, $name, $type)) {
            return Command::FAILURE;
        }

        // Generate action class
        $result = $this->generateActionClass($resource, $name, $type, $description);

        if ($result['success']) {
            $this->info("✅ Action class created successfully!");
            $this->line("📁 File: {$result['file_path']}");
            $this->line("🏷️  Class: {$result['class_name']}");
            $this->newLine();
            $this->info("Next steps:");
            $this->line("1. Implement the action logic in the class");
            $this->line("2. Add the action to your resource:");
            $this->line("   use {$result['class_name']};");
            $this->line("   // In actions array: {$result['class_basename']}::make(),");
            return Command::SUCCESS;
        } else {
            $this->error("❌ Failed to create action class: {$result['error']}");
            return Command::FAILURE;
        }
    }

    protected function validateInputs(string $resource, string $name, string $type): bool
    {
        // Validate resource name
        if (!preg_match('/^[A-Za-z][A-Za-z0-9]*Resource$/', $resource)) {
            $this->error("Resource name must end with 'Resource' and contain only alphanumeric characters");
            return false;
        }

        // Validate action name
        if (!preg_match('/^[A-Za-z][A-Za-z0-9]*Action$/', $name)) {
            $this->error("Action name must end with 'Action' and contain only alphanumeric characters");
            return false;
        }

        // Validate type
        if (!in_array($type, ['action', 'bulk'])) {
            $this->error("Type must be either 'action' or 'bulk'");
            return false;
        }

        return true;
    }

    protected function generateActionClass(string $resource, string $name, string $type, string $description): array
    {
        try {
            // Create directory if it doesn't exist
            $actionsDir = app_path("Filament/Resources/{$resource}/Actions");
            if (!File::exists($actionsDir)) {
                File::makeDirectory($actionsDir, 0755, true);
            }

            // Generate file path
            $filePath = "{$actionsDir}/{$name}.php";
            
            // Check if file already exists
            if (File::exists($filePath)) {
                return [
                    'success' => false,
                    'error' => 'Action class already exists'
                ];
            }

            // Generate class content
            $content = $this->generateClassContent($resource, $name, $type, $description);

            // Write file
            File::put($filePath, $content);

            return [
                'success' => true,
                'file_path' => $filePath,
                'class_name' => "App\\Filament\\Resources\\{$resource}\\Actions\\{$name}",
                'class_basename' => $name
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    protected function generateClassContent(string $resource, string $name, string $type, string $description): string
    {
        $baseClass = $type === 'bulk' ? 'BulkAction' : 'Action';
        $baseClassImport = $type === 'bulk' 
            ? 'use Filament\Tables\Actions\BulkAction;'
            : 'use Filament\Tables\Actions\Action;';

        $recordParameter = $type === 'bulk' ? 'Collection $records' : '$record';
        $methodName = Str::camel(str_replace('Action', '', $name));
        $defaultName = Str::snake(str_replace('Action', '', $name));

        $template = <<<PHP
<?php

namespace App\Filament\Resources\\{$resource}\Actions;

{$baseClassImport}
use Filament\Notifications\Notification;

class {$name} extends {$baseClass}
{
    public static function getDefaultName(): ?string
    {
        return '{$defaultName}';
    }

    protected function setUp(): void
    {
        parent::setUp();

        \$this->label('Action Label')
            ->icon('heroicon-o-cog')
            ->color('primary')
            ->tooltip('{$description}')
            ->action(function ({$recordParameter}) {
                \$this->{$methodName}({$recordParameter});
            });
    }

    protected function {$methodName}({$recordParameter}): void
    {
        try {
            // TODO: Implement your action logic here
            
            // Example success notification
            Notification::make()
                ->title('Action completed successfully')
                ->body('The action has been executed successfully')
                ->success()
                ->send();

        } catch (\Exception \$e) {
            // Error notification
            Notification::make()
                ->title('Action failed')
                ->body('An error occurred: ' . \$e->getMessage())
                ->danger()
                ->send();
        }
    }
}
PHP;

        return $template;
    }
}
