<?php

namespace App\Console\Commands;

use App\Models\Shop;
use App\Services\TikTok\ProductSyncService;
use App\Services\TikTok\TikTokApiService;
use App\Helpers\ErrorHandler;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncTikTokProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:sync-products
                            {--shop-id= : Sync products for specific shop ID}
                            {--gmv-max : Only sync products eligible for GMV Max}
                            {--shopping-ads : Only sync products eligible for Shopping Ads}
                            {--page-size=100 : Number of products per page}
                            {--force : Force sync even if shop is inactive}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ sản phẩm từ TikTok API';

    protected ProductSyncService $syncService;

    public function __construct()
    {
        parent::__construct();

        $apiService = app(TikTokApiService::class);
        $this->syncService = new ProductSyncService($apiService);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Bắt đầu đồng bộ sản phẩm TikTok...');

        try {
            if ($shopId = $this->option('shop-id')) {
                return $this->syncSpecificShop($shopId);
            } else {
                return $this->syncAllShops();
            }
        } catch (\Exception $e) {
            $this->error('❌ Lỗi: ' . $e->getMessage());
            Log::error('TikTok Product Sync Command Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Đồng bộ sản phẩm cho một shop cụ thể
     */
    protected function syncSpecificShop(string $shopId): int
    {
        $shop = Shop::where('shop_id', $shopId)->first();

        if (!$shop) {
            $this->error("❌ Không tìm thấy shop với ID: {$shopId}");
            return Command::FAILURE;
        }

        if (!$this->option('force') && $shop->status !== 'active') {
            $this->error("❌ Shop {$shop->name} không active. Sử dụng --force để bỏ qua.");
            return Command::FAILURE;
        }

        $this->info("📦 Đồng bộ sản phẩm cho shop: {$shop->name}");

        $options = $this->buildSyncOptions();
        $result = $this->syncService->syncShopProducts($shop, $options);

        return $this->handleSyncResult($result, $shop->name);
    }

    /**
     * Đồng bộ sản phẩm cho tất cả shops
     */
    protected function syncAllShops(): int
    {
        $query = Shop::query();

        if (!$this->option('force')) {
            $query->where('status', 'active');
        }

        $shops = $query->get();

        if ($shops->isEmpty()) {
            $this->warn('⚠️ Không có shop nào để đồng bộ');
            return Command::SUCCESS;
        }

        $this->info("📦 Đồng bộ sản phẩm cho {$shops->count()} shops...");

        $options = $this->buildSyncOptions();
        $progressBar = $this->output->createProgressBar($shops->count());
        $progressBar->start();

        $totalResults = [
            'successful_shops' => 0,
            'failed_shops' => 0,
            'total_created' => 0,
            'total_updated' => 0,
            'total_errors' => 0
        ];

        foreach ($shops as $shop) {
            $this->line("\n🏪 Đồng bộ shop: {$shop->name}");

            $result = $this->syncService->syncShopProducts($shop, $options);

            if (ErrorHandler::isSuccess($result)) {
                $totalResults['successful_shops']++;
                $data = $result['data'];
                $totalResults['total_created'] += $data['created'] ?? 0;
                $totalResults['total_updated'] += $data['updated'] ?? 0;
                $totalResults['total_errors'] += count($data['errors'] ?? []);

                $this->info("  ✅ Thành công: {$data['created']} tạo mới, {$data['updated']} cập nhật");
            } else {
                $totalResults['failed_shops']++;
                $this->error("  ❌ Thất bại: " . ($result['message'] ?? 'Unknown error'));
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line('');

        // Hiển thị tổng kết
        $this->displaySummary($totalResults);

        return $totalResults['failed_shops'] > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Xây dựng options cho sync
     */
    protected function buildSyncOptions(): array
    {
        $options = [
            'page_size' => (int) $this->option('page-size')
        ];

        if ($this->option('gmv-max')) {
            $options['filtering'] = ['ad_creation_eligible' => 'GMV_MAX'];
        } elseif ($this->option('shopping-ads')) {
            $options['filtering'] = ['ad_creation_eligible' => 'CUSTOM_SHOP_ADS'];
        }

        return $options;
    }

    /**
     * Xử lý kết quả sync cho một shop
     */
    protected function handleSyncResult(array $result, string $shopName): int
    {
        if (ErrorHandler::isSuccess($result)) {
            $data = $result['data'];
            $this->info("✅ Đồng bộ thành công cho {$shopName}:");
            $this->info("   📊 Tổng sản phẩm API: {$data['total_api_products']}");
            $this->info("   ➕ Tạo mới: {$data['created']}");
            $this->info("   🔄 Cập nhật: {$data['updated']}");

            // Show data source info
            if (isset($data['data_source']) && $data['data_source'] !== 'direct_api') {
                $this->warn("   ⚠️ Nguồn dữ liệu: {$data['data_source']} (fallback)");

                if (!empty($data['limitations'])) {
                    $this->warn("   📝 Hạn chế:");
                    foreach ($data['limitations'] as $limitation) {
                        $this->line("     - {$limitation}");
                    }
                }
            }

            if (!empty($data['errors'])) {
                $this->warn("   ⚠️ Lỗi: " . count($data['errors']));
                foreach ($data['errors'] as $error) {
                    $this->line("     - {$error['item_group_id']}: {$error['error']}");
                }
            }

            return Command::SUCCESS;
        } else {
            $errorType = ErrorHandler::getErrorType($result);
            $errorMessage = ErrorHandler::getErrorMessage($result);

            if ($errorType === ErrorHandler::PERMISSION_ERROR) {
                $this->error("❌ Lỗi quyền truy cập cho {$shopName}:");
                $this->error("   {$errorMessage}");

                if (isset($result['context']['suggested_actions'])) {
                    $this->warn("   💡 Gợi ý khắc phục:");
                    foreach ($result['context']['suggested_actions'] as $action) {
                        $this->line("     - {$action}");
                    }
                }
            } else {
                $this->error("❌ Đồng bộ thất bại cho {$shopName}: {$errorMessage}");
            }

            return Command::FAILURE;
        }
    }

    /**
     * Hiển thị tổng kết
     */
    protected function displaySummary(array $results): void
    {
        $this->info('📊 TỔNG KẾT:');
        $this->info("   🏪 Shops thành công: {$results['successful_shops']}");
        $this->info("   ❌ Shops thất bại: {$results['failed_shops']}");
        $this->info("   ➕ Tổng sản phẩm tạo mới: {$results['total_created']}");
        $this->info("   🔄 Tổng sản phẩm cập nhật: {$results['total_updated']}");

        if ($results['total_errors'] > 0) {
            $this->warn("   ⚠️ Tổng lỗi: {$results['total_errors']}");
        }
    }
}
