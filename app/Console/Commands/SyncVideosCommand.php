<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\VideoSyncService;
use App\Jobs\SyncVideosJob;
use App\Helpers\ErrorHandler;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncVideosCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'tiktok:sync-videos
                            {--campaign= : Sync videos for specific campaign ID}
                            {--campaigns= : Sync videos for multiple campaign IDs (comma-separated)}
                            {--all-active : Sync videos for all active campaigns}
                            {--store= : Filter by specific store ID}
                            {--custom-posts : Only sync videos eligible for custom posts}
                            {--page-size=20 : Number of videos per page (1-50)}
                            {--max-pages=10 : Maximum number of pages to fetch}
                            {--queue : Run sync in background queue}
                            {--force : Force sync even if recently synced}
                            {--no-notify : Disable user notifications}';

    /**
     * The console command description.
     */
    protected $description = 'Đồng bộ videos từ TikTok API về database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎬 Bắt đầu đồng bộ videos từ TikTok API...');

        try {
            $campaignId = $this->option('campaign');
            $campaignIds = $this->option('campaigns');
            $allActive = $this->option('all-active');
            $useQueue = $this->option('queue');
            $notifyUsers = !$this->option('no-notify');

            // Validate options
            if (!$campaignId && !$campaignIds && !$allActive) {
                $this->error('❌ Vui lòng chỉ định một trong các options: --campaign, --campaigns, hoặc --all-active');
                return Command::FAILURE;
            }

            // Build sync options
            $syncOptions = $this->buildSyncOptions();

            // Determine sync type and execute
            if ($campaignId) {
                return $this->handleSingleCampaignSync($campaignId, $syncOptions, $useQueue, $notifyUsers);
            } elseif ($campaignIds) {
                return $this->handleMultipleCampaignsSync($campaignIds, $syncOptions, $useQueue, $notifyUsers);
            } else {
                return $this->handleAllActiveCampaignsSync($syncOptions, $useQueue, $notifyUsers);
            }

        } catch (\Exception $e) {
            $this->error("❌ Sync failed: " . $e->getMessage());
            Log::error('Videos sync command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Build sync options from command arguments
     */
    private function buildSyncOptions(): array
    {
        $options = [
            'page_size' => min(max(1, (int)$this->option('page-size')), 50),
            'max_pages' => min(max(1, (int)$this->option('max-pages')), 20),
        ];

        if ($this->option('store')) {
            $options['store_id'] = $this->option('store');
        }

        if ($this->option('custom-posts')) {
            $options['custom_posts_eligible'] = true;
        }

        return $options;
    }

    /**
     * Handle single campaign sync
     */
    private function handleSingleCampaignSync(string $campaignId, array $options, bool $useQueue, bool $notifyUsers): int
    {
        // Validate campaign exists
        $campaign = Campaign::where('campaign_id', $campaignId)->first();
        if (!$campaign) {
            $this->error("❌ Campaign không tồn tại: {$campaignId}");
            return Command::FAILURE;
        }

        $this->info("📹 Đồng bộ videos cho campaign: {$campaign->name} ({$campaignId})");

        if ($useQueue) {
            SyncVideosJob::dispatch('single', $campaignId, [], $options, $notifyUsers);
            $this->info('✅ Đã thêm job sync vào queue. Kiểm tra queue worker để theo dõi tiến trình.');
            $this->line('💡 Chạy: php artisan queue:work để xử lý job');
            return Command::SUCCESS;
        }

        return $this->executeSingleCampaignSync($campaign, $options);
    }

    /**
     * Handle multiple campaigns sync
     */
    private function handleMultipleCampaignsSync(string $campaignIds, array $options, bool $useQueue, bool $notifyUsers): int
    {
        $campaignIdArray = array_filter(array_map('trim', explode(',', $campaignIds)));

        if (empty($campaignIdArray)) {
            $this->error('❌ Danh sách campaign IDs không hợp lệ');
            return Command::FAILURE;
        }

        $this->info("📹 Đồng bộ videos cho " . count($campaignIdArray) . " campaigns");

        if ($useQueue) {
            SyncVideosJob::dispatch('multiple', null, $campaignIdArray, $options, $notifyUsers);
            $this->info('✅ Đã thêm job sync vào queue. Kiểm tra queue worker để theo dõi tiến trình.');
            $this->line('💡 Chạy: php artisan queue:work để xử lý job');
            return Command::SUCCESS;
        }

        return $this->executeMultipleCampaignsSync($campaignIdArray, $options);
    }

    /**
     * Handle all active campaigns sync
     */
    private function handleAllActiveCampaignsSync(array $options, bool $useQueue, bool $notifyUsers): int
    {
        $activeCampaigns = Campaign::where('status', 'active')
            ->whereNotNull('store_id')
            ->whereNotNull('store_authorized_bc_id')
            ->count();

        if ($activeCampaigns === 0) {
            $this->warn('⚠️  Không tìm thấy campaign nào đang active và có đầy đủ thông tin store');
            return Command::SUCCESS;
        }

        $this->info("📹 Đồng bộ videos cho tất cả {$activeCampaigns} campaigns đang active");

        if ($useQueue) {
            SyncVideosJob::dispatch('all_active', null, [], $options, $notifyUsers);
            $this->info('✅ Đã thêm job sync vào queue. Kiểm tra queue worker để theo dõi tiến trình.');
            $this->line('💡 Chạy: php artisan queue:work để xử lý job');
            return Command::SUCCESS;
        }

        return $this->executeAllActiveCampaignsSync($options);
    }

    /**
     * Execute single campaign sync directly
     */
    private function executeSingleCampaignSync(Campaign $campaign, array $options): int
    {
        $apiService = new TikTokApiService();
        $videoSyncService = new VideoSyncService($apiService);

        $this->info('📡 Đang lấy dữ liệu từ TikTok API...');

        $progressBar = $this->output->createProgressBar(3);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');

        $progressBar->setMessage('Kết nối TikTok API...');
        $progressBar->advance();

        $result = $videoSyncService->syncVideosForCampaign($campaign, $options);

        $progressBar->setMessage('Xử lý dữ liệu...');
        $progressBar->advance();

        $progressBar->setMessage('Hoàn thành!');
        $progressBar->finish();

        $this->newLine(2);

        if (ErrorHandler::isSuccess($result)) {
            $this->displaySingleCampaignResults($result['data']['stats'] ?? []);
            return Command::SUCCESS;
        } else {
            $this->error("❌ Sync thất bại: {$result['message']}");
            return Command::FAILURE;
        }
    }

    /**
     * Execute multiple campaigns sync directly
     */
    private function executeMultipleCampaignsSync(array $campaignIds, array $options): int
    {
        $apiService = new TikTokApiService();
        $videoSyncService = new VideoSyncService($apiService);

        $this->info('📡 Đang đồng bộ videos cho ' . count($campaignIds) . ' campaigns...');

        $result = $videoSyncService->syncVideosForMultipleCampaigns($campaignIds, $options);

        if (isset($result['overall_stats'])) {
            $this->displayMultipleCampaignsResults($result['overall_stats']);
            return Command::SUCCESS;
        } else {
            $this->error("❌ Sync thất bại");
            return Command::FAILURE;
        }
    }

    /**
     * Execute all active campaigns sync directly
     */
    private function executeAllActiveCampaignsSync(array $options): int
    {
        $activeCampaigns = Campaign::where('status', 'active')
            ->whereNotNull('shop_id')
            ->whereHas('shop', function ($query) {
                $query->whereNotNull('store_authorized_bc_id');
            })
            ->pluck('campaign_id')
            ->toArray();

        return $this->executeMultipleCampaignsSync($activeCampaigns, $options);
    }

    /**
     * Display results for single campaign sync
     */
    private function displaySingleCampaignResults(array $stats): void
    {
        $this->info('✅ Đồng bộ videos thành công!');
        $this->newLine();

        $this->line("📊 <comment>Kết quả:</comment>");
        $this->line("   • Tổng videos: <info>{$stats['total']}</info>");
        $this->line("   • Videos mới: <info>{$stats['created']}</info>");
        $this->line("   • Videos cập nhật: <info>{$stats['updated']}</info>");
        $this->line("   • Videos bỏ qua: <info>{$stats['skipped']}</info>");

        if ($stats['errors'] > 0) {
            $this->line("   • Lỗi: <error>{$stats['errors']}</error>");
        }
    }

    /**
     * Display results for multiple campaigns sync
     */
    private function displayMultipleCampaignsResults(array $overallStats): void
    {
        $this->info('✅ Đồng bộ videos cho nhiều campaigns thành công!');
        $this->newLine();

        $this->line("📊 <comment>Kết quả tổng hợp:</comment>");
        $this->line("   • Campaigns xử lý: <info>{$overallStats['campaigns_processed']}</info>");
        $this->line("   • Campaigns thành công: <info>{$overallStats['campaigns_success']}</info>");
        $this->line("   • Campaigns thất bại: <info>{$overallStats['campaigns_failed']}</info>");
        $this->line("   • Tổng videos: <info>{$overallStats['total_videos']}</info>");
        $this->line("   • Videos mới: <info>{$overallStats['total_created']}</info>");
        $this->line("   • Videos cập nhật: <info>{$overallStats['total_updated']}</info>");
    }
}
