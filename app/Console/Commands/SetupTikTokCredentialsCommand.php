<?php

namespace App\Console\Commands;

use App\Services\TikTok\TikTokConfigService;
use Illuminate\Console\Command;

class SetupTikTokCredentialsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:setup-credentials
                            {--interactive : Interactive setup mode}
                            {--test : Test credentials after setup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup TikTok API credentials for production use';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 TikTok API Credentials Setup');
        $this->newLine();

        if ($this->option('interactive')) {
            return $this->handleInteractiveSetup();
        }

        return $this->handleEnvMigration();
    }

    /**
     * Handle interactive credentials setup
     */
    protected function handleInteractiveSetup(): int
    {
        $this->info('📝 Interactive Credentials Setup');
        $this->newLine();

        $credentials = [
            'api.access_token' => $this->secret('Enter TikTok Access Token'),
            'api.app_id' => $this->ask('Enter TikTok App ID'),
            'api.secret' => $this->secret('Enter TikTok App Secret'),
        ];

        // Optional AI scoring setup
        if ($this->confirm('Do you want to configure AI scoring? (optional)', false)) {
            $credentials['ai_scoring.api_key'] = $this->secret('Enter Gemini API Key (for AI scoring)');
        }

        // Validate inputs
        foreach ($credentials as $key => $value) {
            if (empty($value)) {
                $this->error("❌ {$key} cannot be empty");
                return Command::FAILURE;
            }
        }

        // Save credentials
        $configService = new TikTokConfigService();

        foreach ($credentials as $key => $value) {
            $configService->set($key, $value);
        }

        $this->info('✅ Credentials saved successfully!');

        if ($this->option('test')) {
            return $this->testCredentials($configService);
        }

        return Command::SUCCESS;
    }

    /**
     * Handle migration from .env file
     */
    protected function handleEnvMigration(): int
    {
        $this->info('📁 Migrating credentials from .env file');
        $this->newLine();

        $configService = new TikTokConfigService();
        $result = $configService->migrateFromEnv();

        if (empty($result['migrated'])) {
            $this->warn('⚠️  No credentials found in .env file to migrate.');
            $this->newLine();
            $this->line('💡 Options:');
            $this->line('  1. Set credentials in .env file and run this command again');
            $this->line('  2. Use interactive mode: php artisan tiktok:setup-credentials --interactive');
            $this->line('  3. Configure via admin panel: http://localhost:8000/admin/tik-tok-settings');

            return Command::SUCCESS;
        }

        $this->info('✅ Migrated credentials:');
        foreach ($result['migrated'] as $key) {
            $this->line("   • {$key}");
        }

        if ($this->option('test')) {
            return $this->testCredentials($configService);
        }

        return Command::SUCCESS;
    }

    /**
     * Test API credentials
     */
    protected function testCredentials(TikTokConfigService $configService): int
    {
        $this->newLine();
        $this->info('🧪 Testing API connection...');

        try {
            $result = $configService->testConnection();

            if ($result['success']) {
                $this->info('✅ API connection successful!');
                $this->line('🎉 TikTok API credentials are working correctly.');
            } else {
                $this->error('❌ API connection failed:');
                $this->line($result['message']);
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error('❌ API test failed:');
            $this->line($e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
