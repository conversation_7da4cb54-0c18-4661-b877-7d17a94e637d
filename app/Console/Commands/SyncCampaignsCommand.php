<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SyncCampaignsJob;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use App\Models\AdvertiserAccount;
use App\Helpers\ErrorHandler;

class SyncCampaignsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:sync-campaigns
                            {--advertiser= : Specific advertiser ID to sync}
                            {--sessions : Also sync sessions for campaigns}
                            {--queue : Run sync in background queue}
                            {--force : Force sync even if recently synced}
                            {--no-notify : Do not send notifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Đồng bộ campaigns từ TikTok Business API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Bắt đầu đồng bộ campaigns từ TikTok API...');

        try {
            $advertiserId = $this->option('advertiser');
            $syncSessions = $this->option('sessions');
            $useQueue = $this->option('queue');
            $notifyUsers = !$this->option('no-notify');

            // Kiểm tra nếu chạy trong queue
            if ($useQueue) {
                $this->handleQueueSync($advertiserId, $syncSessions, $notifyUsers);
                return Command::SUCCESS;
            }

            // Chạy sync trực tiếp
            $this->handleDirectSync($advertiserId, $syncSessions);
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $errorResponse = ErrorHandler::handleNetworkError(
                $e,
                'Campaign Sync',
                ['command' => 'sync-campaigns']
            );

            $this->error("❌ Sync failed: " . ErrorHandler::getErrorMessage($errorResponse));
            return Command::FAILURE;
        }
    }

    /**
     * Xử lý sync trong queue
     */
    protected function handleQueueSync(?string $advertiserId, bool $syncSessions, bool $notifyUsers): void
    {
        SyncCampaignsJob::dispatch($advertiserId, $syncSessions, $notifyUsers);

        $message = $advertiserId 
            ? "✅ Đã thêm job sync campaigns cho advertiser {$advertiserId} vào queue."
            : "✅ Đã thêm job sync campaigns cho tất cả advertisers vào queue.";
            
        $this->info($message);
        $this->line('💡 Chạy: php artisan queue:work để xử lý job');
    }

    /**
     * Xử lý sync trực tiếp
     */
    protected function handleDirectSync(?string $advertiserId, bool $syncSessions): void
    {
        // Khởi tạo services
        $apiService = new TikTokApiService();
        $syncService = new TikTokSyncService($apiService);

        // Hiển thị progress bar
        $this->info('📡 Đang lấy dữ liệu từ TikTok API...');

        if ($advertiserId) {
            $this->syncForSpecificAdvertiser($apiService, $syncService, $advertiserId, $syncSessions);
        } else {
            $this->syncForAllAdvertisers($syncService);
        }
    }

    /**
     * Sync campaigns cho advertiser cụ thể
     */
    protected function syncForSpecificAdvertiser(TikTokApiService $apiService, TikTokSyncService $syncService, string $advertiserId, bool $syncSessions): void
    {
        $progressBar = $this->output->createProgressBar($syncSessions ? 6 : 4);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');

        $progressBar->setMessage("Kết nối TikTok API cho advertiser {$advertiserId}...");
        $progressBar->advance();

        $apiService->setAdvertiserId($advertiserId);

        $progressBar->setMessage('Đồng bộ campaigns...');
        $progressBar->advance();

        $result = $syncService->syncCampaigns();

        $progressBar->setMessage('Xử lý dữ liệu campaigns...');
        $progressBar->advance();

        if ($syncSessions && $result['success']) {
            $progressBar->setMessage('Đồng bộ sessions...');
            $progressBar->advance();

            $sessionsResult = $syncService->syncSessionsForAdvertiser($advertiserId);
            $result['sessions'] = $sessionsResult;

            $progressBar->setMessage('Xử lý dữ liệu sessions...');
            $progressBar->advance();
        }

        $progressBar->setMessage('Hoàn thành!');
        $progressBar->finish();

        $this->newLine(2);

        if ($result['success']) {
            $this->displaySuccessResults($result, $advertiserId);
        } else {
            $this->error("❌ Sync thất bại: {$result['error']}");
        }
    }

    /**
     * Sync campaigns cho tất cả advertisers
     */
    protected function syncForAllAdvertisers(TikTokSyncService $syncService): void
    {
        // Get eligible advertiser accounts
        $advertiserAccounts = AdvertiserAccount::active()
            ->where(function ($query) {
                $query->whereHas('shops')
                    ->orWhereHas('shopsByAdvertiserId');
            })
            ->with(['shops', 'shopsByAdvertiserId'])
            ->get();

        if ($advertiserAccounts->isEmpty()) {
            $this->warn('⚠️  Không tìm thấy advertiser accounts có shops để đồng bộ.');
            return;
        }

        $progressBar = $this->output->createProgressBar(4);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');

        $progressBar->setMessage('Kết nối TikTok API...');
        $progressBar->advance();

        $progressBar->setMessage('Đồng bộ campaigns cho tất cả advertisers...');
        $progressBar->advance();

        $result = $syncService->syncCampaignsForAllAdvertisers($advertiserAccounts);

        $progressBar->setMessage('Xử lý dữ liệu...');
        $progressBar->advance();

        $progressBar->setMessage('Hoàn thành!');
        $progressBar->finish();

        $this->newLine(2);

        if ($result['success']) {
            $this->displaySuccessResults($result);
        } else {
            $this->error("❌ Sync thất bại: {$result['error']}");
        }
    }

    /**
     * Hiển thị kết quả thành công
     */
    protected function displaySuccessResults(array $result, ?string $advertiserId = null): void
    {
        $syncedCount = $result['synced'] ?? 0;
        $createdCount = $result['created'] ?? 0;
        $updatedCount = $result['updated'] ?? 0;
        $totalAdvertisers = $result['total_advertisers'] ?? 1;
        $totalShops = $result['total_shops'] ?? 0;
        $executionTime = $result['execution_time'] ?? 0;

        $this->info('🎉 Đồng bộ campaigns thành công!');
        $this->newLine();

        $headers = ['Metric', 'Value'];
        $rows = [
            ['📊 Tổng campaigns', $syncedCount],
            ['🆕 Tạo mới', $createdCount],
            ['🔄 Cập nhật', $updatedCount],
        ];

        if (!$advertiserId) {
            $rows[] = ['👥 Advertiser accounts', $totalAdvertisers];
            $rows[] = ['🏪 Shops', $totalShops];
        }

        $rows[] = ['⏱️ Thời gian', "{$executionTime}s"];

        if (isset($result['sessions'])) {
            $sessionsResult = $result['sessions'];
            $rows[] = ['📺 Sessions synced', $sessionsResult['synced'] ?? 0];
            $rows[] = ['📺 Sessions created', $sessionsResult['created'] ?? 0];
            $rows[] = ['📺 Sessions updated', $sessionsResult['updated'] ?? 0];
        }

        $this->table($headers, $rows);
    }
}
