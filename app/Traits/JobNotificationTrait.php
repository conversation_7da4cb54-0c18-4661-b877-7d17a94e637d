<?php

namespace App\Traits;

use App\Services\JobNotificationService;
use Throwable;

trait JobNotificationTrait
{
    /**
     * Tên job để hiển thị trong notification
     * Override trong class sử dụng trait
     */
    protected string $jobDisplayName = 'Job';

    /**
     * Có gửi notification hay không
     */
    protected bool $notifyUsers = true;

    /**
     * G<PERSON>i thông báo thành công
     *
     * @param string $message
     * @param array $stats
     * @return void
     */
    protected function sendSuccessNotification(string $message, array $stats = []): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        JobNotificationService::sendSuccessNotification(
            $this->jobDisplayName,
            $message,
            $stats
        );
    }

    /**
     * G<PERSON>i thông báo lỗi
     *
     * @param string $error
     * @param array $context
     * @return void
     */
    protected function sendErrorNotification(string $error, array $context = []): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        JobNotificationService::sendErrorNotification(
            $this->jobDisplayName,
            $error,
            $context
        );
    }

    /**
     * <PERSON><PERSON>i thông báo cảnh báo
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    protected function sendWarningNotification(string $message, array $context = []): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        JobNotificationService::sendWarningNotification(
            $this->jobDisplayName,
            $message,
            $context
        );
    }

    /**
     * Gửi thông báo thông tin
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    protected function sendInfoNotification(string $message, array $context = []): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        JobNotificationService::sendInfoNotification(
            $this->jobDisplayName,
            $message,
            $context
        );
    }

    /**
     * Gửi thông báo với thống kê được format sẵn
     *
     * @param array $stats
     * @param string $customMessage
     * @return void
     */
    protected function sendStatsNotification(array $stats, string $customMessage = ''): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        $statsMessage = JobNotificationService::formatStatsMessage($stats);
        $message = $customMessage ?: "Hoàn thành: {$statsMessage}";

        $this->sendSuccessNotification($message, $stats);
    }

    /**
     * Gửi thông báo khi job failed
     *
     * @param Throwable $exception
     * @param int $attempts
     * @return void
     */
    protected function sendFailedNotification(Throwable $exception, int $attempts = 0): void
    {
        if (!$this->notifyUsers) {
            return;
        }

        $message = $attempts > 0
            ? "Job thất bại sau {$attempts} lần thử: " . $exception->getMessage()
            : "Job thất bại: " . $exception->getMessage();

        $this->sendErrorNotification($message, [
            'exception_class' => get_class($exception),
            'attempts' => $attempts,
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
