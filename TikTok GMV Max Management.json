{"app": {"name": "TikTok GMV Max Manager", "description": "Ứng dụng quản lý chiến dịch quảng cáo TikTok GMV Max", "version": "1.0.0", "timezone": "Asia/Ho_Chi_Minh", "locale": "vi"}, "database": {"models": {"Campaign": {"table": "gmv_max_campaigns", "fillable": ["campaign_id", "name", "status", "target_roi", "budget", "daily_budget", "start_date", "end_date", "advertiser_id", "shop_id", "created_at", "updated_at"], "casts": {"target_roi": "decimal:2", "budget": "decimal:2", "daily_budget": "decimal:2", "start_date": "datetime", "end_date": "datetime"}, "relationships": {"sessions": "hasMany:Session", "reports": "hasMany:CampaignReport", "shop": "belongsTo:Shop"}}, "Session": {"table": "gmv_max_sessions", "fillable": ["session_id", "campaign_id", "name", "status", "delivery_type", "budget", "start_time", "end_time", "created_at", "updated_at"], "casts": {"budget": "decimal:2", "start_time": "datetime", "end_time": "datetime"}, "relationships": {"campaign": "belongsTo:Campaign"}}, "Shop": {"table": "tiktok_shops", "fillable": ["shop_id", "name", "status", "is_eligible_gmv_max", "region", "currency", "advertiser_id", "exclusive_authorization_status", "created_at", "updated_at"], "relationships": {"campaigns": "hasMany:Campaign", "products": "hasMany:Product", "exclusiveAuthorizations": "hasMany:ExclusiveAuthorization"}}, "Product": {"table": "shop_products", "fillable": ["product_id", "shop_id", "name", "price", "status", "category", "is_occupied", "created_at", "updated_at"], "relationships": {"shop": "belongsTo:Shop"}}, "Identity": {"table": "gmv_max_identities", "fillable": ["identity_id", "name", "type", "status", "advertiser_id", "created_at", "updated_at"]}, "Video": {"table": "gmv_max_videos", "fillable": ["video_id", "campaign_id", "title", "url", "thumbnail", "duration", "status", "is_custom_anchor", "created_at", "updated_at"], "relationships": {"campaign": "belongsTo:Campaign"}}, "ExclusiveAuthorization": {"table": "exclusive_authorizations", "fillable": ["authorization_id", "shop_id", "advertiser_id", "status", "granted_at", "expires_at", "created_at", "updated_at"], "casts": {"granted_at": "datetime", "expires_at": "datetime"}, "relationships": {"shop": "belongsTo:Shop"}}, "CampaignReport": {"table": "campaign_reports", "fillable": ["report_id", "campaign_id", "report_date", "total_cost", "orders_count", "gross_revenue", "cost_per_order", "roi", "impressions", "clicks", "ctr", "conversion_rate", "created_at", "updated_at"], "casts": {"total_cost": "decimal:2", "gross_revenue": "decimal:2", "cost_per_order": "decimal:2", "roi": "decimal:2", "ctr": "decimal:2", "conversion_rate": "decimal:2", "report_date": "date"}, "relationships": {"campaign": "belongsTo:Campaign"}}}}, "filament": {"resources": {"CampaignResource": {"model": "Campaign", "navigation": {"group": "<PERSON><PERSON><PERSON><PERSON>", "icon": "heroicon-o-megaphone", "sort": 1}, "form": {"schema": [{"type": "TextInput", "name": "name", "label": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "required": true, "maxLength": 255}, {"type": "Select", "name": "shop_id", "label": "<PERSON><PERSON><PERSON> h<PERSON>", "required": true, "relationship": "shop", "getOptionLabelFromRecordUsing": "name"}, {"type": "TextInput", "name": "target_roi", "label": "Target ROI (%)", "numeric": true, "step": 0.01, "suffix": "%"}, {"type": "TextInput", "name": "budget", "label": "<PERSON><PERSON> s<PERSON>ch tổng", "numeric": true, "step": 0.01, "prefix": "₫"}, {"type": "TextInput", "name": "daily_budget", "label": "<PERSON><PERSON> s<PERSON>ch hàng ngày", "numeric": true, "step": 0.01, "prefix": "₫"}, {"type": "DateTimePicker", "name": "start_date", "label": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "required": true}, {"type": "DateTimePicker", "name": "end_date", "label": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, {"type": "Select", "name": "status", "label": "<PERSON><PERSON><PERSON><PERSON> thái", "options": {"draft": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy"}, "default": "draft"}]}, "table": {"columns": [{"name": "name", "label": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "searchable": true, "sortable": true}, {"name": "shop.name", "label": "<PERSON><PERSON><PERSON> h<PERSON>", "sortable": true}, {"name": "status", "label": "<PERSON><PERSON><PERSON><PERSON> thái", "badge": true, "colors": {"draft": "gray", "active": "success", "paused": "warning", "completed": "info", "cancelled": "danger"}}, {"name": "target_roi", "label": "Target ROI", "suffix": "%", "sortable": true}, {"name": "budget", "label": "<PERSON><PERSON>", "money": true, "sortable": true}, {"name": "start_date", "label": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "date": true, "sortable": true}], "filters": [{"name": "status", "type": "SelectFilter", "options": {"draft": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy"}}, {"name": "shop_id", "type": "SelectFilter", "relationship": "shop"}], "actions": [{"name": "view_reports", "label": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "icon": "heroicon-o-chart-bar", "color": "info"}, {"name": "sync_data", "label": "<PERSON>ồng bộ dữ liệu", "icon": "heroicon-o-arrow-path", "color": "warning"}]}, "api_endpoints": ["/gmv_max/campaign/get/", "/campaign/gmv_max/info/", "/campaign/gmv_max/create/", "/campaign/gmv_max/update/", "/gmv_max/bid/recommend/"]}, "SessionResource": {"model": "Session", "navigation": {"group": "<PERSON><PERSON><PERSON><PERSON>", "icon": "heroicon-o-play", "sort": 2}, "form": {"schema": [{"type": "Select", "name": "campaign_id", "label": "<PERSON><PERSON><PERSON>", "required": true, "relationship": "campaign", "getOptionLabelFromRecordUsing": "name"}, {"type": "TextInput", "name": "name", "label": "Tên session", "required": true, "maxLength": 255}, {"type": "Select", "name": "delivery_type", "label": "<PERSON><PERSON><PERSON> triển khai", "options": {"standard": "<PERSON><PERSON><PERSON><PERSON>", "accelerated": "<PERSON><PERSON><PERSON> tốc", "balanced": "Cân bằng"}, "default": "standard"}, {"type": "TextInput", "name": "budget", "label": "<PERSON><PERSON>", "numeric": true, "step": 0.01, "prefix": "₫"}, {"type": "DateTimePicker", "name": "start_time", "label": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "required": true}, {"type": "DateTimePicker", "name": "end_time", "label": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}]}, "api_endpoints": ["/campaign/gmv_max/session/create/", "/campaign/gmv_max/session/update/", "/campaign/gmv_max/session/list/", "/campaign/gmv_max/session/get/", "/campaign/gmv_max/session/delete/"]}, "ShopResource": {"model": "Shop", "navigation": {"group": "<PERSON><PERSON><PERSON><PERSON>", "icon": "heroicon-o-building-storefront", "sort": 1}, "form": {"schema": [{"type": "TextInput", "name": "name", "label": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "required": true}, {"type": "TextInput", "name": "shop_id", "label": "Shop ID", "required": true, "unique": true}, {"type": "Select", "name": "region", "label": "<PERSON><PERSON> v<PERSON>", "options": {"VN": "Việt Nam", "TH": "<PERSON><PERSON><PERSON><PERSON>", "MY": "Malaysia", "SG": "Singapore", "ID": "Indonesia", "PH": "Philippines"}}, {"type": "Select", "name": "currency", "label": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "options": {"VND": "Việt Nam Đồng", "USD": "US Dollar", "THB": "Thai Baht"}}, {"type": "Toggle", "name": "is_eligible_gmv_max", "label": "<PERSON><PERSON> điều kiện GMV Max"}]}, "api_endpoints": ["/gmv_max/store/list/", "/gmv_max/store/shop_ad_usage_check/"]}, "ProductResource": {"model": "Product", "navigation": {"group": "<PERSON><PERSON><PERSON><PERSON>", "icon": "heroicon-o-shopping-bag", "sort": 2}, "api_endpoints": ["/gmv_max/occupied_custom_shop_ads/list/"]}, "VideoResource": {"model": "Video", "navigation": {"group": "Nội Dung & Media", "icon": "heroicon-o-video-camera", "sort": 1}, "form": {"schema": [{"type": "Select", "name": "campaign_id", "label": "<PERSON><PERSON><PERSON>", "relationship": "campaign"}, {"type": "TextInput", "name": "title", "label": "<PERSON>i<PERSON><PERSON> đề video", "required": true}, {"type": "TextInput", "name": "url", "label": "URL Video", "url": true}, {"type": "FileUpload", "name": "thumbnail", "label": "<PERSON><PERSON><PERSON><PERSON>", "image": true, "acceptedFileTypes": ["image/jpeg", "image/png"]}, {"type": "TextInput", "name": "duration", "label": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> (giây)", "numeric": true}, {"type": "Toggle", "name": "is_custom_anchor", "label": "Custom Anchor Video"}]}, "api_endpoints": ["/gmv_max/video/get/", "/gmv_max/custom_anchor_video_list/get/"]}, "IdentityResource": {"model": "Identity", "navigation": {"group": "Nội Dung & Media", "icon": "heroicon-o-user-circle", "sort": 2}, "api_endpoints": ["/gmv_max/identity/get/"]}, "ExclusiveAuthorizationResource": {"model": "ExclusiveAuthorization", "navigation": {"group": "<PERSON><PERSON><PERSON><PERSON> & Ủy Quyền", "icon": "heroicon-o-key", "sort": 1}, "form": {"schema": [{"type": "Select", "name": "shop_id", "label": "<PERSON><PERSON><PERSON> h<PERSON>", "required": true, "relationship": "shop"}, {"type": "TextInput", "name": "advertiser_id", "label": "Advertiser ID", "required": true}, {"type": "Select", "name": "status", "label": "<PERSON><PERSON><PERSON><PERSON> thái", "options": {"pending": "Chờ xử lý", "granted": "<PERSON><PERSON> cấp", "expired": "<PERSON><PERSON> hết hạn", "revoked": "<PERSON><PERSON> thu hồi"}}, {"type": "DateTimePicker", "name": "granted_at", "label": "<PERSON><PERSON><PERSON> c<PERSON> quy<PERSON>n"}, {"type": "DateTimePicker", "name": "expires_at", "label": "<PERSON><PERSON><PERSON> h<PERSON> hạn"}]}, "api_endpoints": ["/gmv_max/exclusive_authorization/get/", "/gmv_max/exclusive_authorization/create/"]}, "ReportResource": {"model": "CampaignReport", "navigation": {"group": "Báo Cáo & Phân Tích", "icon": "heroicon-o-chart-bar", "sort": 1}, "table": {"columns": [{"name": "campaign.name", "label": "<PERSON><PERSON><PERSON>", "sortable": true}, {"name": "report_date", "label": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "date": true, "sortable": true}, {"name": "total_cost", "label": "<PERSON> phí tổng", "money": true, "sortable": true}, {"name": "orders_count", "label": "<PERSON><PERSON> đơn hàng", "numeric": true, "sortable": true}, {"name": "gross_revenue", "label": "<PERSON><PERSON>h thu", "money": true, "sortable": true}, {"name": "cost_per_order", "label": "Chi phí/Đơn", "money": true, "sortable": true}, {"name": "roi", "label": "ROI (%)", "suffix": "%", "sortable": true, "color": "success"}], "filters": [{"name": "report_date", "type": "DateFilter"}, {"name": "campaign_id", "type": "SelectFilter", "relationship": "campaign"}]}, "api_endpoints": ["/gmv_max/report/get/"]}}, "widgets": {"DashboardStatsWidget": {"type": "StatsOverviewWidget", "stats": [{"name": "total_campaigns", "label": "<PERSON><PERSON><PERSON> chi<PERSON>n d<PERSON>ch", "value": "Campaign::count()", "description": "<PERSON><PERSON><PERSON> dịch đang ho<PERSON>t động", "descriptionIcon": "heroicon-m-arrow-trending-up", "color": "success"}, {"name": "total_revenue", "label": "<PERSON><PERSON>ng doanh thu", "value": "CampaignReport::sum('gross_revenue')", "description": "<PERSON><PERSON>h thu tháng này", "descriptionIcon": "heroicon-m-banknotes", "color": "success"}, {"name": "average_roi", "label": "ROI trung bình", "value": "CampaignReport::avg('roi')", "description": "ROI trung bình tháng này", "descriptionIcon": "heroicon-m-chart-bar", "color": "warning", "suffix": "%"}, {"name": "total_orders", "label": "<PERSON><PERSON><PERSON> đơn hàng", "value": "CampaignReport::sum('orders_count')", "description": "<PERSON><PERSON><PERSON> hàng tháng này", "descriptionIcon": "heroicon-m-shopping-cart", "color": "info"}]}, "CampaignPerformanceChart": {"type": "LineChartWidget", "heading": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "data": "CampaignReport::selectRaw('DATE(report_date) as date, SUM(gross_revenue) as revenue, SUM(total_cost) as cost')->groupBy('date')->orderBy('date')->get()", "datasets": [{"label": "<PERSON><PERSON>h thu", "data": "revenue", "borderColor": "#10B981", "backgroundColor": "#10B98120"}, {"label": "Chi phí", "data": "cost", "borderColor": "#EF4444", "backgroundColor": "#EF444420"}]}, "TopPerformingCampaigns": {"type": "TableWidget", "heading": "<PERSON><PERSON><PERSON> dịch hi<PERSON>u su<PERSON>t cao", "data": "Campaign::with('reports')->orderByDesc('reports.roi')->limit(10)->get()", "columns": ["name", "target_roi", "reports.roi", "reports.gross_revenue"]}}, "pages": {"Dashboard": {"widgets": ["DashboardStatsWidget", "CampaignPerformanceChart", "TopPerformingCampaigns"]}}, "settings": {"theme": "dark", "sidebar_collapsed": false, "navigation_groups": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Nội Dung & Media", "<PERSON><PERSON><PERSON><PERSON> & Ủy Quyền", "Báo Cáo & Phân Tích"]}, "plugins": ["filament/spatie-laravel-settings-plugin", "filament/spatie-laravel-media-library-plugin", "filament/spatie-laravel-tags-plugin"]}, "integrations": {"tiktok_api": {"base_url": "https://business-api.tiktok.com", "version": "v1.3", "auth": {"type": "oauth2", "scopes": ["user_info.basic", "video.list", "business_management", "campaign_management", "reporting"]}, "endpoints": {"campaigns": "/gmv_max/campaign/", "sessions": "/campaign/gmv_max/session/", "stores": "/gmv_max/store/", "reports": "/gmv_max/report/", "identities": "/gmv_max/identity/", "videos": "/gmv_max/video/", "authorizations": "/gmv_max/exclusive_authorization/"}}, "ai_scoring": {"enabled": true, "provider": "openai", "models": ["gpt-4-turbo", "gpt-3.5-turbo"], "features": ["roi_prediction", "budget_optimization", "performance_alerts", "campaign_recommendations"]}, "automation": {"schedulers": [{"name": "sync_campaign_data", "schedule": "0 */6 * * *", "description": "<PERSON><PERSON><PERSON> bộ dữ liệu chiến dịch mỗi 6 giờ"}, {"name": "generate_daily_reports", "schedule": "0 1 * * *", "description": "Tạo báo cáo hàng ngày lúc 1:00 AM"}, {"name": "performance_alerts", "schedule": "*/30 * * * *", "description": "<PERSON><PERSON><PERSON> tra cảnh báo hiệu suất mỗi 30 phút"}], "webhooks": [{"name": "campaign_status_change", "url": "/webhooks/campaign/status", "events": ["campaign.paused", "campaign.completed", "campaign.cancelled"]}, {"name": "low_roi_alert", "url": "/webhooks/alerts/roi", "events": ["roi.below_threshold"]}]}}, "permissions": {"roles": [{"name": "admin", "label": "<PERSON><PERSON><PERSON><PERSON> trị viên", "permissions": ["*"]}, {"name": "manager", "label": "<PERSON><PERSON><PERSON><PERSON> lý", "permissions": ["campaigns.view", "campaigns.create", "campaigns.edit", "sessions.view", "sessions.create", "sessions.edit", "reports.view", "shops.view"]}, {"name": "analyst", "label": "<PERSON><PERSON> tích viên", "permissions": ["campaigns.view", "sessions.view", "reports.view", "reports.export"]}, {"name": "operator", "label": "<PERSON><PERSON><PERSON> h<PERSON>nh", "permissions": ["campaigns.view", "campaigns.edit", "sessions.view", "sessions.create", "sessions.edit"]}]}}