<?php

return [
    /*
    |--------------------------------------------------------------------------
    | TikTok Business API Configuration
    |--------------------------------------------------------------------------
    |
    | System defaults for TikTok Business API integration.
    |
    | Note: Most settings are now managed via database (tiktok_settings table).
    | These values serve as fallbacks when database settings are not available.
    | Use admin panel at /admin/tik-tok-settings to configure runtime settings.
    |
    */

    'api' => [
        'base_url' => 'https://business-api.tiktok.com',
        'version' => 'v1.3',
        // Note: access_token, app_id, secret, advertiser_id are managed via database
    ],

    /*
    |--------------------------------------------------------------------------
    | System Defaults
    |--------------------------------------------------------------------------
    |
    | These are fallback values used when database settings are not available.
    | Runtime settings are managed via tiktok_settings table.
    |
    */
    'rate_limit' => [
        'requests_per_minute' => '60',
        'delay_between_requests' => '1', // seconds
    ],

    'cache' => [
        'campaigns_ttl' => '1800', // 30 minutes
        'stores_ttl' => '3600', // 1 hour
        'reports_ttl' => '900', // 15 minutes
    ],

    'sync' => [
        'retry_attempts' => 3,
    ],

    'logging' => [
        'channel' => 'daily',
    ],

    'webhook' => [
        'enabled' => false,
        'url' => '',
        'secret' => '',
    ],
];
