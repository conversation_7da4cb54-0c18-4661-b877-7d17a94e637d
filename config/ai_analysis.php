<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AI Analysis Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for background AI analysis processing
    |
    */

    'enabled' => env('AI_ANALYSIS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Analysis Frequency
    |--------------------------------------------------------------------------
    |
    | How often to run AI analysis for campaigns
    |
    */

    'frequency' => [
        'recommendations' => env('AI_ANALYSIS_RECOMMENDATIONS_HOURS', 24),
        'roi_prediction' => env('AI_ANALYSIS_ROI_HOURS', 12),
        'budget_optimization' => env('AI_ANALYSIS_BUDGET_HOURS', 6),
    ],

    /*
    |--------------------------------------------------------------------------
    | Batch Processing
    |--------------------------------------------------------------------------
    |
    | Configuration for batch processing campaigns
    |
    */

    'batch_size' => env('AI_ANALYSIS_BATCH_SIZE', 10),
    'max_execution_time' => env('AI_ANALYSIS_MAX_TIME', 300), // 5 minutes

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for retrying failed analyses
    |
    */

    'retry' => [
        'max_attempts' => env('AI_ANALYSIS_MAX_RETRIES', 3),
        'delay_seconds' => env('AI_ANALYSIS_RETRY_DELAY', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for caching analysis results
    |
    */

    'cache' => [
        'ttl' => env('AI_ANALYSIS_CACHE_TTL', 3600), // 1 hour
        'prefix' => 'ai_analysis_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for queue processing
    |
    */

    'queue' => [
        'enabled' => env('AI_ANALYSIS_QUEUE_ENABLED', false),
        'connection' => env('AI_ANALYSIS_QUEUE_CONNECTION', 'default'),
        'queue' => env('AI_ANALYSIS_QUEUE_NAME', 'ai-analysis'),
    ],
];
